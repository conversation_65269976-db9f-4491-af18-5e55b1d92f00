"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/JournalApp.tsx":
/*!***************************************!*\
  !*** ./src/components/JournalApp.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JournalApp: () => (/* binding */ JournalApp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _TextEditor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TextEditor */ \"(app-pages-browser)/./src/components/TextEditor.tsx\");\n/* harmony import */ var _TagManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TagManager */ \"(app-pages-browser)/./src/components/TagManager.tsx\");\n/* harmony import */ var _FileUpload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FileUpload */ \"(app-pages-browser)/./src/components/FileUpload.tsx\");\n/* harmony import */ var _Settings__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Settings */ \"(app-pages-browser)/./src/components/Settings.tsx\");\n/* harmony import */ var _lib_localStorage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/localStorage */ \"(app-pages-browser)/./src/lib/localStorage.ts\");\n/* harmony import */ var _lib_testData__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/testData */ \"(app-pages-browser)/./src/lib/testData.ts\");\n/* __next_internal_client_entry_do_not_use__ JournalApp auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction JournalApp(param) {\n    let { className = '' } = param;\n    _s();\n    const [currentEntry, setCurrentEntry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [entries, setEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUpload, setShowUpload] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Start closed on mobile\n    const [zoom, setZoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const storage = (0,_lib_localStorage__WEBPACK_IMPORTED_MODULE_6__.getLocalStorage)();\n    // Update available tags from all entries\n    const updateAvailableTags = (entriesList)=>{\n        const allTags = new Set();\n        entriesList.forEach((entry)=>{\n            entry.tags.forEach((tag)=>allTags.add(tag));\n        });\n        storage.cacheTags([\n            ...allTags\n        ]);\n    };\n    // Detect mobile and load cached entries on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"JournalApp.useEffect\": ()=>{\n            const checkMobile = {\n                \"JournalApp.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                    setSidebarOpen(window.innerWidth >= 768); // Open sidebar on desktop by default\n                }\n            }[\"JournalApp.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            const cached = storage.getCachedEntries();\n            if (cached) {\n                setEntries(cached.entries);\n                updateAvailableTags(cached.entries);\n            }\n            return ({\n                \"JournalApp.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"JournalApp.useEffect\"];\n        }\n    }[\"JournalApp.useEffect\"], []);\n    // Filter entries based on search and tags\n    const filteredEntries = entries.filter((entry)=>{\n        const matchesSearch = !searchQuery || entry.title.toLowerCase().includes(searchQuery.toLowerCase()) || entry.content.toLowerCase().includes(searchQuery.toLowerCase()) || entry.tags.some((tag)=>tag.toLowerCase().includes(searchQuery.toLowerCase()));\n        const matchesTags = selectedTags.length === 0 || selectedTags.every((tag)=>entry.tags.includes(tag));\n        return matchesSearch && matchesTags;\n    });\n    const createNewEntry = ()=>{\n        console.log('createNewEntry clicked!');\n        const newEntry = {\n            id: \"entry_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n            title: '',\n            content: '',\n            tags: [],\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n            word_count: 0\n        };\n        setCurrentEntry(newEntry);\n        setEntries((prev)=>[\n                newEntry,\n                ...prev\n            ]);\n        storage.cacheEntry(newEntry);\n        // Close sidebar on mobile when creating new entry\n        if (isMobile) {\n            setSidebarOpen(false);\n        }\n    };\n    const saveEntry = (data)=>{\n        if (!currentEntry) return;\n        const updatedEntry = {\n            ...currentEntry,\n            title: data.title || 'Untitled Entry',\n            content: data.content,\n            tags: data.tags,\n            updated_at: new Date().toISOString(),\n            word_count: data.content.trim().split(/\\s+/).filter((word)=>word.length > 0).length\n        };\n        setCurrentEntry(updatedEntry);\n        const newEntries = entries.map((entry)=>entry.id === updatedEntry.id ? updatedEntry : entry);\n        setEntries(newEntries);\n        storage.cacheEntry(updatedEntry);\n        // Update available tags from all entries\n        updateAvailableTags(newEntries);\n    };\n    const selectEntry = (entry)=>{\n        setCurrentEntry(entry);\n        // Close sidebar on mobile when selecting entry\n        if (isMobile) {\n            setSidebarOpen(false);\n        }\n    };\n    const deleteEntry = (entryId)=>{\n        setEntries((prev)=>prev.filter((entry)=>entry.id !== entryId));\n        storage.removeCachedEntry(entryId);\n        if ((currentEntry === null || currentEntry === void 0 ? void 0 : currentEntry.id) === entryId) {\n            setCurrentEntry(null);\n        }\n    };\n    const handleSearch = (query, tags)=>{\n        setSearchQuery(query);\n        setSelectedTags(tags);\n    };\n    const handleFileImport = (file)=>{\n        const newEntry = {\n            id: crypto.randomUUID(),\n            title: file.title,\n            content: file.content,\n            tags: [],\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n            word_count: file.content.split(/\\s+/).filter((word)=>word.length > 0).length\n        };\n        const newEntries = [\n            newEntry,\n            ...entries\n        ];\n        setEntries(newEntries);\n        storage.cacheEntry(newEntry);\n        setCurrentEntry(newEntry);\n        setShowUpload(false);\n        updateAvailableTags(newEntries);\n    };\n    const adjustZoom = (delta)=>{\n        const newZoom = Math.max(50, Math.min(200, zoom + delta));\n        setZoom(newZoom);\n    };\n    const loadSampleEntries = ()=>{\n        const sampleEntries = (0,_lib_testData__WEBPACK_IMPORTED_MODULE_7__.loadSampleData)();\n        if (sampleEntries.length > 0) {\n            setEntries(sampleEntries);\n            updateAvailableTags(sampleEntries);\n        }\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', {\n            month: 'short',\n            day: 'numeric',\n            year: 'numeric'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-50 dark:bg-gray-900 \".concat(className),\n        children: [\n            isMobile && sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-40\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 194,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n        \".concat(sidebarOpen ? isMobile ? 'w-80' : 'w-80' : 'w-0', \"\\n        \").concat(isMobile ? 'fixed left-0 top-0 h-full z-50' : 'relative', \"\\n        transition-all duration-300 overflow-hidden\\n        bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700\\n        flex flex-col\\n      \"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 md:p-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3 md:mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-lg md:text-xl font-bold text-gray-900 dark:text-gray-100\",\n                                        children: \"AllJournal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 13\n                                    }, this),\n                                    !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>adjustZoom(-10),\n                                                className: \"p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300\",\n                                                title: \"Zoom Out\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                children: [\n                                                    zoom,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>adjustZoom(10),\n                                                className: \"p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300\",\n                                                title: \"Zoom In\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: createNewEntry,\n                                        className: \"flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm md:text-base\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: isMobile ? 'New' : 'New Entry'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowUpload(true),\n                                        className: \"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                        title: \"Upload Files\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowSettings(true),\n                                        className: \"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                        title: \"Settings\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 md:p-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TagManager__WEBPACK_IMPORTED_MODULE_3__.TagManager, {\n                            selectedTags: selectedTags,\n                            onTagsChange: setSelectedTags,\n                            onSearch: handleSearch\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 md:p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                        children: [\n                                            \"Entries (\",\n                                            filteredEntries.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        filteredEntries.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                onClick: ()=>selectEntry(entry),\n                                                className: \"\\n                    p-3 rounded-lg cursor-pointer transition-colors\\n                    \".concat((currentEntry === null || currentEntry === void 0 ? void 0 : currentEntry.id) === entry.id ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' : 'hover:bg-gray-50 dark:hover:bg-gray-700', \"\\n                  \"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 dark:text-gray-100 truncate text-sm md:text-base\",\n                                                        children: entry.title || 'Untitled Entry'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs md:text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2\",\n                                                        children: entry.content || 'No content'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mt-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: formatDate(entry.updated_at)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: [\n                                                                    entry.word_count,\n                                                                    \" words\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    entry.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mt-2\",\n                                                        children: [\n                                                            entry.tags.slice(0, isMobile ? 2 : 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs px-2 py-1 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded\",\n                                                                    children: [\n                                                                        \"#\",\n                                                                        tag\n                                                                    ]\n                                                                }, tag, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 25\n                                                                }, this)),\n                                                            entry.tags.length > (isMobile ? 2 : 3) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: [\n                                                                    \"+\",\n                                                                    entry.tags.length - (isMobile ? 2 : 3)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, entry.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this)),\n                                        filteredEntries.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-12 h-12 mx-auto text-gray-400 mb-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 dark:text-gray-400\",\n                                                    children: entries.length === 0 ? 'No entries yet' : 'No entries match your search'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 19\n                                                }, this),\n                                                entries.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: createNewEntry,\n                                                    className: \"mt-2 text-blue-600 dark:text-blue-400 hover:underline\",\n                                                    children: \"Create your first entry\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col \".concat(isMobile && sidebarOpen ? 'pointer-events-none' : ''),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-3 md:p-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            console.log('Menu button clicked!', sidebarOpen);\n                                            setSidebarOpen(!sidebarOpen);\n                                        },\n                                        className: \"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 13\n                                    }, this),\n                                    isMobile && currentEntry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-medium text-gray-900 dark:text-gray-100 truncate\",\n                                        children: currentEntry.title || 'Untitled Entry'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, this),\n                            currentEntry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 md:space-x-4\",\n                                children: [\n                                    !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400 hidden sm:block\",\n                                        children: [\n                                            \"Last saved: \",\n                                            formatDate(currentEntry.updated_at)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>deleteEntry(currentEntry.id),\n                                        className: \"text-sm text-red-600 dark:text-red-400 hover:underline\",\n                                        children: isMobile ? 'Delete' : 'Delete Entry'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-2 md:p-0\",\n                        style: {\n                            zoom: \"\".concat(zoom, \"%\")\n                        },\n                        children: currentEntry ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TextEditor__WEBPACK_IMPORTED_MODULE_2__.TextEditor, {\n                            entryId: currentEntry.id,\n                            initialTitle: currentEntry.title,\n                            initialContent: currentEntry.content,\n                            initialTags: currentEntry.tags,\n                            onSave: saveEntry,\n                            onAutoSave: saveEntry,\n                            className: \"h-full\"\n                        }, currentEntry.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-full p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center max-w-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-12 h-12 md:w-16 md:h-16 mx-auto text-gray-400 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg md:text-xl font-medium text-gray-900 dark:text-gray-100 mb-2\",\n                                        children: \"Welcome to AllJournal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm md:text-base text-gray-600 dark:text-gray-400 mb-6\",\n                                        children: isMobile ? \"Tap the menu to browse entries or create a new one to start writing.\" : \"Select an entry from the sidebar or create a new one to start writing.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: createNewEntry,\n                                                className: \"flex items-center justify-center space-x-2 w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Create New Entry\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 19\n                                            }, this),\n                                            isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSidebarOpen(true),\n                                                className: \"flex items-center justify-center space-x-2 w-full px-4 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Browse Entries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 21\n                                            }, this),\n                                            entries.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: loadSampleEntries,\n                                                className: \"flex items-center justify-center space-x-2 w-full px-4 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Load Sample Entries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 351,\n                columnNumber: 7\n            }, this),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Settings__WEBPACK_IMPORTED_MODULE_5__.Settings, {\n                isOpen: showSettings,\n                onClose: ()=>setShowSettings(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 453,\n                columnNumber: 9\n            }, this),\n            showUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-gray-100\",\n                                    children: \"Import Files\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowUpload(false),\n                                    className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FileUpload__WEBPACK_IMPORTED_MODULE_4__.FileUpload, {\n                                onFilesProcessed: ()=>{},\n                                onImport: handleFileImport\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                    lineNumber: 461,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 460,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n        lineNumber: 191,\n        columnNumber: 5\n    }, this);\n}\n_s(JournalApp, \"jQW7sy2PZD0LrknvYp9gYrVxNR4=\");\n_c = JournalApp;\nvar _c;\n$RefreshReg$(_c, \"JournalApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/JournalApp.tsx\n"));

/***/ })

});