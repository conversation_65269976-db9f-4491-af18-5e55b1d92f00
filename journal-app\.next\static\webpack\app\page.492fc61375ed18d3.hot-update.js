"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/JournalApp.tsx":
/*!***************************************!*\
  !*** ./src/components/JournalApp.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JournalApp: () => (/* binding */ JournalApp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _lib_localStorage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/localStorage */ \"(app-pages-browser)/./src/lib/localStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ JournalApp auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nfunction JournalApp(param) {\n    let { className = '' } = param;\n    _s();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentEntry, setCurrentEntry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [entries, setEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUpload, setShowUpload] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Memoize storage to prevent recreation on every render\n    const storage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"JournalApp.useMemo[storage]\": ()=>(0,_lib_localStorage__WEBPACK_IMPORTED_MODULE_2__.getLocalStorage)()\n    }[\"JournalApp.useMemo[storage]\"], []);\n    // Load cached entries on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"JournalApp.useEffect\": ()=>{\n            const cached = storage.getCachedEntries();\n            if (cached) {\n                setEntries(cached.entries);\n            }\n        }\n    }[\"JournalApp.useEffect\"], [\n        storage\n    ]);\n    const createNewEntry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"JournalApp.useCallback[createNewEntry]\": ()=>{\n            console.log('createNewEntry called!');\n            const newEntry = {\n                id: \"entry_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n                title: '',\n                content: '',\n                tags: [],\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString(),\n                word_count: 0\n            };\n            setCurrentEntry(newEntry);\n            setEntries({\n                \"JournalApp.useCallback[createNewEntry]\": (prev)=>[\n                        newEntry,\n                        ...prev\n                    ]\n            }[\"JournalApp.useCallback[createNewEntry]\"]);\n            storage.cacheEntry(newEntry);\n        }\n    }[\"JournalApp.useCallback[createNewEntry]\"], [\n        storage\n    ]);\n    const selectEntry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"JournalApp.useCallback[selectEntry]\": (entry)=>{\n            setCurrentEntry(entry);\n        }\n    }[\"JournalApp.useCallback[selectEntry]\"], []);\n    const saveEntry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"JournalApp.useCallback[saveEntry]\": (data)=>{\n            if (!currentEntry) return;\n            const updatedEntry = {\n                ...currentEntry,\n                title: data.title || 'Untitled Entry',\n                content: data.content,\n                tags: data.tags,\n                updated_at: new Date().toISOString(),\n                word_count: data.content.trim().split(/\\s+/).filter({\n                    \"JournalApp.useCallback[saveEntry]\": (word)=>word.length > 0\n                }[\"JournalApp.useCallback[saveEntry]\"]).length\n            };\n            setCurrentEntry(updatedEntry);\n            const newEntries = entries.map({\n                \"JournalApp.useCallback[saveEntry].newEntries\": (entry)=>entry.id === updatedEntry.id ? updatedEntry : entry\n            }[\"JournalApp.useCallback[saveEntry].newEntries\"]);\n            setEntries(newEntries);\n            storage.cacheEntry(updatedEntry);\n        }\n    }[\"JournalApp.useCallback[saveEntry]\"], [\n        currentEntry,\n        entries,\n        storage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(sidebarOpen ? 'w-80' : 'w-0', \" transition-all duration-300 overflow-hidden bg-white border-r border-gray-200 flex flex-col\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold text-gray-900\",\n                                children: \"AllJournal\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2 mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: createNewEntry,\n                                        className: \"flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"New Entry\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowUpload(true),\n                                        className: \"p-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                        title: \"Upload Files\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowSettings(true),\n                                        className: \"p-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                        title: \"Settings\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-gray-700 mb-3\",\n                                    children: [\n                                        \"Entries (\",\n                                        entries.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        entries.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                onClick: ()=>selectEntry(entry),\n                                                className: \"\\n                    p-3 rounded-lg cursor-pointer transition-colors\\n                    \".concat((currentEntry === null || currentEntry === void 0 ? void 0 : currentEntry.id) === entry.id ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50', \"\\n                  \"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 truncate\",\n                                                        children: entry.title || 'Untitled Entry'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mt-1 line-clamp-2\",\n                                                        children: entry.content || 'No content'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mt-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: new Date(entry.updated_at).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                                lineNumber: 134,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    entry.word_count,\n                                                                    \" words\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, entry.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this)),\n                                        entries.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-12 h-12 mx-auto text-gray-400 mb-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500\",\n                                                    children: \"No entries yet\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: createNewEntry,\n                                                    className: \"mt-2 text-blue-600 hover:underline\",\n                                                    children: \"Create your first entry\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 bg-white border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                console.log('Menu button clicked!', sidebarOpen);\n                                setSidebarOpen(!sidebarOpen);\n                            },\n                            className: \"p-2 text-gray-600 hover:text-gray-800 rounded-lg hover:bg-gray-100 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-4\",\n                        children: currentEntry ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleEditor, {\n                            entry: currentEntry,\n                            onSave: saveEntry\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-16 h-16 mx-auto text-gray-400 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-medium text-gray-900 mb-2\",\n                                        children: \"Welcome to AllJournal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-6\",\n                                        children: \"Create a new entry to start writing.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: createNewEntry,\n                                        className: \"flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Create New Entry\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n_s(JournalApp, \"cgvuPssTS3P0fLT65WjSbE/pdxM=\");\n_c = JournalApp;\nfunction SimpleEditor(param) {\n    let { entry, onSave } = param;\n    _s1();\n    const [title, setTitle] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(entry.title);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(entry.content);\n    // Auto-save with debounce\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"SimpleEditor.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"SimpleEditor.useEffect.timer\": ()=>{\n                    if (title !== entry.title || content !== entry.content) {\n                        onSave({\n                            title,\n                            content,\n                            tags: entry.tags\n                        });\n                    }\n                }\n            }[\"SimpleEditor.useEffect.timer\"], 1000);\n            return ({\n                \"SimpleEditor.useEffect\": ()=>clearTimeout(timer)\n            })[\"SimpleEditor.useEffect\"];\n        }\n    }[\"SimpleEditor.useEffect\"], [\n        title,\n        content,\n        entry.title,\n        entry.content,\n        entry.tags,\n        onSave\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    value: title,\n                    onChange: (e)=>setTitle(e.target.value),\n                    placeholder: \"Enter title...\",\n                    className: \"w-full text-2xl font-bold bg-transparent border-none outline-none text-gray-900 placeholder-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    value: content,\n                    onChange: (e)=>setContent(e.target.value),\n                    placeholder: \"Start writing your journal entry...\",\n                    className: \"w-full h-full bg-transparent border-none outline-none resize-none text-gray-900 placeholder-gray-500 leading-relaxed\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200 text-sm text-gray-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"Words: \",\n                                content.trim().split(/\\s+/).filter((word)=>word.length > 0).length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"Characters: \",\n                                content.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n        lineNumber: 227,\n        columnNumber: 5\n    }, this);\n}\n_s1(SimpleEditor, \"1URyN+jVAv4ErmCp+sIa5PA1j7M=\");\n_c1 = SimpleEditor;\nvar _c, _c1;\n$RefreshReg$(_c, \"JournalApp\");\n$RefreshReg$(_c1, \"SimpleEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/JournalApp.tsx\n"));

/***/ })

});