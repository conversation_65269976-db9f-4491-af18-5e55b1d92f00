/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/xmlbuilder";
exports.ids = ["vendor-chunks/xmlbuilder"];
exports.modules = {

/***/ "(ssr)/./node_modules/xmlbuilder/lib/Utility.js":
/*!************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/Utility.js ***!
  \************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var assign, getValue, isArray, isEmpty, isFunction, isObject, isPlainObject,\n    slice = [].slice,\n    hasProp = {}.hasOwnProperty;\n\n  assign = function() {\n    var i, key, len, source, sources, target;\n    target = arguments[0], sources = 2 <= arguments.length ? slice.call(arguments, 1) : [];\n    if (isFunction(Object.assign)) {\n      Object.assign.apply(null, arguments);\n    } else {\n      for (i = 0, len = sources.length; i < len; i++) {\n        source = sources[i];\n        if (source != null) {\n          for (key in source) {\n            if (!hasProp.call(source, key)) continue;\n            target[key] = source[key];\n          }\n        }\n      }\n    }\n    return target;\n  };\n\n  isFunction = function(val) {\n    return !!val && Object.prototype.toString.call(val) === '[object Function]';\n  };\n\n  isObject = function(val) {\n    var ref;\n    return !!val && ((ref = typeof val) === 'function' || ref === 'object');\n  };\n\n  isArray = function(val) {\n    if (isFunction(Array.isArray)) {\n      return Array.isArray(val);\n    } else {\n      return Object.prototype.toString.call(val) === '[object Array]';\n    }\n  };\n\n  isEmpty = function(val) {\n    var key;\n    if (isArray(val)) {\n      return !val.length;\n    } else {\n      for (key in val) {\n        if (!hasProp.call(val, key)) continue;\n        return false;\n      }\n      return true;\n    }\n  };\n\n  isPlainObject = function(val) {\n    var ctor, proto;\n    return isObject(val) && (proto = Object.getPrototypeOf(val)) && (ctor = proto.constructor) && (typeof ctor === 'function') && (ctor instanceof ctor) && (Function.prototype.toString.call(ctor) === Function.prototype.toString.call(Object));\n  };\n\n  getValue = function(obj) {\n    if (isFunction(obj.valueOf)) {\n      return obj.valueOf();\n    } else {\n      return obj;\n    }\n  };\n\n  module.exports.assign = assign;\n\n  module.exports.isFunction = isFunction;\n\n  module.exports.isObject = isObject;\n\n  module.exports.isArray = isArray;\n\n  module.exports.isEmpty = isEmpty;\n\n  module.exports.isPlainObject = isPlainObject;\n\n  module.exports.getValue = getValue;\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/Utility.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLAttribute.js":
/*!*****************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLAttribute.js ***!
  \*****************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLAttribute;\n\n  module.exports = XMLAttribute = (function() {\n    function XMLAttribute(parent, name, value) {\n      this.options = parent.options;\n      this.stringify = parent.stringify;\n      this.parent = parent;\n      if (name == null) {\n        throw new Error(\"Missing attribute name. \" + this.debugInfo(name));\n      }\n      if (value == null) {\n        throw new Error(\"Missing attribute value. \" + this.debugInfo(name));\n      }\n      this.name = this.stringify.attName(name);\n      this.value = this.stringify.attValue(value);\n    }\n\n    XMLAttribute.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLAttribute.prototype.toString = function(options) {\n      return this.options.writer.set(options).attribute(this);\n    };\n\n    XMLAttribute.prototype.debugInfo = function(name) {\n      name = name || this.name;\n      if (name == null) {\n        return \"parent: <\" + this.parent.name + \">\";\n      } else {\n        return \"attribute: {\" + name + \"}, parent: <\" + this.parent.name + \">\";\n      }\n    };\n\n    return XMLAttribute;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLAttribute.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLCData.js":
/*!*************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLCData.js ***!
  \*************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLCData, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLCData = (function(superClass) {\n    extend(XMLCData, superClass);\n\n    function XMLCData(parent, text) {\n      XMLCData.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing CDATA text. \" + this.debugInfo());\n      }\n      this.text = this.stringify.cdata(text);\n    }\n\n    XMLCData.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLCData.prototype.toString = function(options) {\n      return this.options.writer.set(options).cdata(this);\n    };\n\n    return XMLCData;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLCData.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLComment.js":
/*!***************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLComment.js ***!
  \***************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLComment, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLComment = (function(superClass) {\n    extend(XMLComment, superClass);\n\n    function XMLComment(parent, text) {\n      XMLComment.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing comment text. \" + this.debugInfo());\n      }\n      this.text = this.stringify.comment(text);\n    }\n\n    XMLComment.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLComment.prototype.toString = function(options) {\n      return this.options.writer.set(options).comment(this);\n    };\n\n    return XMLComment;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLComment.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLDTDAttList.js":
/*!******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDTDAttList.js ***!
  \******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDTDAttList, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLDTDAttList = (function(superClass) {\n    extend(XMLDTDAttList, superClass);\n\n    function XMLDTDAttList(parent, elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      XMLDTDAttList.__super__.constructor.call(this, parent);\n      if (elementName == null) {\n        throw new Error(\"Missing DTD element name. \" + this.debugInfo());\n      }\n      if (attributeName == null) {\n        throw new Error(\"Missing DTD attribute name. \" + this.debugInfo(elementName));\n      }\n      if (!attributeType) {\n        throw new Error(\"Missing DTD attribute type. \" + this.debugInfo(elementName));\n      }\n      if (!defaultValueType) {\n        throw new Error(\"Missing DTD attribute default. \" + this.debugInfo(elementName));\n      }\n      if (defaultValueType.indexOf('#') !== 0) {\n        defaultValueType = '#' + defaultValueType;\n      }\n      if (!defaultValueType.match(/^(#REQUIRED|#IMPLIED|#FIXED|#DEFAULT)$/)) {\n        throw new Error(\"Invalid default value type; expected: #REQUIRED, #IMPLIED, #FIXED or #DEFAULT. \" + this.debugInfo(elementName));\n      }\n      if (defaultValue && !defaultValueType.match(/^(#FIXED|#DEFAULT)$/)) {\n        throw new Error(\"Default value only applies to #FIXED or #DEFAULT. \" + this.debugInfo(elementName));\n      }\n      this.elementName = this.stringify.eleName(elementName);\n      this.attributeName = this.stringify.attName(attributeName);\n      this.attributeType = this.stringify.dtdAttType(attributeType);\n      this.defaultValue = this.stringify.dtdAttDefault(defaultValue);\n      this.defaultValueType = defaultValueType;\n    }\n\n    XMLDTDAttList.prototype.toString = function(options) {\n      return this.options.writer.set(options).dtdAttList(this);\n    };\n\n    return XMLDTDAttList;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvWE1MRFREQXR0TGlzdC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUMsMEJBQTBCLDJEQUEyRCxrQkFBa0IsNEJBQTRCLG1DQUFtQyw4QkFBOEIsb0NBQW9DLGVBQWU7QUFDOVIsZ0JBQWdCOztBQUVoQixZQUFZLG1CQUFPLENBQUMsaUVBQVc7O0FBRS9CO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscURBQXFEO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQSxHQUFHOztBQUVILENBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xceHlpZW5cXE9uZURyaXZlXFxEZXNrdG9wXFxBbGxKb3VybmFsXFxqb3VybmFsLWFwcFxcbm9kZV9tb2R1bGVzXFx4bWxidWlsZGVyXFxsaWJcXFhNTERUREF0dExpc3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gR2VuZXJhdGVkIGJ5IENvZmZlZVNjcmlwdCAxLjEyLjdcbihmdW5jdGlvbigpIHtcbiAgdmFyIFhNTERUREF0dExpc3QsIFhNTE5vZGUsXG4gICAgZXh0ZW5kID0gZnVuY3Rpb24oY2hpbGQsIHBhcmVudCkgeyBmb3IgKHZhciBrZXkgaW4gcGFyZW50KSB7IGlmIChoYXNQcm9wLmNhbGwocGFyZW50LCBrZXkpKSBjaGlsZFtrZXldID0gcGFyZW50W2tleV07IH0gZnVuY3Rpb24gY3RvcigpIHsgdGhpcy5jb25zdHJ1Y3RvciA9IGNoaWxkOyB9IGN0b3IucHJvdG90eXBlID0gcGFyZW50LnByb3RvdHlwZTsgY2hpbGQucHJvdG90eXBlID0gbmV3IGN0b3IoKTsgY2hpbGQuX19zdXBlcl9fID0gcGFyZW50LnByb3RvdHlwZTsgcmV0dXJuIGNoaWxkOyB9LFxuICAgIGhhc1Byb3AgPSB7fS5oYXNPd25Qcm9wZXJ0eTtcblxuICBYTUxOb2RlID0gcmVxdWlyZSgnLi9YTUxOb2RlJyk7XG5cbiAgbW9kdWxlLmV4cG9ydHMgPSBYTUxEVERBdHRMaXN0ID0gKGZ1bmN0aW9uKHN1cGVyQ2xhc3MpIHtcbiAgICBleHRlbmQoWE1MRFREQXR0TGlzdCwgc3VwZXJDbGFzcyk7XG5cbiAgICBmdW5jdGlvbiBYTUxEVERBdHRMaXN0KHBhcmVudCwgZWxlbWVudE5hbWUsIGF0dHJpYnV0ZU5hbWUsIGF0dHJpYnV0ZVR5cGUsIGRlZmF1bHRWYWx1ZVR5cGUsIGRlZmF1bHRWYWx1ZSkge1xuICAgICAgWE1MRFREQXR0TGlzdC5fX3N1cGVyX18uY29uc3RydWN0b3IuY2FsbCh0aGlzLCBwYXJlbnQpO1xuICAgICAgaWYgKGVsZW1lbnROYW1lID09IG51bGwpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiTWlzc2luZyBEVEQgZWxlbWVudCBuYW1lLiBcIiArIHRoaXMuZGVidWdJbmZvKCkpO1xuICAgICAgfVxuICAgICAgaWYgKGF0dHJpYnV0ZU5hbWUgPT0gbnVsbCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJNaXNzaW5nIERURCBhdHRyaWJ1dGUgbmFtZS4gXCIgKyB0aGlzLmRlYnVnSW5mbyhlbGVtZW50TmFtZSkpO1xuICAgICAgfVxuICAgICAgaWYgKCFhdHRyaWJ1dGVUeXBlKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIk1pc3NpbmcgRFREIGF0dHJpYnV0ZSB0eXBlLiBcIiArIHRoaXMuZGVidWdJbmZvKGVsZW1lbnROYW1lKSk7XG4gICAgICB9XG4gICAgICBpZiAoIWRlZmF1bHRWYWx1ZVR5cGUpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiTWlzc2luZyBEVEQgYXR0cmlidXRlIGRlZmF1bHQuIFwiICsgdGhpcy5kZWJ1Z0luZm8oZWxlbWVudE5hbWUpKTtcbiAgICAgIH1cbiAgICAgIGlmIChkZWZhdWx0VmFsdWVUeXBlLmluZGV4T2YoJyMnKSAhPT0gMCkge1xuICAgICAgICBkZWZhdWx0VmFsdWVUeXBlID0gJyMnICsgZGVmYXVsdFZhbHVlVHlwZTtcbiAgICAgIH1cbiAgICAgIGlmICghZGVmYXVsdFZhbHVlVHlwZS5tYXRjaCgvXigjUkVRVUlSRUR8I0lNUExJRUR8I0ZJWEVEfCNERUZBVUxUKSQvKSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJJbnZhbGlkIGRlZmF1bHQgdmFsdWUgdHlwZTsgZXhwZWN0ZWQ6ICNSRVFVSVJFRCwgI0lNUExJRUQsICNGSVhFRCBvciAjREVGQVVMVC4gXCIgKyB0aGlzLmRlYnVnSW5mbyhlbGVtZW50TmFtZSkpO1xuICAgICAgfVxuICAgICAgaWYgKGRlZmF1bHRWYWx1ZSAmJiAhZGVmYXVsdFZhbHVlVHlwZS5tYXRjaCgvXigjRklYRUR8I0RFRkFVTFQpJC8pKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIkRlZmF1bHQgdmFsdWUgb25seSBhcHBsaWVzIHRvICNGSVhFRCBvciAjREVGQVVMVC4gXCIgKyB0aGlzLmRlYnVnSW5mbyhlbGVtZW50TmFtZSkpO1xuICAgICAgfVxuICAgICAgdGhpcy5lbGVtZW50TmFtZSA9IHRoaXMuc3RyaW5naWZ5LmVsZU5hbWUoZWxlbWVudE5hbWUpO1xuICAgICAgdGhpcy5hdHRyaWJ1dGVOYW1lID0gdGhpcy5zdHJpbmdpZnkuYXR0TmFtZShhdHRyaWJ1dGVOYW1lKTtcbiAgICAgIHRoaXMuYXR0cmlidXRlVHlwZSA9IHRoaXMuc3RyaW5naWZ5LmR0ZEF0dFR5cGUoYXR0cmlidXRlVHlwZSk7XG4gICAgICB0aGlzLmRlZmF1bHRWYWx1ZSA9IHRoaXMuc3RyaW5naWZ5LmR0ZEF0dERlZmF1bHQoZGVmYXVsdFZhbHVlKTtcbiAgICAgIHRoaXMuZGVmYXVsdFZhbHVlVHlwZSA9IGRlZmF1bHRWYWx1ZVR5cGU7XG4gICAgfVxuXG4gICAgWE1MRFREQXR0TGlzdC5wcm90b3R5cGUudG9TdHJpbmcgPSBmdW5jdGlvbihvcHRpb25zKSB7XG4gICAgICByZXR1cm4gdGhpcy5vcHRpb25zLndyaXRlci5zZXQob3B0aW9ucykuZHRkQXR0TGlzdCh0aGlzKTtcbiAgICB9O1xuXG4gICAgcmV0dXJuIFhNTERUREF0dExpc3Q7XG5cbiAgfSkoWE1MTm9kZSk7XG5cbn0pLmNhbGwodGhpcyk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLDTDAttList.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLDTDElement.js":
/*!******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDTDElement.js ***!
  \******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDTDElement, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLDTDElement = (function(superClass) {\n    extend(XMLDTDElement, superClass);\n\n    function XMLDTDElement(parent, name, value) {\n      XMLDTDElement.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD element name. \" + this.debugInfo());\n      }\n      if (!value) {\n        value = '(#PCDATA)';\n      }\n      if (Array.isArray(value)) {\n        value = '(' + value.join(',') + ')';\n      }\n      this.name = this.stringify.eleName(name);\n      this.value = this.stringify.dtdElementValue(value);\n    }\n\n    XMLDTDElement.prototype.toString = function(options) {\n      return this.options.writer.set(options).dtdElement(this);\n    };\n\n    return XMLDTDElement;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLDTDElement.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLDTDEntity.js":
/*!*****************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDTDEntity.js ***!
  \*****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDTDEntity, XMLNode, isObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isObject = (__webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/xmlbuilder/lib/Utility.js\").isObject);\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLDTDEntity = (function(superClass) {\n    extend(XMLDTDEntity, superClass);\n\n    function XMLDTDEntity(parent, pe, name, value) {\n      XMLDTDEntity.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD entity name. \" + this.debugInfo(name));\n      }\n      if (value == null) {\n        throw new Error(\"Missing DTD entity value. \" + this.debugInfo(name));\n      }\n      this.pe = !!pe;\n      this.name = this.stringify.eleName(name);\n      if (!isObject(value)) {\n        this.value = this.stringify.dtdEntityValue(value);\n      } else {\n        if (!value.pubID && !value.sysID) {\n          throw new Error(\"Public and/or system identifiers are required for an external entity. \" + this.debugInfo(name));\n        }\n        if (value.pubID && !value.sysID) {\n          throw new Error(\"System identifier is required for a public external entity. \" + this.debugInfo(name));\n        }\n        if (value.pubID != null) {\n          this.pubID = this.stringify.dtdPubID(value.pubID);\n        }\n        if (value.sysID != null) {\n          this.sysID = this.stringify.dtdSysID(value.sysID);\n        }\n        if (value.nData != null) {\n          this.nData = this.stringify.dtdNData(value.nData);\n        }\n        if (this.pe && this.nData) {\n          throw new Error(\"Notation declaration is not allowed in a parameter entity. \" + this.debugInfo(name));\n        }\n      }\n    }\n\n    XMLDTDEntity.prototype.toString = function(options) {\n      return this.options.writer.set(options).dtdEntity(this);\n    };\n\n    return XMLDTDEntity;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLDTDEntity.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLDTDNotation.js":
/*!*******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDTDNotation.js ***!
  \*******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDTDNotation, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLDTDNotation = (function(superClass) {\n    extend(XMLDTDNotation, superClass);\n\n    function XMLDTDNotation(parent, name, value) {\n      XMLDTDNotation.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD notation name. \" + this.debugInfo(name));\n      }\n      if (!value.pubID && !value.sysID) {\n        throw new Error(\"Public or system identifiers are required for an external entity. \" + this.debugInfo(name));\n      }\n      this.name = this.stringify.eleName(name);\n      if (value.pubID != null) {\n        this.pubID = this.stringify.dtdPubID(value.pubID);\n      }\n      if (value.sysID != null) {\n        this.sysID = this.stringify.dtdSysID(value.sysID);\n      }\n    }\n\n    XMLDTDNotation.prototype.toString = function(options) {\n      return this.options.writer.set(options).dtdNotation(this);\n    };\n\n    return XMLDTDNotation;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLDTDNotation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLDeclaration.js":
/*!*******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDeclaration.js ***!
  \*******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDeclaration, XMLNode, isObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isObject = (__webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/xmlbuilder/lib/Utility.js\").isObject);\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLDeclaration = (function(superClass) {\n    extend(XMLDeclaration, superClass);\n\n    function XMLDeclaration(parent, version, encoding, standalone) {\n      var ref;\n      XMLDeclaration.__super__.constructor.call(this, parent);\n      if (isObject(version)) {\n        ref = version, version = ref.version, encoding = ref.encoding, standalone = ref.standalone;\n      }\n      if (!version) {\n        version = '1.0';\n      }\n      this.version = this.stringify.xmlVersion(version);\n      if (encoding != null) {\n        this.encoding = this.stringify.xmlEncoding(encoding);\n      }\n      if (standalone != null) {\n        this.standalone = this.stringify.xmlStandalone(standalone);\n      }\n    }\n\n    XMLDeclaration.prototype.toString = function(options) {\n      return this.options.writer.set(options).declaration(this);\n    };\n\n    return XMLDeclaration;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLDeclaration.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLDocType.js":
/*!***************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDocType.js ***!
  \***************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDocType, XMLNode, isObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isObject = (__webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/xmlbuilder/lib/Utility.js\").isObject);\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  XMLDTDAttList = __webpack_require__(/*! ./XMLDTDAttList */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDAttList.js\");\n\n  XMLDTDEntity = __webpack_require__(/*! ./XMLDTDEntity */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDEntity.js\");\n\n  XMLDTDElement = __webpack_require__(/*! ./XMLDTDElement */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDElement.js\");\n\n  XMLDTDNotation = __webpack_require__(/*! ./XMLDTDNotation */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDNotation.js\");\n\n  module.exports = XMLDocType = (function(superClass) {\n    extend(XMLDocType, superClass);\n\n    function XMLDocType(parent, pubID, sysID) {\n      var ref, ref1;\n      XMLDocType.__super__.constructor.call(this, parent);\n      this.name = \"!DOCTYPE\";\n      this.documentObject = parent;\n      if (isObject(pubID)) {\n        ref = pubID, pubID = ref.pubID, sysID = ref.sysID;\n      }\n      if (sysID == null) {\n        ref1 = [pubID, sysID], sysID = ref1[0], pubID = ref1[1];\n      }\n      if (pubID != null) {\n        this.pubID = this.stringify.dtdPubID(pubID);\n      }\n      if (sysID != null) {\n        this.sysID = this.stringify.dtdSysID(sysID);\n      }\n    }\n\n    XMLDocType.prototype.element = function(name, value) {\n      var child;\n      child = new XMLDTDElement(this, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.attList = function(elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      var child;\n      child = new XMLDTDAttList(this, elementName, attributeName, attributeType, defaultValueType, defaultValue);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.entity = function(name, value) {\n      var child;\n      child = new XMLDTDEntity(this, false, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.pEntity = function(name, value) {\n      var child;\n      child = new XMLDTDEntity(this, true, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.notation = function(name, value) {\n      var child;\n      child = new XMLDTDNotation(this, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.toString = function(options) {\n      return this.options.writer.set(options).docType(this);\n    };\n\n    XMLDocType.prototype.ele = function(name, value) {\n      return this.element(name, value);\n    };\n\n    XMLDocType.prototype.att = function(elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      return this.attList(elementName, attributeName, attributeType, defaultValueType, defaultValue);\n    };\n\n    XMLDocType.prototype.ent = function(name, value) {\n      return this.entity(name, value);\n    };\n\n    XMLDocType.prototype.pent = function(name, value) {\n      return this.pEntity(name, value);\n    };\n\n    XMLDocType.prototype.not = function(name, value) {\n      return this.notation(name, value);\n    };\n\n    XMLDocType.prototype.up = function() {\n      return this.root() || this.documentObject;\n    };\n\n    return XMLDocType;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvWE1MRG9jVHlwZS5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUMsMEJBQTBCLDJEQUEyRCxrQkFBa0IsNEJBQTRCLG1DQUFtQyw4QkFBOEIsb0NBQW9DLGVBQWU7QUFDOVIsZ0JBQWdCOztBQUVoQixhQUFhLGlHQUE2Qjs7QUFFMUMsWUFBWSxtQkFBTyxDQUFDLGlFQUFXOztBQUUvQixrQkFBa0IsbUJBQU8sQ0FBQyw2RUFBaUI7O0FBRTNDLGlCQUFpQixtQkFBTyxDQUFDLDJFQUFnQjs7QUFFekMsa0JBQWtCLG1CQUFPLENBQUMsNkVBQWlCOztBQUUzQyxtQkFBbUIsbUJBQU8sQ0FBQywrRUFBa0I7O0FBRTdDO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBLEdBQUc7O0FBRUgsQ0FBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx4eWllblxcT25lRHJpdmVcXERlc2t0b3BcXEFsbEpvdXJuYWxcXGpvdXJuYWwtYXBwXFxub2RlX21vZHVsZXNcXHhtbGJ1aWxkZXJcXGxpYlxcWE1MRG9jVHlwZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBHZW5lcmF0ZWQgYnkgQ29mZmVlU2NyaXB0IDEuMTIuN1xuKGZ1bmN0aW9uKCkge1xuICB2YXIgWE1MRFREQXR0TGlzdCwgWE1MRFRERWxlbWVudCwgWE1MRFRERW50aXR5LCBYTUxEVEROb3RhdGlvbiwgWE1MRG9jVHlwZSwgWE1MTm9kZSwgaXNPYmplY3QsXG4gICAgZXh0ZW5kID0gZnVuY3Rpb24oY2hpbGQsIHBhcmVudCkgeyBmb3IgKHZhciBrZXkgaW4gcGFyZW50KSB7IGlmIChoYXNQcm9wLmNhbGwocGFyZW50LCBrZXkpKSBjaGlsZFtrZXldID0gcGFyZW50W2tleV07IH0gZnVuY3Rpb24gY3RvcigpIHsgdGhpcy5jb25zdHJ1Y3RvciA9IGNoaWxkOyB9IGN0b3IucHJvdG90eXBlID0gcGFyZW50LnByb3RvdHlwZTsgY2hpbGQucHJvdG90eXBlID0gbmV3IGN0b3IoKTsgY2hpbGQuX19zdXBlcl9fID0gcGFyZW50LnByb3RvdHlwZTsgcmV0dXJuIGNoaWxkOyB9LFxuICAgIGhhc1Byb3AgPSB7fS5oYXNPd25Qcm9wZXJ0eTtcblxuICBpc09iamVjdCA9IHJlcXVpcmUoJy4vVXRpbGl0eScpLmlzT2JqZWN0O1xuXG4gIFhNTE5vZGUgPSByZXF1aXJlKCcuL1hNTE5vZGUnKTtcblxuICBYTUxEVERBdHRMaXN0ID0gcmVxdWlyZSgnLi9YTUxEVERBdHRMaXN0Jyk7XG5cbiAgWE1MRFRERW50aXR5ID0gcmVxdWlyZSgnLi9YTUxEVERFbnRpdHknKTtcblxuICBYTUxEVERFbGVtZW50ID0gcmVxdWlyZSgnLi9YTUxEVERFbGVtZW50Jyk7XG5cbiAgWE1MRFRETm90YXRpb24gPSByZXF1aXJlKCcuL1hNTERURE5vdGF0aW9uJyk7XG5cbiAgbW9kdWxlLmV4cG9ydHMgPSBYTUxEb2NUeXBlID0gKGZ1bmN0aW9uKHN1cGVyQ2xhc3MpIHtcbiAgICBleHRlbmQoWE1MRG9jVHlwZSwgc3VwZXJDbGFzcyk7XG5cbiAgICBmdW5jdGlvbiBYTUxEb2NUeXBlKHBhcmVudCwgcHViSUQsIHN5c0lEKSB7XG4gICAgICB2YXIgcmVmLCByZWYxO1xuICAgICAgWE1MRG9jVHlwZS5fX3N1cGVyX18uY29uc3RydWN0b3IuY2FsbCh0aGlzLCBwYXJlbnQpO1xuICAgICAgdGhpcy5uYW1lID0gXCIhRE9DVFlQRVwiO1xuICAgICAgdGhpcy5kb2N1bWVudE9iamVjdCA9IHBhcmVudDtcbiAgICAgIGlmIChpc09iamVjdChwdWJJRCkpIHtcbiAgICAgICAgcmVmID0gcHViSUQsIHB1YklEID0gcmVmLnB1YklELCBzeXNJRCA9IHJlZi5zeXNJRDtcbiAgICAgIH1cbiAgICAgIGlmIChzeXNJRCA9PSBudWxsKSB7XG4gICAgICAgIHJlZjEgPSBbcHViSUQsIHN5c0lEXSwgc3lzSUQgPSByZWYxWzBdLCBwdWJJRCA9IHJlZjFbMV07XG4gICAgICB9XG4gICAgICBpZiAocHViSUQgIT0gbnVsbCkge1xuICAgICAgICB0aGlzLnB1YklEID0gdGhpcy5zdHJpbmdpZnkuZHRkUHViSUQocHViSUQpO1xuICAgICAgfVxuICAgICAgaWYgKHN5c0lEICE9IG51bGwpIHtcbiAgICAgICAgdGhpcy5zeXNJRCA9IHRoaXMuc3RyaW5naWZ5LmR0ZFN5c0lEKHN5c0lEKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBYTUxEb2NUeXBlLnByb3RvdHlwZS5lbGVtZW50ID0gZnVuY3Rpb24obmFtZSwgdmFsdWUpIHtcbiAgICAgIHZhciBjaGlsZDtcbiAgICAgIGNoaWxkID0gbmV3IFhNTERUREVsZW1lbnQodGhpcywgbmFtZSwgdmFsdWUpO1xuICAgICAgdGhpcy5jaGlsZHJlbi5wdXNoKGNoaWxkKTtcbiAgICAgIHJldHVybiB0aGlzO1xuICAgIH07XG5cbiAgICBYTUxEb2NUeXBlLnByb3RvdHlwZS5hdHRMaXN0ID0gZnVuY3Rpb24oZWxlbWVudE5hbWUsIGF0dHJpYnV0ZU5hbWUsIGF0dHJpYnV0ZVR5cGUsIGRlZmF1bHRWYWx1ZVR5cGUsIGRlZmF1bHRWYWx1ZSkge1xuICAgICAgdmFyIGNoaWxkO1xuICAgICAgY2hpbGQgPSBuZXcgWE1MRFREQXR0TGlzdCh0aGlzLCBlbGVtZW50TmFtZSwgYXR0cmlidXRlTmFtZSwgYXR0cmlidXRlVHlwZSwgZGVmYXVsdFZhbHVlVHlwZSwgZGVmYXVsdFZhbHVlKTtcbiAgICAgIHRoaXMuY2hpbGRyZW4ucHVzaChjaGlsZCk7XG4gICAgICByZXR1cm4gdGhpcztcbiAgICB9O1xuXG4gICAgWE1MRG9jVHlwZS5wcm90b3R5cGUuZW50aXR5ID0gZnVuY3Rpb24obmFtZSwgdmFsdWUpIHtcbiAgICAgIHZhciBjaGlsZDtcbiAgICAgIGNoaWxkID0gbmV3IFhNTERUREVudGl0eSh0aGlzLCBmYWxzZSwgbmFtZSwgdmFsdWUpO1xuICAgICAgdGhpcy5jaGlsZHJlbi5wdXNoKGNoaWxkKTtcbiAgICAgIHJldHVybiB0aGlzO1xuICAgIH07XG5cbiAgICBYTUxEb2NUeXBlLnByb3RvdHlwZS5wRW50aXR5ID0gZnVuY3Rpb24obmFtZSwgdmFsdWUpIHtcbiAgICAgIHZhciBjaGlsZDtcbiAgICAgIGNoaWxkID0gbmV3IFhNTERUREVudGl0eSh0aGlzLCB0cnVlLCBuYW1lLCB2YWx1ZSk7XG4gICAgICB0aGlzLmNoaWxkcmVuLnB1c2goY2hpbGQpO1xuICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfTtcblxuICAgIFhNTERvY1R5cGUucHJvdG90eXBlLm5vdGF0aW9uID0gZnVuY3Rpb24obmFtZSwgdmFsdWUpIHtcbiAgICAgIHZhciBjaGlsZDtcbiAgICAgIGNoaWxkID0gbmV3IFhNTERURE5vdGF0aW9uKHRoaXMsIG5hbWUsIHZhbHVlKTtcbiAgICAgIHRoaXMuY2hpbGRyZW4ucHVzaChjaGlsZCk7XG4gICAgICByZXR1cm4gdGhpcztcbiAgICB9O1xuXG4gICAgWE1MRG9jVHlwZS5wcm90b3R5cGUudG9TdHJpbmcgPSBmdW5jdGlvbihvcHRpb25zKSB7XG4gICAgICByZXR1cm4gdGhpcy5vcHRpb25zLndyaXRlci5zZXQob3B0aW9ucykuZG9jVHlwZSh0aGlzKTtcbiAgICB9O1xuXG4gICAgWE1MRG9jVHlwZS5wcm90b3R5cGUuZWxlID0gZnVuY3Rpb24obmFtZSwgdmFsdWUpIHtcbiAgICAgIHJldHVybiB0aGlzLmVsZW1lbnQobmFtZSwgdmFsdWUpO1xuICAgIH07XG5cbiAgICBYTUxEb2NUeXBlLnByb3RvdHlwZS5hdHQgPSBmdW5jdGlvbihlbGVtZW50TmFtZSwgYXR0cmlidXRlTmFtZSwgYXR0cmlidXRlVHlwZSwgZGVmYXVsdFZhbHVlVHlwZSwgZGVmYXVsdFZhbHVlKSB7XG4gICAgICByZXR1cm4gdGhpcy5hdHRMaXN0KGVsZW1lbnROYW1lLCBhdHRyaWJ1dGVOYW1lLCBhdHRyaWJ1dGVUeXBlLCBkZWZhdWx0VmFsdWVUeXBlLCBkZWZhdWx0VmFsdWUpO1xuICAgIH07XG5cbiAgICBYTUxEb2NUeXBlLnByb3RvdHlwZS5lbnQgPSBmdW5jdGlvbihuYW1lLCB2YWx1ZSkge1xuICAgICAgcmV0dXJuIHRoaXMuZW50aXR5KG5hbWUsIHZhbHVlKTtcbiAgICB9O1xuXG4gICAgWE1MRG9jVHlwZS5wcm90b3R5cGUucGVudCA9IGZ1bmN0aW9uKG5hbWUsIHZhbHVlKSB7XG4gICAgICByZXR1cm4gdGhpcy5wRW50aXR5KG5hbWUsIHZhbHVlKTtcbiAgICB9O1xuXG4gICAgWE1MRG9jVHlwZS5wcm90b3R5cGUubm90ID0gZnVuY3Rpb24obmFtZSwgdmFsdWUpIHtcbiAgICAgIHJldHVybiB0aGlzLm5vdGF0aW9uKG5hbWUsIHZhbHVlKTtcbiAgICB9O1xuXG4gICAgWE1MRG9jVHlwZS5wcm90b3R5cGUudXAgPSBmdW5jdGlvbigpIHtcbiAgICAgIHJldHVybiB0aGlzLnJvb3QoKSB8fCB0aGlzLmRvY3VtZW50T2JqZWN0O1xuICAgIH07XG5cbiAgICByZXR1cm4gWE1MRG9jVHlwZTtcblxuICB9KShYTUxOb2RlKTtcblxufSkuY2FsbCh0aGlzKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLDocType.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLDocument.js":
/*!****************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDocument.js ***!
  \****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDocument, XMLNode, XMLStringWriter, XMLStringifier, isPlainObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isPlainObject = (__webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/xmlbuilder/lib/Utility.js\").isPlainObject);\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  XMLStringifier = __webpack_require__(/*! ./XMLStringifier */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLStringifier.js\");\n\n  XMLStringWriter = __webpack_require__(/*! ./XMLStringWriter */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLStringWriter.js\");\n\n  module.exports = XMLDocument = (function(superClass) {\n    extend(XMLDocument, superClass);\n\n    function XMLDocument(options) {\n      XMLDocument.__super__.constructor.call(this, null);\n      this.name = \"?xml\";\n      options || (options = {});\n      if (!options.writer) {\n        options.writer = new XMLStringWriter();\n      }\n      this.options = options;\n      this.stringify = new XMLStringifier(options);\n      this.isDocument = true;\n    }\n\n    XMLDocument.prototype.end = function(writer) {\n      var writerOptions;\n      if (!writer) {\n        writer = this.options.writer;\n      } else if (isPlainObject(writer)) {\n        writerOptions = writer;\n        writer = this.options.writer.set(writerOptions);\n      }\n      return writer.document(this);\n    };\n\n    XMLDocument.prototype.toString = function(options) {\n      return this.options.writer.set(options).document(this);\n    };\n\n    return XMLDocument;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLDocument.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLDocumentCB.js":
/*!******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDocumentCB.js ***!
  \******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLAttribute, XMLCData, XMLComment, XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDeclaration, XMLDocType, XMLDocumentCB, XMLElement, XMLProcessingInstruction, XMLRaw, XMLStringWriter, XMLStringifier, XMLText, getValue, isFunction, isObject, isPlainObject, ref,\n    hasProp = {}.hasOwnProperty;\n\n  ref = __webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/xmlbuilder/lib/Utility.js\"), isObject = ref.isObject, isFunction = ref.isFunction, isPlainObject = ref.isPlainObject, getValue = ref.getValue;\n\n  XMLElement = __webpack_require__(/*! ./XMLElement */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLElement.js\");\n\n  XMLCData = __webpack_require__(/*! ./XMLCData */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLCData.js\");\n\n  XMLComment = __webpack_require__(/*! ./XMLComment */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLComment.js\");\n\n  XMLRaw = __webpack_require__(/*! ./XMLRaw */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLRaw.js\");\n\n  XMLText = __webpack_require__(/*! ./XMLText */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLText.js\");\n\n  XMLProcessingInstruction = __webpack_require__(/*! ./XMLProcessingInstruction */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js\");\n\n  XMLDeclaration = __webpack_require__(/*! ./XMLDeclaration */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDeclaration.js\");\n\n  XMLDocType = __webpack_require__(/*! ./XMLDocType */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDocType.js\");\n\n  XMLDTDAttList = __webpack_require__(/*! ./XMLDTDAttList */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDAttList.js\");\n\n  XMLDTDEntity = __webpack_require__(/*! ./XMLDTDEntity */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDEntity.js\");\n\n  XMLDTDElement = __webpack_require__(/*! ./XMLDTDElement */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDElement.js\");\n\n  XMLDTDNotation = __webpack_require__(/*! ./XMLDTDNotation */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDNotation.js\");\n\n  XMLAttribute = __webpack_require__(/*! ./XMLAttribute */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLAttribute.js\");\n\n  XMLStringifier = __webpack_require__(/*! ./XMLStringifier */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLStringifier.js\");\n\n  XMLStringWriter = __webpack_require__(/*! ./XMLStringWriter */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLStringWriter.js\");\n\n  module.exports = XMLDocumentCB = (function() {\n    function XMLDocumentCB(options, onData, onEnd) {\n      var writerOptions;\n      this.name = \"?xml\";\n      options || (options = {});\n      if (!options.writer) {\n        options.writer = new XMLStringWriter(options);\n      } else if (isPlainObject(options.writer)) {\n        writerOptions = options.writer;\n        options.writer = new XMLStringWriter(writerOptions);\n      }\n      this.options = options;\n      this.writer = options.writer;\n      this.stringify = new XMLStringifier(options);\n      this.onDataCallback = onData || function() {};\n      this.onEndCallback = onEnd || function() {};\n      this.currentNode = null;\n      this.currentLevel = -1;\n      this.openTags = {};\n      this.documentStarted = false;\n      this.documentCompleted = false;\n      this.root = null;\n    }\n\n    XMLDocumentCB.prototype.node = function(name, attributes, text) {\n      var ref1, ref2;\n      if (name == null) {\n        throw new Error(\"Missing node name.\");\n      }\n      if (this.root && this.currentLevel === -1) {\n        throw new Error(\"Document can only have one root node. \" + this.debugInfo(name));\n      }\n      this.openCurrent();\n      name = getValue(name);\n      if (attributes === null && (text == null)) {\n        ref1 = [{}, null], attributes = ref1[0], text = ref1[1];\n      }\n      if (attributes == null) {\n        attributes = {};\n      }\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref2 = [attributes, text], text = ref2[0], attributes = ref2[1];\n      }\n      this.currentNode = new XMLElement(this, name, attributes);\n      this.currentNode.children = false;\n      this.currentLevel++;\n      this.openTags[this.currentLevel] = this.currentNode;\n      if (text != null) {\n        this.text(text);\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.element = function(name, attributes, text) {\n      if (this.currentNode && this.currentNode instanceof XMLDocType) {\n        return this.dtdElement.apply(this, arguments);\n      } else {\n        return this.node(name, attributes, text);\n      }\n    };\n\n    XMLDocumentCB.prototype.attribute = function(name, value) {\n      var attName, attValue;\n      if (!this.currentNode || this.currentNode.children) {\n        throw new Error(\"att() can only be used immediately after an ele() call in callback mode. \" + this.debugInfo(name));\n      }\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (isObject(name)) {\n        for (attName in name) {\n          if (!hasProp.call(name, attName)) continue;\n          attValue = name[attName];\n          this.attribute(attName, attValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        if (!this.options.skipNullAttributes || (value != null)) {\n          this.currentNode.attributes[name] = new XMLAttribute(this, name, value);\n        }\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.text = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLText(this, value);\n      this.onData(this.writer.text(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.cdata = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLCData(this, value);\n      this.onData(this.writer.cdata(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.comment = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLComment(this, value);\n      this.onData(this.writer.comment(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.raw = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLRaw(this, value);\n      this.onData(this.writer.raw(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.instruction = function(target, value) {\n      var i, insTarget, insValue, len, node;\n      this.openCurrent();\n      if (target != null) {\n        target = getValue(target);\n      }\n      if (value != null) {\n        value = getValue(value);\n      }\n      if (Array.isArray(target)) {\n        for (i = 0, len = target.length; i < len; i++) {\n          insTarget = target[i];\n          this.instruction(insTarget);\n        }\n      } else if (isObject(target)) {\n        for (insTarget in target) {\n          if (!hasProp.call(target, insTarget)) continue;\n          insValue = target[insTarget];\n          this.instruction(insTarget, insValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        node = new XMLProcessingInstruction(this, target, value);\n        this.onData(this.writer.processingInstruction(node, this.currentLevel + 1), this.currentLevel + 1);\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.declaration = function(version, encoding, standalone) {\n      var node;\n      this.openCurrent();\n      if (this.documentStarted) {\n        throw new Error(\"declaration() must be the first node.\");\n      }\n      node = new XMLDeclaration(this, version, encoding, standalone);\n      this.onData(this.writer.declaration(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.doctype = function(root, pubID, sysID) {\n      this.openCurrent();\n      if (root == null) {\n        throw new Error(\"Missing root node name.\");\n      }\n      if (this.root) {\n        throw new Error(\"dtd() must come before the root node.\");\n      }\n      this.currentNode = new XMLDocType(this, pubID, sysID);\n      this.currentNode.rootNodeName = root;\n      this.currentNode.children = false;\n      this.currentLevel++;\n      this.openTags[this.currentLevel] = this.currentNode;\n      return this;\n    };\n\n    XMLDocumentCB.prototype.dtdElement = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDElement(this, name, value);\n      this.onData(this.writer.dtdElement(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.attList = function(elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDAttList(this, elementName, attributeName, attributeType, defaultValueType, defaultValue);\n      this.onData(this.writer.dtdAttList(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.entity = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDEntity(this, false, name, value);\n      this.onData(this.writer.dtdEntity(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.pEntity = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDEntity(this, true, name, value);\n      this.onData(this.writer.dtdEntity(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.notation = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDNotation(this, name, value);\n      this.onData(this.writer.dtdNotation(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.up = function() {\n      if (this.currentLevel < 0) {\n        throw new Error(\"The document node has no parent.\");\n      }\n      if (this.currentNode) {\n        if (this.currentNode.children) {\n          this.closeNode(this.currentNode);\n        } else {\n          this.openNode(this.currentNode);\n        }\n        this.currentNode = null;\n      } else {\n        this.closeNode(this.openTags[this.currentLevel]);\n      }\n      delete this.openTags[this.currentLevel];\n      this.currentLevel--;\n      return this;\n    };\n\n    XMLDocumentCB.prototype.end = function() {\n      while (this.currentLevel >= 0) {\n        this.up();\n      }\n      return this.onEnd();\n    };\n\n    XMLDocumentCB.prototype.openCurrent = function() {\n      if (this.currentNode) {\n        this.currentNode.children = true;\n        return this.openNode(this.currentNode);\n      }\n    };\n\n    XMLDocumentCB.prototype.openNode = function(node) {\n      if (!node.isOpen) {\n        if (!this.root && this.currentLevel === 0 && node instanceof XMLElement) {\n          this.root = node;\n        }\n        this.onData(this.writer.openNode(node, this.currentLevel), this.currentLevel);\n        return node.isOpen = true;\n      }\n    };\n\n    XMLDocumentCB.prototype.closeNode = function(node) {\n      if (!node.isClosed) {\n        this.onData(this.writer.closeNode(node, this.currentLevel), this.currentLevel);\n        return node.isClosed = true;\n      }\n    };\n\n    XMLDocumentCB.prototype.onData = function(chunk, level) {\n      this.documentStarted = true;\n      return this.onDataCallback(chunk, level + 1);\n    };\n\n    XMLDocumentCB.prototype.onEnd = function() {\n      this.documentCompleted = true;\n      return this.onEndCallback();\n    };\n\n    XMLDocumentCB.prototype.debugInfo = function(name) {\n      if (name == null) {\n        return \"\";\n      } else {\n        return \"node: <\" + name + \">\";\n      }\n    };\n\n    XMLDocumentCB.prototype.ele = function() {\n      return this.element.apply(this, arguments);\n    };\n\n    XMLDocumentCB.prototype.nod = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLDocumentCB.prototype.txt = function(value) {\n      return this.text(value);\n    };\n\n    XMLDocumentCB.prototype.dat = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLDocumentCB.prototype.com = function(value) {\n      return this.comment(value);\n    };\n\n    XMLDocumentCB.prototype.ins = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLDocumentCB.prototype.dec = function(version, encoding, standalone) {\n      return this.declaration(version, encoding, standalone);\n    };\n\n    XMLDocumentCB.prototype.dtd = function(root, pubID, sysID) {\n      return this.doctype(root, pubID, sysID);\n    };\n\n    XMLDocumentCB.prototype.e = function(name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n\n    XMLDocumentCB.prototype.n = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLDocumentCB.prototype.t = function(value) {\n      return this.text(value);\n    };\n\n    XMLDocumentCB.prototype.d = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLDocumentCB.prototype.c = function(value) {\n      return this.comment(value);\n    };\n\n    XMLDocumentCB.prototype.r = function(value) {\n      return this.raw(value);\n    };\n\n    XMLDocumentCB.prototype.i = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLDocumentCB.prototype.att = function() {\n      if (this.currentNode && this.currentNode instanceof XMLDocType) {\n        return this.attList.apply(this, arguments);\n      } else {\n        return this.attribute.apply(this, arguments);\n      }\n    };\n\n    XMLDocumentCB.prototype.a = function() {\n      if (this.currentNode && this.currentNode instanceof XMLDocType) {\n        return this.attList.apply(this, arguments);\n      } else {\n        return this.attribute.apply(this, arguments);\n      }\n    };\n\n    XMLDocumentCB.prototype.ent = function(name, value) {\n      return this.entity(name, value);\n    };\n\n    XMLDocumentCB.prototype.pent = function(name, value) {\n      return this.pEntity(name, value);\n    };\n\n    XMLDocumentCB.prototype.not = function(name, value) {\n      return this.notation(name, value);\n    };\n\n    return XMLDocumentCB;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLDocumentCB.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLDummy.js":
/*!*************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDummy.js ***!
  \*************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDummy, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLDummy = (function(superClass) {\n    extend(XMLDummy, superClass);\n\n    function XMLDummy(parent) {\n      XMLDummy.__super__.constructor.call(this, parent);\n      this.isDummy = true;\n    }\n\n    XMLDummy.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLDummy.prototype.toString = function(options) {\n      return '';\n    };\n\n    return XMLDummy;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvWE1MRHVtbXkuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLDBCQUEwQiwyREFBMkQsa0JBQWtCLDRCQUE0QixtQ0FBbUMsOEJBQThCLG9DQUFvQyxlQUFlO0FBQzlSLGdCQUFnQjs7QUFFaEIsWUFBWSxtQkFBTyxDQUFDLGlFQUFXOztBQUUvQjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEsR0FBRzs7QUFFSCxDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHh5aWVuXFxPbmVEcml2ZVxcRGVza3RvcFxcQWxsSm91cm5hbFxcam91cm5hbC1hcHBcXG5vZGVfbW9kdWxlc1xceG1sYnVpbGRlclxcbGliXFxYTUxEdW1teS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBHZW5lcmF0ZWQgYnkgQ29mZmVlU2NyaXB0IDEuMTIuN1xuKGZ1bmN0aW9uKCkge1xuICB2YXIgWE1MRHVtbXksIFhNTE5vZGUsXG4gICAgZXh0ZW5kID0gZnVuY3Rpb24oY2hpbGQsIHBhcmVudCkgeyBmb3IgKHZhciBrZXkgaW4gcGFyZW50KSB7IGlmIChoYXNQcm9wLmNhbGwocGFyZW50LCBrZXkpKSBjaGlsZFtrZXldID0gcGFyZW50W2tleV07IH0gZnVuY3Rpb24gY3RvcigpIHsgdGhpcy5jb25zdHJ1Y3RvciA9IGNoaWxkOyB9IGN0b3IucHJvdG90eXBlID0gcGFyZW50LnByb3RvdHlwZTsgY2hpbGQucHJvdG90eXBlID0gbmV3IGN0b3IoKTsgY2hpbGQuX19zdXBlcl9fID0gcGFyZW50LnByb3RvdHlwZTsgcmV0dXJuIGNoaWxkOyB9LFxuICAgIGhhc1Byb3AgPSB7fS5oYXNPd25Qcm9wZXJ0eTtcblxuICBYTUxOb2RlID0gcmVxdWlyZSgnLi9YTUxOb2RlJyk7XG5cbiAgbW9kdWxlLmV4cG9ydHMgPSBYTUxEdW1teSA9IChmdW5jdGlvbihzdXBlckNsYXNzKSB7XG4gICAgZXh0ZW5kKFhNTER1bW15LCBzdXBlckNsYXNzKTtcblxuICAgIGZ1bmN0aW9uIFhNTER1bW15KHBhcmVudCkge1xuICAgICAgWE1MRHVtbXkuX19zdXBlcl9fLmNvbnN0cnVjdG9yLmNhbGwodGhpcywgcGFyZW50KTtcbiAgICAgIHRoaXMuaXNEdW1teSA9IHRydWU7XG4gICAgfVxuXG4gICAgWE1MRHVtbXkucHJvdG90eXBlLmNsb25lID0gZnVuY3Rpb24oKSB7XG4gICAgICByZXR1cm4gT2JqZWN0LmNyZWF0ZSh0aGlzKTtcbiAgICB9O1xuXG4gICAgWE1MRHVtbXkucHJvdG90eXBlLnRvU3RyaW5nID0gZnVuY3Rpb24ob3B0aW9ucykge1xuICAgICAgcmV0dXJuICcnO1xuICAgIH07XG5cbiAgICByZXR1cm4gWE1MRHVtbXk7XG5cbiAgfSkoWE1MTm9kZSk7XG5cbn0pLmNhbGwodGhpcyk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLDummy.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLElement.js":
/*!***************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLElement.js ***!
  \***************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLAttribute, XMLElement, XMLNode, getValue, isFunction, isObject, ref,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  ref = __webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/xmlbuilder/lib/Utility.js\"), isObject = ref.isObject, isFunction = ref.isFunction, getValue = ref.getValue;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  XMLAttribute = __webpack_require__(/*! ./XMLAttribute */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLAttribute.js\");\n\n  module.exports = XMLElement = (function(superClass) {\n    extend(XMLElement, superClass);\n\n    function XMLElement(parent, name, attributes) {\n      XMLElement.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing element name. \" + this.debugInfo());\n      }\n      this.name = this.stringify.eleName(name);\n      this.attributes = {};\n      if (attributes != null) {\n        this.attribute(attributes);\n      }\n      if (parent.isDocument) {\n        this.isRoot = true;\n        this.documentObject = parent;\n        parent.rootObject = this;\n      }\n    }\n\n    XMLElement.prototype.clone = function() {\n      var att, attName, clonedSelf, ref1;\n      clonedSelf = Object.create(this);\n      if (clonedSelf.isRoot) {\n        clonedSelf.documentObject = null;\n      }\n      clonedSelf.attributes = {};\n      ref1 = this.attributes;\n      for (attName in ref1) {\n        if (!hasProp.call(ref1, attName)) continue;\n        att = ref1[attName];\n        clonedSelf.attributes[attName] = att.clone();\n      }\n      clonedSelf.children = [];\n      this.children.forEach(function(child) {\n        var clonedChild;\n        clonedChild = child.clone();\n        clonedChild.parent = clonedSelf;\n        return clonedSelf.children.push(clonedChild);\n      });\n      return clonedSelf;\n    };\n\n    XMLElement.prototype.attribute = function(name, value) {\n      var attName, attValue;\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (isObject(name)) {\n        for (attName in name) {\n          if (!hasProp.call(name, attName)) continue;\n          attValue = name[attName];\n          this.attribute(attName, attValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        if (!this.options.skipNullAttributes || (value != null)) {\n          this.attributes[name] = new XMLAttribute(this, name, value);\n        }\n      }\n      return this;\n    };\n\n    XMLElement.prototype.removeAttribute = function(name) {\n      var attName, i, len;\n      if (name == null) {\n        throw new Error(\"Missing attribute name. \" + this.debugInfo());\n      }\n      name = getValue(name);\n      if (Array.isArray(name)) {\n        for (i = 0, len = name.length; i < len; i++) {\n          attName = name[i];\n          delete this.attributes[attName];\n        }\n      } else {\n        delete this.attributes[name];\n      }\n      return this;\n    };\n\n    XMLElement.prototype.toString = function(options) {\n      return this.options.writer.set(options).element(this);\n    };\n\n    XMLElement.prototype.att = function(name, value) {\n      return this.attribute(name, value);\n    };\n\n    XMLElement.prototype.a = function(name, value) {\n      return this.attribute(name, value);\n    };\n\n    return XMLElement;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLElement.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js":
/*!************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLNode.js ***!
  \************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLCData, XMLComment, XMLDeclaration, XMLDocType, XMLDummy, XMLElement, XMLNode, XMLProcessingInstruction, XMLRaw, XMLText, getValue, isEmpty, isFunction, isObject, ref,\n    hasProp = {}.hasOwnProperty;\n\n  ref = __webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/xmlbuilder/lib/Utility.js\"), isObject = ref.isObject, isFunction = ref.isFunction, isEmpty = ref.isEmpty, getValue = ref.getValue;\n\n  XMLElement = null;\n\n  XMLCData = null;\n\n  XMLComment = null;\n\n  XMLDeclaration = null;\n\n  XMLDocType = null;\n\n  XMLRaw = null;\n\n  XMLText = null;\n\n  XMLProcessingInstruction = null;\n\n  XMLDummy = null;\n\n  module.exports = XMLNode = (function() {\n    function XMLNode(parent) {\n      this.parent = parent;\n      if (this.parent) {\n        this.options = this.parent.options;\n        this.stringify = this.parent.stringify;\n      }\n      this.children = [];\n      if (!XMLElement) {\n        XMLElement = __webpack_require__(/*! ./XMLElement */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLElement.js\");\n        XMLCData = __webpack_require__(/*! ./XMLCData */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLCData.js\");\n        XMLComment = __webpack_require__(/*! ./XMLComment */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLComment.js\");\n        XMLDeclaration = __webpack_require__(/*! ./XMLDeclaration */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDeclaration.js\");\n        XMLDocType = __webpack_require__(/*! ./XMLDocType */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDocType.js\");\n        XMLRaw = __webpack_require__(/*! ./XMLRaw */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLRaw.js\");\n        XMLText = __webpack_require__(/*! ./XMLText */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLText.js\");\n        XMLProcessingInstruction = __webpack_require__(/*! ./XMLProcessingInstruction */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js\");\n        XMLDummy = __webpack_require__(/*! ./XMLDummy */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDummy.js\");\n      }\n    }\n\n    XMLNode.prototype.element = function(name, attributes, text) {\n      var childNode, item, j, k, key, lastChild, len, len1, ref1, ref2, val;\n      lastChild = null;\n      if (attributes === null && (text == null)) {\n        ref1 = [{}, null], attributes = ref1[0], text = ref1[1];\n      }\n      if (attributes == null) {\n        attributes = {};\n      }\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref2 = [attributes, text], text = ref2[0], attributes = ref2[1];\n      }\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (Array.isArray(name)) {\n        for (j = 0, len = name.length; j < len; j++) {\n          item = name[j];\n          lastChild = this.element(item);\n        }\n      } else if (isFunction(name)) {\n        lastChild = this.element(name.apply());\n      } else if (isObject(name)) {\n        for (key in name) {\n          if (!hasProp.call(name, key)) continue;\n          val = name[key];\n          if (isFunction(val)) {\n            val = val.apply();\n          }\n          if ((isObject(val)) && (isEmpty(val))) {\n            val = null;\n          }\n          if (!this.options.ignoreDecorators && this.stringify.convertAttKey && key.indexOf(this.stringify.convertAttKey) === 0) {\n            lastChild = this.attribute(key.substr(this.stringify.convertAttKey.length), val);\n          } else if (!this.options.separateArrayItems && Array.isArray(val)) {\n            for (k = 0, len1 = val.length; k < len1; k++) {\n              item = val[k];\n              childNode = {};\n              childNode[key] = item;\n              lastChild = this.element(childNode);\n            }\n          } else if (isObject(val)) {\n            lastChild = this.element(key);\n            lastChild.element(val);\n          } else {\n            lastChild = this.element(key, val);\n          }\n        }\n      } else if (this.options.skipNullNodes && text === null) {\n        lastChild = this.dummy();\n      } else {\n        if (!this.options.ignoreDecorators && this.stringify.convertTextKey && name.indexOf(this.stringify.convertTextKey) === 0) {\n          lastChild = this.text(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertCDataKey && name.indexOf(this.stringify.convertCDataKey) === 0) {\n          lastChild = this.cdata(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertCommentKey && name.indexOf(this.stringify.convertCommentKey) === 0) {\n          lastChild = this.comment(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertRawKey && name.indexOf(this.stringify.convertRawKey) === 0) {\n          lastChild = this.raw(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertPIKey && name.indexOf(this.stringify.convertPIKey) === 0) {\n          lastChild = this.instruction(name.substr(this.stringify.convertPIKey.length), text);\n        } else {\n          lastChild = this.node(name, attributes, text);\n        }\n      }\n      if (lastChild == null) {\n        throw new Error(\"Could not create any elements with: \" + name + \". \" + this.debugInfo());\n      }\n      return lastChild;\n    };\n\n    XMLNode.prototype.insertBefore = function(name, attributes, text) {\n      var child, i, removed;\n      if (this.isRoot) {\n        throw new Error(\"Cannot insert elements at root level. \" + this.debugInfo(name));\n      }\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i);\n      child = this.parent.element(name, attributes, text);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return child;\n    };\n\n    XMLNode.prototype.insertAfter = function(name, attributes, text) {\n      var child, i, removed;\n      if (this.isRoot) {\n        throw new Error(\"Cannot insert elements at root level. \" + this.debugInfo(name));\n      }\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.element(name, attributes, text);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return child;\n    };\n\n    XMLNode.prototype.remove = function() {\n      var i, ref1;\n      if (this.isRoot) {\n        throw new Error(\"Cannot remove the root element. \" + this.debugInfo());\n      }\n      i = this.parent.children.indexOf(this);\n      [].splice.apply(this.parent.children, [i, i - i + 1].concat(ref1 = [])), ref1;\n      return this.parent;\n    };\n\n    XMLNode.prototype.node = function(name, attributes, text) {\n      var child, ref1;\n      if (name != null) {\n        name = getValue(name);\n      }\n      attributes || (attributes = {});\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref1 = [attributes, text], text = ref1[0], attributes = ref1[1];\n      }\n      child = new XMLElement(this, name, attributes);\n      if (text != null) {\n        child.text(text);\n      }\n      this.children.push(child);\n      return child;\n    };\n\n    XMLNode.prototype.text = function(value) {\n      var child;\n      child = new XMLText(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.cdata = function(value) {\n      var child;\n      child = new XMLCData(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.comment = function(value) {\n      var child;\n      child = new XMLComment(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.commentBefore = function(value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i);\n      child = this.parent.comment(value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.commentAfter = function(value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.comment(value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.raw = function(value) {\n      var child;\n      child = new XMLRaw(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.dummy = function() {\n      var child;\n      child = new XMLDummy(this);\n      this.children.push(child);\n      return child;\n    };\n\n    XMLNode.prototype.instruction = function(target, value) {\n      var insTarget, insValue, instruction, j, len;\n      if (target != null) {\n        target = getValue(target);\n      }\n      if (value != null) {\n        value = getValue(value);\n      }\n      if (Array.isArray(target)) {\n        for (j = 0, len = target.length; j < len; j++) {\n          insTarget = target[j];\n          this.instruction(insTarget);\n        }\n      } else if (isObject(target)) {\n        for (insTarget in target) {\n          if (!hasProp.call(target, insTarget)) continue;\n          insValue = target[insTarget];\n          this.instruction(insTarget, insValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        instruction = new XMLProcessingInstruction(this, target, value);\n        this.children.push(instruction);\n      }\n      return this;\n    };\n\n    XMLNode.prototype.instructionBefore = function(target, value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i);\n      child = this.parent.instruction(target, value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.instructionAfter = function(target, value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.instruction(target, value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.declaration = function(version, encoding, standalone) {\n      var doc, xmldec;\n      doc = this.document();\n      xmldec = new XMLDeclaration(doc, version, encoding, standalone);\n      if (doc.children[0] instanceof XMLDeclaration) {\n        doc.children[0] = xmldec;\n      } else {\n        doc.children.unshift(xmldec);\n      }\n      return doc.root() || doc;\n    };\n\n    XMLNode.prototype.doctype = function(pubID, sysID) {\n      var child, doc, doctype, i, j, k, len, len1, ref1, ref2;\n      doc = this.document();\n      doctype = new XMLDocType(doc, pubID, sysID);\n      ref1 = doc.children;\n      for (i = j = 0, len = ref1.length; j < len; i = ++j) {\n        child = ref1[i];\n        if (child instanceof XMLDocType) {\n          doc.children[i] = doctype;\n          return doctype;\n        }\n      }\n      ref2 = doc.children;\n      for (i = k = 0, len1 = ref2.length; k < len1; i = ++k) {\n        child = ref2[i];\n        if (child.isRoot) {\n          doc.children.splice(i, 0, doctype);\n          return doctype;\n        }\n      }\n      doc.children.push(doctype);\n      return doctype;\n    };\n\n    XMLNode.prototype.up = function() {\n      if (this.isRoot) {\n        throw new Error(\"The root node has no parent. Use doc() if you need to get the document object.\");\n      }\n      return this.parent;\n    };\n\n    XMLNode.prototype.root = function() {\n      var node;\n      node = this;\n      while (node) {\n        if (node.isDocument) {\n          return node.rootObject;\n        } else if (node.isRoot) {\n          return node;\n        } else {\n          node = node.parent;\n        }\n      }\n    };\n\n    XMLNode.prototype.document = function() {\n      var node;\n      node = this;\n      while (node) {\n        if (node.isDocument) {\n          return node;\n        } else {\n          node = node.parent;\n        }\n      }\n    };\n\n    XMLNode.prototype.end = function(options) {\n      return this.document().end(options);\n    };\n\n    XMLNode.prototype.prev = function() {\n      var i;\n      i = this.parent.children.indexOf(this);\n      while (i > 0 && this.parent.children[i - 1].isDummy) {\n        i = i - 1;\n      }\n      if (i < 1) {\n        throw new Error(\"Already at the first node. \" + this.debugInfo());\n      }\n      return this.parent.children[i - 1];\n    };\n\n    XMLNode.prototype.next = function() {\n      var i;\n      i = this.parent.children.indexOf(this);\n      while (i < this.parent.children.length - 1 && this.parent.children[i + 1].isDummy) {\n        i = i + 1;\n      }\n      if (i === -1 || i === this.parent.children.length - 1) {\n        throw new Error(\"Already at the last node. \" + this.debugInfo());\n      }\n      return this.parent.children[i + 1];\n    };\n\n    XMLNode.prototype.importDocument = function(doc) {\n      var clonedRoot;\n      clonedRoot = doc.root().clone();\n      clonedRoot.parent = this;\n      clonedRoot.isRoot = false;\n      this.children.push(clonedRoot);\n      return this;\n    };\n\n    XMLNode.prototype.debugInfo = function(name) {\n      var ref1, ref2;\n      name = name || this.name;\n      if ((name == null) && !((ref1 = this.parent) != null ? ref1.name : void 0)) {\n        return \"\";\n      } else if (name == null) {\n        return \"parent: <\" + this.parent.name + \">\";\n      } else if (!((ref2 = this.parent) != null ? ref2.name : void 0)) {\n        return \"node: <\" + name + \">\";\n      } else {\n        return \"node: <\" + name + \">, parent: <\" + this.parent.name + \">\";\n      }\n    };\n\n    XMLNode.prototype.ele = function(name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n\n    XMLNode.prototype.nod = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLNode.prototype.txt = function(value) {\n      return this.text(value);\n    };\n\n    XMLNode.prototype.dat = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLNode.prototype.com = function(value) {\n      return this.comment(value);\n    };\n\n    XMLNode.prototype.ins = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLNode.prototype.doc = function() {\n      return this.document();\n    };\n\n    XMLNode.prototype.dec = function(version, encoding, standalone) {\n      return this.declaration(version, encoding, standalone);\n    };\n\n    XMLNode.prototype.dtd = function(pubID, sysID) {\n      return this.doctype(pubID, sysID);\n    };\n\n    XMLNode.prototype.e = function(name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n\n    XMLNode.prototype.n = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLNode.prototype.t = function(value) {\n      return this.text(value);\n    };\n\n    XMLNode.prototype.d = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLNode.prototype.c = function(value) {\n      return this.comment(value);\n    };\n\n    XMLNode.prototype.r = function(value) {\n      return this.raw(value);\n    };\n\n    XMLNode.prototype.i = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLNode.prototype.u = function() {\n      return this.up();\n    };\n\n    XMLNode.prototype.importXMLBuilder = function(doc) {\n      return this.importDocument(doc);\n    };\n\n    return XMLNode;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js":
/*!*****************************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js ***!
  \*****************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLNode, XMLProcessingInstruction,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLProcessingInstruction = (function(superClass) {\n    extend(XMLProcessingInstruction, superClass);\n\n    function XMLProcessingInstruction(parent, target, value) {\n      XMLProcessingInstruction.__super__.constructor.call(this, parent);\n      if (target == null) {\n        throw new Error(\"Missing instruction target. \" + this.debugInfo());\n      }\n      this.target = this.stringify.insTarget(target);\n      if (value) {\n        this.value = this.stringify.insValue(value);\n      }\n    }\n\n    XMLProcessingInstruction.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLProcessingInstruction.prototype.toString = function(options) {\n      return this.options.writer.set(options).processingInstruction(this);\n    };\n\n    return XMLProcessingInstruction;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLRaw.js":
/*!***********************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLRaw.js ***!
  \***********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLNode, XMLRaw,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLRaw = (function(superClass) {\n    extend(XMLRaw, superClass);\n\n    function XMLRaw(parent, text) {\n      XMLRaw.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing raw text. \" + this.debugInfo());\n      }\n      this.value = this.stringify.raw(text);\n    }\n\n    XMLRaw.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLRaw.prototype.toString = function(options) {\n      return this.options.writer.set(options).raw(this);\n    };\n\n    return XMLRaw;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLRaw.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLStreamWriter.js":
/*!********************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLStreamWriter.js ***!
  \********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLCData, XMLComment, XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDeclaration, XMLDocType, XMLDummy, XMLElement, XMLProcessingInstruction, XMLRaw, XMLStreamWriter, XMLText, XMLWriterBase,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLDeclaration = __webpack_require__(/*! ./XMLDeclaration */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDeclaration.js\");\n\n  XMLDocType = __webpack_require__(/*! ./XMLDocType */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDocType.js\");\n\n  XMLCData = __webpack_require__(/*! ./XMLCData */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLCData.js\");\n\n  XMLComment = __webpack_require__(/*! ./XMLComment */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLComment.js\");\n\n  XMLElement = __webpack_require__(/*! ./XMLElement */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLElement.js\");\n\n  XMLRaw = __webpack_require__(/*! ./XMLRaw */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLRaw.js\");\n\n  XMLText = __webpack_require__(/*! ./XMLText */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLText.js\");\n\n  XMLProcessingInstruction = __webpack_require__(/*! ./XMLProcessingInstruction */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js\");\n\n  XMLDummy = __webpack_require__(/*! ./XMLDummy */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDummy.js\");\n\n  XMLDTDAttList = __webpack_require__(/*! ./XMLDTDAttList */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDAttList.js\");\n\n  XMLDTDElement = __webpack_require__(/*! ./XMLDTDElement */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDElement.js\");\n\n  XMLDTDEntity = __webpack_require__(/*! ./XMLDTDEntity */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDEntity.js\");\n\n  XMLDTDNotation = __webpack_require__(/*! ./XMLDTDNotation */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDNotation.js\");\n\n  XMLWriterBase = __webpack_require__(/*! ./XMLWriterBase */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLWriterBase.js\");\n\n  module.exports = XMLStreamWriter = (function(superClass) {\n    extend(XMLStreamWriter, superClass);\n\n    function XMLStreamWriter(stream, options) {\n      XMLStreamWriter.__super__.constructor.call(this, options);\n      this.stream = stream;\n    }\n\n    XMLStreamWriter.prototype.document = function(doc) {\n      var child, i, j, len, len1, ref, ref1, results;\n      ref = doc.children;\n      for (i = 0, len = ref.length; i < len; i++) {\n        child = ref[i];\n        child.isLastRootNode = false;\n      }\n      doc.children[doc.children.length - 1].isLastRootNode = true;\n      ref1 = doc.children;\n      results = [];\n      for (j = 0, len1 = ref1.length; j < len1; j++) {\n        child = ref1[j];\n        if (child instanceof XMLDummy) {\n          continue;\n        }\n        switch (false) {\n          case !(child instanceof XMLDeclaration):\n            results.push(this.declaration(child));\n            break;\n          case !(child instanceof XMLDocType):\n            results.push(this.docType(child));\n            break;\n          case !(child instanceof XMLComment):\n            results.push(this.comment(child));\n            break;\n          case !(child instanceof XMLProcessingInstruction):\n            results.push(this.processingInstruction(child));\n            break;\n          default:\n            results.push(this.element(child));\n        }\n      }\n      return results;\n    };\n\n    XMLStreamWriter.prototype.attribute = function(att) {\n      return this.stream.write(' ' + att.name + '=\"' + att.value + '\"');\n    };\n\n    XMLStreamWriter.prototype.cdata = function(node, level) {\n      return this.stream.write(this.space(level) + '<![CDATA[' + node.text + ']]>' + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.comment = function(node, level) {\n      return this.stream.write(this.space(level) + '<!-- ' + node.text + ' -->' + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.declaration = function(node, level) {\n      this.stream.write(this.space(level));\n      this.stream.write('<?xml version=\"' + node.version + '\"');\n      if (node.encoding != null) {\n        this.stream.write(' encoding=\"' + node.encoding + '\"');\n      }\n      if (node.standalone != null) {\n        this.stream.write(' standalone=\"' + node.standalone + '\"');\n      }\n      this.stream.write(this.spacebeforeslash + '?>');\n      return this.stream.write(this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.docType = function(node, level) {\n      var child, i, len, ref;\n      level || (level = 0);\n      this.stream.write(this.space(level));\n      this.stream.write('<!DOCTYPE ' + node.root().name);\n      if (node.pubID && node.sysID) {\n        this.stream.write(' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"');\n      } else if (node.sysID) {\n        this.stream.write(' SYSTEM \"' + node.sysID + '\"');\n      }\n      if (node.children.length > 0) {\n        this.stream.write(' [');\n        this.stream.write(this.endline(node));\n        ref = node.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          switch (false) {\n            case !(child instanceof XMLDTDAttList):\n              this.dtdAttList(child, level + 1);\n              break;\n            case !(child instanceof XMLDTDElement):\n              this.dtdElement(child, level + 1);\n              break;\n            case !(child instanceof XMLDTDEntity):\n              this.dtdEntity(child, level + 1);\n              break;\n            case !(child instanceof XMLDTDNotation):\n              this.dtdNotation(child, level + 1);\n              break;\n            case !(child instanceof XMLCData):\n              this.cdata(child, level + 1);\n              break;\n            case !(child instanceof XMLComment):\n              this.comment(child, level + 1);\n              break;\n            case !(child instanceof XMLProcessingInstruction):\n              this.processingInstruction(child, level + 1);\n              break;\n            default:\n              throw new Error(\"Unknown DTD node type: \" + child.constructor.name);\n          }\n        }\n        this.stream.write(']');\n      }\n      this.stream.write(this.spacebeforeslash + '>');\n      return this.stream.write(this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.element = function(node, level) {\n      var att, child, i, len, name, ref, ref1, space;\n      level || (level = 0);\n      space = this.space(level);\n      this.stream.write(space + '<' + node.name);\n      ref = node.attributes;\n      for (name in ref) {\n        if (!hasProp.call(ref, name)) continue;\n        att = ref[name];\n        this.attribute(att);\n      }\n      if (node.children.length === 0 || node.children.every(function(e) {\n        return e.value === '';\n      })) {\n        if (this.allowEmpty) {\n          this.stream.write('></' + node.name + '>');\n        } else {\n          this.stream.write(this.spacebeforeslash + '/>');\n        }\n      } else if (this.pretty && node.children.length === 1 && (node.children[0].value != null)) {\n        this.stream.write('>');\n        this.stream.write(node.children[0].value);\n        this.stream.write('</' + node.name + '>');\n      } else {\n        this.stream.write('>' + this.newline);\n        ref1 = node.children;\n        for (i = 0, len = ref1.length; i < len; i++) {\n          child = ref1[i];\n          switch (false) {\n            case !(child instanceof XMLCData):\n              this.cdata(child, level + 1);\n              break;\n            case !(child instanceof XMLComment):\n              this.comment(child, level + 1);\n              break;\n            case !(child instanceof XMLElement):\n              this.element(child, level + 1);\n              break;\n            case !(child instanceof XMLRaw):\n              this.raw(child, level + 1);\n              break;\n            case !(child instanceof XMLText):\n              this.text(child, level + 1);\n              break;\n            case !(child instanceof XMLProcessingInstruction):\n              this.processingInstruction(child, level + 1);\n              break;\n            case !(child instanceof XMLDummy):\n              '';\n              break;\n            default:\n              throw new Error(\"Unknown XML node type: \" + child.constructor.name);\n          }\n        }\n        this.stream.write(space + '</' + node.name + '>');\n      }\n      return this.stream.write(this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.processingInstruction = function(node, level) {\n      this.stream.write(this.space(level) + '<?' + node.target);\n      if (node.value) {\n        this.stream.write(' ' + node.value);\n      }\n      return this.stream.write(this.spacebeforeslash + '?>' + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.raw = function(node, level) {\n      return this.stream.write(this.space(level) + node.value + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.text = function(node, level) {\n      return this.stream.write(this.space(level) + node.value + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.dtdAttList = function(node, level) {\n      this.stream.write(this.space(level) + '<!ATTLIST ' + node.elementName + ' ' + node.attributeName + ' ' + node.attributeType);\n      if (node.defaultValueType !== '#DEFAULT') {\n        this.stream.write(' ' + node.defaultValueType);\n      }\n      if (node.defaultValue) {\n        this.stream.write(' \"' + node.defaultValue + '\"');\n      }\n      return this.stream.write(this.spacebeforeslash + '>' + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.dtdElement = function(node, level) {\n      this.stream.write(this.space(level) + '<!ELEMENT ' + node.name + ' ' + node.value);\n      return this.stream.write(this.spacebeforeslash + '>' + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.dtdEntity = function(node, level) {\n      this.stream.write(this.space(level) + '<!ENTITY');\n      if (node.pe) {\n        this.stream.write(' %');\n      }\n      this.stream.write(' ' + node.name);\n      if (node.value) {\n        this.stream.write(' \"' + node.value + '\"');\n      } else {\n        if (node.pubID && node.sysID) {\n          this.stream.write(' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"');\n        } else if (node.sysID) {\n          this.stream.write(' SYSTEM \"' + node.sysID + '\"');\n        }\n        if (node.nData) {\n          this.stream.write(' NDATA ' + node.nData);\n        }\n      }\n      return this.stream.write(this.spacebeforeslash + '>' + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.dtdNotation = function(node, level) {\n      this.stream.write(this.space(level) + '<!NOTATION ' + node.name);\n      if (node.pubID && node.sysID) {\n        this.stream.write(' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"');\n      } else if (node.pubID) {\n        this.stream.write(' PUBLIC \"' + node.pubID + '\"');\n      } else if (node.sysID) {\n        this.stream.write(' SYSTEM \"' + node.sysID + '\"');\n      }\n      return this.stream.write(this.spacebeforeslash + '>' + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.endline = function(node) {\n      if (!node.isLastRootNode) {\n        return this.newline;\n      } else {\n        return '';\n      }\n    };\n\n    return XMLStreamWriter;\n\n  })(XMLWriterBase);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLStreamWriter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLStringWriter.js":
/*!********************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLStringWriter.js ***!
  \********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLCData, XMLComment, XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDeclaration, XMLDocType, XMLDummy, XMLElement, XMLProcessingInstruction, XMLRaw, XMLStringWriter, XMLText, XMLWriterBase,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLDeclaration = __webpack_require__(/*! ./XMLDeclaration */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDeclaration.js\");\n\n  XMLDocType = __webpack_require__(/*! ./XMLDocType */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDocType.js\");\n\n  XMLCData = __webpack_require__(/*! ./XMLCData */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLCData.js\");\n\n  XMLComment = __webpack_require__(/*! ./XMLComment */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLComment.js\");\n\n  XMLElement = __webpack_require__(/*! ./XMLElement */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLElement.js\");\n\n  XMLRaw = __webpack_require__(/*! ./XMLRaw */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLRaw.js\");\n\n  XMLText = __webpack_require__(/*! ./XMLText */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLText.js\");\n\n  XMLProcessingInstruction = __webpack_require__(/*! ./XMLProcessingInstruction */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js\");\n\n  XMLDummy = __webpack_require__(/*! ./XMLDummy */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDummy.js\");\n\n  XMLDTDAttList = __webpack_require__(/*! ./XMLDTDAttList */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDAttList.js\");\n\n  XMLDTDElement = __webpack_require__(/*! ./XMLDTDElement */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDElement.js\");\n\n  XMLDTDEntity = __webpack_require__(/*! ./XMLDTDEntity */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDEntity.js\");\n\n  XMLDTDNotation = __webpack_require__(/*! ./XMLDTDNotation */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDNotation.js\");\n\n  XMLWriterBase = __webpack_require__(/*! ./XMLWriterBase */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLWriterBase.js\");\n\n  module.exports = XMLStringWriter = (function(superClass) {\n    extend(XMLStringWriter, superClass);\n\n    function XMLStringWriter(options) {\n      XMLStringWriter.__super__.constructor.call(this, options);\n    }\n\n    XMLStringWriter.prototype.document = function(doc) {\n      var child, i, len, r, ref;\n      this.textispresent = false;\n      r = '';\n      ref = doc.children;\n      for (i = 0, len = ref.length; i < len; i++) {\n        child = ref[i];\n        if (child instanceof XMLDummy) {\n          continue;\n        }\n        r += (function() {\n          switch (false) {\n            case !(child instanceof XMLDeclaration):\n              return this.declaration(child);\n            case !(child instanceof XMLDocType):\n              return this.docType(child);\n            case !(child instanceof XMLComment):\n              return this.comment(child);\n            case !(child instanceof XMLProcessingInstruction):\n              return this.processingInstruction(child);\n            default:\n              return this.element(child, 0);\n          }\n        }).call(this);\n      }\n      if (this.pretty && r.slice(-this.newline.length) === this.newline) {\n        r = r.slice(0, -this.newline.length);\n      }\n      return r;\n    };\n\n    XMLStringWriter.prototype.attribute = function(att) {\n      return ' ' + att.name + '=\"' + att.value + '\"';\n    };\n\n    XMLStringWriter.prototype.cdata = function(node, level) {\n      return this.space(level) + '<![CDATA[' + node.text + ']]>' + this.newline;\n    };\n\n    XMLStringWriter.prototype.comment = function(node, level) {\n      return this.space(level) + '<!-- ' + node.text + ' -->' + this.newline;\n    };\n\n    XMLStringWriter.prototype.declaration = function(node, level) {\n      var r;\n      r = this.space(level);\n      r += '<?xml version=\"' + node.version + '\"';\n      if (node.encoding != null) {\n        r += ' encoding=\"' + node.encoding + '\"';\n      }\n      if (node.standalone != null) {\n        r += ' standalone=\"' + node.standalone + '\"';\n      }\n      r += this.spacebeforeslash + '?>';\n      r += this.newline;\n      return r;\n    };\n\n    XMLStringWriter.prototype.docType = function(node, level) {\n      var child, i, len, r, ref;\n      level || (level = 0);\n      r = this.space(level);\n      r += '<!DOCTYPE ' + node.root().name;\n      if (node.pubID && node.sysID) {\n        r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n      } else if (node.sysID) {\n        r += ' SYSTEM \"' + node.sysID + '\"';\n      }\n      if (node.children.length > 0) {\n        r += ' [';\n        r += this.newline;\n        ref = node.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          r += (function() {\n            switch (false) {\n              case !(child instanceof XMLDTDAttList):\n                return this.dtdAttList(child, level + 1);\n              case !(child instanceof XMLDTDElement):\n                return this.dtdElement(child, level + 1);\n              case !(child instanceof XMLDTDEntity):\n                return this.dtdEntity(child, level + 1);\n              case !(child instanceof XMLDTDNotation):\n                return this.dtdNotation(child, level + 1);\n              case !(child instanceof XMLCData):\n                return this.cdata(child, level + 1);\n              case !(child instanceof XMLComment):\n                return this.comment(child, level + 1);\n              case !(child instanceof XMLProcessingInstruction):\n                return this.processingInstruction(child, level + 1);\n              default:\n                throw new Error(\"Unknown DTD node type: \" + child.constructor.name);\n            }\n          }).call(this);\n        }\n        r += ']';\n      }\n      r += this.spacebeforeslash + '>';\n      r += this.newline;\n      return r;\n    };\n\n    XMLStringWriter.prototype.element = function(node, level) {\n      var att, child, i, j, len, len1, name, r, ref, ref1, ref2, space, textispresentwasset;\n      level || (level = 0);\n      textispresentwasset = false;\n      if (this.textispresent) {\n        this.newline = '';\n        this.pretty = false;\n      } else {\n        this.newline = this.newlinedefault;\n        this.pretty = this.prettydefault;\n      }\n      space = this.space(level);\n      r = '';\n      r += space + '<' + node.name;\n      ref = node.attributes;\n      for (name in ref) {\n        if (!hasProp.call(ref, name)) continue;\n        att = ref[name];\n        r += this.attribute(att);\n      }\n      if (node.children.length === 0 || node.children.every(function(e) {\n        return e.value === '';\n      })) {\n        if (this.allowEmpty) {\n          r += '></' + node.name + '>' + this.newline;\n        } else {\n          r += this.spacebeforeslash + '/>' + this.newline;\n        }\n      } else if (this.pretty && node.children.length === 1 && (node.children[0].value != null)) {\n        r += '>';\n        r += node.children[0].value;\n        r += '</' + node.name + '>' + this.newline;\n      } else {\n        if (this.dontprettytextnodes) {\n          ref1 = node.children;\n          for (i = 0, len = ref1.length; i < len; i++) {\n            child = ref1[i];\n            if (child.value != null) {\n              this.textispresent++;\n              textispresentwasset = true;\n              break;\n            }\n          }\n        }\n        if (this.textispresent) {\n          this.newline = '';\n          this.pretty = false;\n          space = this.space(level);\n        }\n        r += '>' + this.newline;\n        ref2 = node.children;\n        for (j = 0, len1 = ref2.length; j < len1; j++) {\n          child = ref2[j];\n          r += (function() {\n            switch (false) {\n              case !(child instanceof XMLCData):\n                return this.cdata(child, level + 1);\n              case !(child instanceof XMLComment):\n                return this.comment(child, level + 1);\n              case !(child instanceof XMLElement):\n                return this.element(child, level + 1);\n              case !(child instanceof XMLRaw):\n                return this.raw(child, level + 1);\n              case !(child instanceof XMLText):\n                return this.text(child, level + 1);\n              case !(child instanceof XMLProcessingInstruction):\n                return this.processingInstruction(child, level + 1);\n              case !(child instanceof XMLDummy):\n                return '';\n              default:\n                throw new Error(\"Unknown XML node type: \" + child.constructor.name);\n            }\n          }).call(this);\n        }\n        if (textispresentwasset) {\n          this.textispresent--;\n        }\n        if (!this.textispresent) {\n          this.newline = this.newlinedefault;\n          this.pretty = this.prettydefault;\n        }\n        r += space + '</' + node.name + '>' + this.newline;\n      }\n      return r;\n    };\n\n    XMLStringWriter.prototype.processingInstruction = function(node, level) {\n      var r;\n      r = this.space(level) + '<?' + node.target;\n      if (node.value) {\n        r += ' ' + node.value;\n      }\n      r += this.spacebeforeslash + '?>' + this.newline;\n      return r;\n    };\n\n    XMLStringWriter.prototype.raw = function(node, level) {\n      return this.space(level) + node.value + this.newline;\n    };\n\n    XMLStringWriter.prototype.text = function(node, level) {\n      return this.space(level) + node.value + this.newline;\n    };\n\n    XMLStringWriter.prototype.dtdAttList = function(node, level) {\n      var r;\n      r = this.space(level) + '<!ATTLIST ' + node.elementName + ' ' + node.attributeName + ' ' + node.attributeType;\n      if (node.defaultValueType !== '#DEFAULT') {\n        r += ' ' + node.defaultValueType;\n      }\n      if (node.defaultValue) {\n        r += ' \"' + node.defaultValue + '\"';\n      }\n      r += this.spacebeforeslash + '>' + this.newline;\n      return r;\n    };\n\n    XMLStringWriter.prototype.dtdElement = function(node, level) {\n      return this.space(level) + '<!ELEMENT ' + node.name + ' ' + node.value + this.spacebeforeslash + '>' + this.newline;\n    };\n\n    XMLStringWriter.prototype.dtdEntity = function(node, level) {\n      var r;\n      r = this.space(level) + '<!ENTITY';\n      if (node.pe) {\n        r += ' %';\n      }\n      r += ' ' + node.name;\n      if (node.value) {\n        r += ' \"' + node.value + '\"';\n      } else {\n        if (node.pubID && node.sysID) {\n          r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n        } else if (node.sysID) {\n          r += ' SYSTEM \"' + node.sysID + '\"';\n        }\n        if (node.nData) {\n          r += ' NDATA ' + node.nData;\n        }\n      }\n      r += this.spacebeforeslash + '>' + this.newline;\n      return r;\n    };\n\n    XMLStringWriter.prototype.dtdNotation = function(node, level) {\n      var r;\n      r = this.space(level) + '<!NOTATION ' + node.name;\n      if (node.pubID && node.sysID) {\n        r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n      } else if (node.pubID) {\n        r += ' PUBLIC \"' + node.pubID + '\"';\n      } else if (node.sysID) {\n        r += ' SYSTEM \"' + node.sysID + '\"';\n      }\n      r += this.spacebeforeslash + '>' + this.newline;\n      return r;\n    };\n\n    XMLStringWriter.prototype.openNode = function(node, level) {\n      var att, name, r, ref;\n      level || (level = 0);\n      if (node instanceof XMLElement) {\n        r = this.space(level) + '<' + node.name;\n        ref = node.attributes;\n        for (name in ref) {\n          if (!hasProp.call(ref, name)) continue;\n          att = ref[name];\n          r += this.attribute(att);\n        }\n        r += (node.children ? '>' : '/>') + this.newline;\n        return r;\n      } else {\n        r = this.space(level) + '<!DOCTYPE ' + node.rootNodeName;\n        if (node.pubID && node.sysID) {\n          r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n        } else if (node.sysID) {\n          r += ' SYSTEM \"' + node.sysID + '\"';\n        }\n        r += (node.children ? ' [' : '>') + this.newline;\n        return r;\n      }\n    };\n\n    XMLStringWriter.prototype.closeNode = function(node, level) {\n      level || (level = 0);\n      switch (false) {\n        case !(node instanceof XMLElement):\n          return this.space(level) + '</' + node.name + '>' + this.newline;\n        case !(node instanceof XMLDocType):\n          return this.space(level) + ']>' + this.newline;\n      }\n    };\n\n    return XMLStringWriter;\n\n  })(XMLWriterBase);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLStringWriter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLStringifier.js":
/*!*******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLStringifier.js ***!
  \*******************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLStringifier,\n    bind = function(fn, me){ return function(){ return fn.apply(me, arguments); }; },\n    hasProp = {}.hasOwnProperty;\n\n  module.exports = XMLStringifier = (function() {\n    function XMLStringifier(options) {\n      this.assertLegalChar = bind(this.assertLegalChar, this);\n      var key, ref, value;\n      options || (options = {});\n      this.noDoubleEncoding = options.noDoubleEncoding;\n      ref = options.stringify || {};\n      for (key in ref) {\n        if (!hasProp.call(ref, key)) continue;\n        value = ref[key];\n        this[key] = value;\n      }\n    }\n\n    XMLStringifier.prototype.eleName = function(val) {\n      val = '' + val || '';\n      return this.assertLegalChar(val);\n    };\n\n    XMLStringifier.prototype.eleText = function(val) {\n      val = '' + val || '';\n      return this.assertLegalChar(this.elEscape(val));\n    };\n\n    XMLStringifier.prototype.cdata = function(val) {\n      val = '' + val || '';\n      val = val.replace(']]>', ']]]]><![CDATA[>');\n      return this.assertLegalChar(val);\n    };\n\n    XMLStringifier.prototype.comment = function(val) {\n      val = '' + val || '';\n      if (val.match(/--/)) {\n        throw new Error(\"Comment text cannot contain double-hypen: \" + val);\n      }\n      return this.assertLegalChar(val);\n    };\n\n    XMLStringifier.prototype.raw = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.attName = function(val) {\n      return val = '' + val || '';\n    };\n\n    XMLStringifier.prototype.attValue = function(val) {\n      val = '' + val || '';\n      return this.attEscape(val);\n    };\n\n    XMLStringifier.prototype.insTarget = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.insValue = function(val) {\n      val = '' + val || '';\n      if (val.match(/\\?>/)) {\n        throw new Error(\"Invalid processing instruction value: \" + val);\n      }\n      return val;\n    };\n\n    XMLStringifier.prototype.xmlVersion = function(val) {\n      val = '' + val || '';\n      if (!val.match(/1\\.[0-9]+/)) {\n        throw new Error(\"Invalid version number: \" + val);\n      }\n      return val;\n    };\n\n    XMLStringifier.prototype.xmlEncoding = function(val) {\n      val = '' + val || '';\n      if (!val.match(/^[A-Za-z](?:[A-Za-z0-9._-])*$/)) {\n        throw new Error(\"Invalid encoding: \" + val);\n      }\n      return val;\n    };\n\n    XMLStringifier.prototype.xmlStandalone = function(val) {\n      if (val) {\n        return \"yes\";\n      } else {\n        return \"no\";\n      }\n    };\n\n    XMLStringifier.prototype.dtdPubID = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.dtdSysID = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.dtdElementValue = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.dtdAttType = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.dtdAttDefault = function(val) {\n      if (val != null) {\n        return '' + val || '';\n      } else {\n        return val;\n      }\n    };\n\n    XMLStringifier.prototype.dtdEntityValue = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.dtdNData = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.convertAttKey = '@';\n\n    XMLStringifier.prototype.convertPIKey = '?';\n\n    XMLStringifier.prototype.convertTextKey = '#text';\n\n    XMLStringifier.prototype.convertCDataKey = '#cdata';\n\n    XMLStringifier.prototype.convertCommentKey = '#comment';\n\n    XMLStringifier.prototype.convertRawKey = '#raw';\n\n    XMLStringifier.prototype.assertLegalChar = function(str) {\n      var res;\n      res = str.match(/[\\0\\uFFFE\\uFFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]/);\n      if (res) {\n        throw new Error(\"Invalid character in string: \" + str + \" at index \" + res.index);\n      }\n      return str;\n    };\n\n    XMLStringifier.prototype.elEscape = function(str) {\n      var ampregex;\n      ampregex = this.noDoubleEncoding ? /(?!&\\S+;)&/g : /&/g;\n      return str.replace(ampregex, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\\r/g, '&#xD;');\n    };\n\n    XMLStringifier.prototype.attEscape = function(str) {\n      var ampregex;\n      ampregex = this.noDoubleEncoding ? /(?!&\\S+;)&/g : /&/g;\n      return str.replace(ampregex, '&amp;').replace(/</g, '&lt;').replace(/\"/g, '&quot;').replace(/\\t/g, '&#x9;').replace(/\\n/g, '&#xA;').replace(/\\r/g, '&#xD;');\n    };\n\n    return XMLStringifier;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLStringifier.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLText.js":
/*!************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLText.js ***!
  \************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLNode, XMLText,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLText = (function(superClass) {\n    extend(XMLText, superClass);\n\n    function XMLText(parent, text) {\n      XMLText.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing element text. \" + this.debugInfo());\n      }\n      this.value = this.stringify.eleText(text);\n    }\n\n    XMLText.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLText.prototype.toString = function(options) {\n      return this.options.writer.set(options).text(this);\n    };\n\n    return XMLText;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLText.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLWriterBase.js":
/*!******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLWriterBase.js ***!
  \******************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLWriterBase,\n    hasProp = {}.hasOwnProperty;\n\n  module.exports = XMLWriterBase = (function() {\n    function XMLWriterBase(options) {\n      var key, ref, ref1, ref2, ref3, ref4, ref5, ref6, value;\n      options || (options = {});\n      this.pretty = options.pretty || false;\n      this.allowEmpty = (ref = options.allowEmpty) != null ? ref : false;\n      if (this.pretty) {\n        this.indent = (ref1 = options.indent) != null ? ref1 : '  ';\n        this.newline = (ref2 = options.newline) != null ? ref2 : '\\n';\n        this.offset = (ref3 = options.offset) != null ? ref3 : 0;\n        this.dontprettytextnodes = (ref4 = options.dontprettytextnodes) != null ? ref4 : 0;\n      } else {\n        this.indent = '';\n        this.newline = '';\n        this.offset = 0;\n        this.dontprettytextnodes = 0;\n      }\n      this.spacebeforeslash = (ref5 = options.spacebeforeslash) != null ? ref5 : '';\n      if (this.spacebeforeslash === true) {\n        this.spacebeforeslash = ' ';\n      }\n      this.newlinedefault = this.newline;\n      this.prettydefault = this.pretty;\n      ref6 = options.writer || {};\n      for (key in ref6) {\n        if (!hasProp.call(ref6, key)) continue;\n        value = ref6[key];\n        this[key] = value;\n      }\n    }\n\n    XMLWriterBase.prototype.set = function(options) {\n      var key, ref, value;\n      options || (options = {});\n      if (\"pretty\" in options) {\n        this.pretty = options.pretty;\n      }\n      if (\"allowEmpty\" in options) {\n        this.allowEmpty = options.allowEmpty;\n      }\n      if (this.pretty) {\n        this.indent = \"indent\" in options ? options.indent : '  ';\n        this.newline = \"newline\" in options ? options.newline : '\\n';\n        this.offset = \"offset\" in options ? options.offset : 0;\n        this.dontprettytextnodes = \"dontprettytextnodes\" in options ? options.dontprettytextnodes : 0;\n      } else {\n        this.indent = '';\n        this.newline = '';\n        this.offset = 0;\n        this.dontprettytextnodes = 0;\n      }\n      this.spacebeforeslash = \"spacebeforeslash\" in options ? options.spacebeforeslash : '';\n      if (this.spacebeforeslash === true) {\n        this.spacebeforeslash = ' ';\n      }\n      this.newlinedefault = this.newline;\n      this.prettydefault = this.pretty;\n      ref = options.writer || {};\n      for (key in ref) {\n        if (!hasProp.call(ref, key)) continue;\n        value = ref[key];\n        this[key] = value;\n      }\n      return this;\n    };\n\n    XMLWriterBase.prototype.space = function(level) {\n      var indent;\n      if (this.pretty) {\n        indent = (level || 0) + this.offset + 1;\n        if (indent > 0) {\n          return new Array(indent).join(this.indent);\n        } else {\n          return '';\n        }\n      } else {\n        return '';\n      }\n    };\n\n    return XMLWriterBase;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLWriterBase.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/index.js":
/*!**********************************************!*\
  !*** ./node_modules/xmlbuilder/lib/index.js ***!
  \**********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDocument, XMLDocumentCB, XMLStreamWriter, XMLStringWriter, assign, isFunction, ref;\n\n  ref = __webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/xmlbuilder/lib/Utility.js\"), assign = ref.assign, isFunction = ref.isFunction;\n\n  XMLDocument = __webpack_require__(/*! ./XMLDocument */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDocument.js\");\n\n  XMLDocumentCB = __webpack_require__(/*! ./XMLDocumentCB */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDocumentCB.js\");\n\n  XMLStringWriter = __webpack_require__(/*! ./XMLStringWriter */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLStringWriter.js\");\n\n  XMLStreamWriter = __webpack_require__(/*! ./XMLStreamWriter */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLStreamWriter.js\");\n\n  module.exports.create = function(name, xmldec, doctype, options) {\n    var doc, root;\n    if (name == null) {\n      throw new Error(\"Root element needs a name.\");\n    }\n    options = assign({}, xmldec, doctype, options);\n    doc = new XMLDocument(options);\n    root = doc.element(name);\n    if (!options.headless) {\n      doc.declaration(options);\n      if ((options.pubID != null) || (options.sysID != null)) {\n        doc.doctype(options);\n      }\n    }\n    return root;\n  };\n\n  module.exports.begin = function(options, onData, onEnd) {\n    var ref1;\n    if (isFunction(options)) {\n      ref1 = [options, onData], onData = ref1[0], onEnd = ref1[1];\n      options = {};\n    }\n    if (onData) {\n      return new XMLDocumentCB(options, onData, onEnd);\n    } else {\n      return new XMLDocument(options);\n    }\n  };\n\n  module.exports.stringWriter = function(options) {\n    return new XMLStringWriter(options);\n  };\n\n  module.exports.streamWriter = function(stream, options) {\n    return new XMLStreamWriter(stream, options);\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/index.js\n");

/***/ })

};
;