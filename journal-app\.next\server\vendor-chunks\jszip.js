"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jszip";
exports.ids = ["vendor-chunks/jszip"];
exports.modules = {

/***/ "(ssr)/./node_modules/jszip/lib/base64.js":
/*!******************************************!*\
  !*** ./node_modules/jszip/lib/base64.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\n// private property\nvar _keyStr = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\n\n\n// public method for encoding\nexports.encode = function(input) {\n    var output = [];\n    var chr1, chr2, chr3, enc1, enc2, enc3, enc4;\n    var i = 0, len = input.length, remainingBytes = len;\n\n    var isArray = utils.getTypeOf(input) !== \"string\";\n    while (i < input.length) {\n        remainingBytes = len - i;\n\n        if (!isArray) {\n            chr1 = input.charCodeAt(i++);\n            chr2 = i < len ? input.charCodeAt(i++) : 0;\n            chr3 = i < len ? input.charCodeAt(i++) : 0;\n        } else {\n            chr1 = input[i++];\n            chr2 = i < len ? input[i++] : 0;\n            chr3 = i < len ? input[i++] : 0;\n        }\n\n        enc1 = chr1 >> 2;\n        enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);\n        enc3 = remainingBytes > 1 ? (((chr2 & 15) << 2) | (chr3 >> 6)) : 64;\n        enc4 = remainingBytes > 2 ? (chr3 & 63) : 64;\n\n        output.push(_keyStr.charAt(enc1) + _keyStr.charAt(enc2) + _keyStr.charAt(enc3) + _keyStr.charAt(enc4));\n\n    }\n\n    return output.join(\"\");\n};\n\n// public method for decoding\nexports.decode = function(input) {\n    var chr1, chr2, chr3;\n    var enc1, enc2, enc3, enc4;\n    var i = 0, resultIndex = 0;\n\n    var dataUrlPrefix = \"data:\";\n\n    if (input.substr(0, dataUrlPrefix.length) === dataUrlPrefix) {\n        // This is a common error: people give a data url\n        // (data:image/png;base64,iVBOR...) with a {base64: true} and\n        // wonders why things don't work.\n        // We can detect that the string input looks like a data url but we\n        // *can't* be sure it is one: removing everything up to the comma would\n        // be too dangerous.\n        throw new Error(\"Invalid base64 input, it looks like a data url.\");\n    }\n\n    input = input.replace(/[^A-Za-z0-9+/=]/g, \"\");\n\n    var totalLength = input.length * 3 / 4;\n    if(input.charAt(input.length - 1) === _keyStr.charAt(64)) {\n        totalLength--;\n    }\n    if(input.charAt(input.length - 2) === _keyStr.charAt(64)) {\n        totalLength--;\n    }\n    if (totalLength % 1 !== 0) {\n        // totalLength is not an integer, the length does not match a valid\n        // base64 content. That can happen if:\n        // - the input is not a base64 content\n        // - the input is *almost* a base64 content, with a extra chars at the\n        //   beginning or at the end\n        // - the input uses a base64 variant (base64url for example)\n        throw new Error(\"Invalid base64 input, bad content length.\");\n    }\n    var output;\n    if (support.uint8array) {\n        output = new Uint8Array(totalLength|0);\n    } else {\n        output = new Array(totalLength|0);\n    }\n\n    while (i < input.length) {\n\n        enc1 = _keyStr.indexOf(input.charAt(i++));\n        enc2 = _keyStr.indexOf(input.charAt(i++));\n        enc3 = _keyStr.indexOf(input.charAt(i++));\n        enc4 = _keyStr.indexOf(input.charAt(i++));\n\n        chr1 = (enc1 << 2) | (enc2 >> 4);\n        chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);\n        chr3 = ((enc3 & 3) << 6) | enc4;\n\n        output[resultIndex++] = chr1;\n\n        if (enc3 !== 64) {\n            output[resultIndex++] = chr2;\n        }\n        if (enc4 !== 64) {\n            output[resultIndex++] = chr3;\n        }\n\n    }\n\n    return output;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/base64.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/compressedObject.js":
/*!****************************************************!*\
  !*** ./node_modules/jszip/lib/compressedObject.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar external = __webpack_require__(/*! ./external */ \"(ssr)/./node_modules/jszip/lib/external.js\");\nvar DataWorker = __webpack_require__(/*! ./stream/DataWorker */ \"(ssr)/./node_modules/jszip/lib/stream/DataWorker.js\");\nvar Crc32Probe = __webpack_require__(/*! ./stream/Crc32Probe */ \"(ssr)/./node_modules/jszip/lib/stream/Crc32Probe.js\");\nvar DataLengthProbe = __webpack_require__(/*! ./stream/DataLengthProbe */ \"(ssr)/./node_modules/jszip/lib/stream/DataLengthProbe.js\");\n\n/**\n * Represent a compressed object, with everything needed to decompress it.\n * @constructor\n * @param {number} compressedSize the size of the data compressed.\n * @param {number} uncompressedSize the size of the data after decompression.\n * @param {number} crc32 the crc32 of the decompressed file.\n * @param {object} compression the type of compression, see lib/compressions.js.\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the compressed data.\n */\nfunction CompressedObject(compressedSize, uncompressedSize, crc32, compression, data) {\n    this.compressedSize = compressedSize;\n    this.uncompressedSize = uncompressedSize;\n    this.crc32 = crc32;\n    this.compression = compression;\n    this.compressedContent = data;\n}\n\nCompressedObject.prototype = {\n    /**\n     * Create a worker to get the uncompressed content.\n     * @return {GenericWorker} the worker.\n     */\n    getContentWorker: function () {\n        var worker = new DataWorker(external.Promise.resolve(this.compressedContent))\n            .pipe(this.compression.uncompressWorker())\n            .pipe(new DataLengthProbe(\"data_length\"));\n\n        var that = this;\n        worker.on(\"end\", function () {\n            if (this.streamInfo[\"data_length\"] !== that.uncompressedSize) {\n                throw new Error(\"Bug : uncompressed data size mismatch\");\n            }\n        });\n        return worker;\n    },\n    /**\n     * Create a worker to get the compressed content.\n     * @return {GenericWorker} the worker.\n     */\n    getCompressedWorker: function () {\n        return new DataWorker(external.Promise.resolve(this.compressedContent))\n            .withStreamInfo(\"compressedSize\", this.compressedSize)\n            .withStreamInfo(\"uncompressedSize\", this.uncompressedSize)\n            .withStreamInfo(\"crc32\", this.crc32)\n            .withStreamInfo(\"compression\", this.compression)\n        ;\n    }\n};\n\n/**\n * Chain the given worker with other workers to compress the content with the\n * given compression.\n * @param {GenericWorker} uncompressedWorker the worker to pipe.\n * @param {Object} compression the compression object.\n * @param {Object} compressionOptions the options to use when compressing.\n * @return {GenericWorker} the new worker compressing the content.\n */\nCompressedObject.createWorkerFrom = function (uncompressedWorker, compression, compressionOptions) {\n    return uncompressedWorker\n        .pipe(new Crc32Probe())\n        .pipe(new DataLengthProbe(\"uncompressedSize\"))\n        .pipe(compression.compressWorker(compressionOptions))\n        .pipe(new DataLengthProbe(\"compressedSize\"))\n        .withStreamInfo(\"compression\", compression);\n};\n\nmodule.exports = CompressedObject;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/compressedObject.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/compressions.js":
/*!************************************************!*\
  !*** ./node_modules/jszip/lib/compressions.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\n\nexports.STORE = {\n    magic: \"\\x00\\x00\",\n    compressWorker : function () {\n        return new GenericWorker(\"STORE compression\");\n    },\n    uncompressWorker : function () {\n        return new GenericWorker(\"STORE decompression\");\n    }\n};\nexports.DEFLATE = __webpack_require__(/*! ./flate */ \"(ssr)/./node_modules/jszip/lib/flate.js\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL2NvbXByZXNzaW9ucy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixvQkFBb0IsbUJBQU8sQ0FBQyxzRkFBd0I7O0FBRXBELGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrRkFBb0MiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xceHlpZW5cXE9uZURyaXZlXFxEZXNrdG9wXFxBbGxKb3VybmFsXFxqb3VybmFsLWFwcFxcbm9kZV9tb2R1bGVzXFxqc3ppcFxcbGliXFxjb21wcmVzc2lvbnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbnZhciBHZW5lcmljV29ya2VyID0gcmVxdWlyZShcIi4vc3RyZWFtL0dlbmVyaWNXb3JrZXJcIik7XG5cbmV4cG9ydHMuU1RPUkUgPSB7XG4gICAgbWFnaWM6IFwiXFx4MDBcXHgwMFwiLFxuICAgIGNvbXByZXNzV29ya2VyIDogZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gbmV3IEdlbmVyaWNXb3JrZXIoXCJTVE9SRSBjb21wcmVzc2lvblwiKTtcbiAgICB9LFxuICAgIHVuY29tcHJlc3NXb3JrZXIgOiBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiBuZXcgR2VuZXJpY1dvcmtlcihcIlNUT1JFIGRlY29tcHJlc3Npb25cIik7XG4gICAgfVxufTtcbmV4cG9ydHMuREVGTEFURSA9IHJlcXVpcmUoXCIuL2ZsYXRlXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/compressions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/crc32.js":
/*!*****************************************!*\
  !*** ./node_modules/jszip/lib/crc32.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\n\n/**\n * The following functions come from pako, from pako/lib/zlib/crc32.js\n * released under the MIT license, see pako https://github.com/nodeca/pako/\n */\n\n// Use ordinary array, since untyped makes no boost here\nfunction makeTable() {\n    var c, table = [];\n\n    for(var n =0; n < 256; n++){\n        c = n;\n        for(var k =0; k < 8; k++){\n            c = ((c&1) ? (0xEDB88320 ^ (c >>> 1)) : (c >>> 1));\n        }\n        table[n] = c;\n    }\n\n    return table;\n}\n\n// Create table on load. Just 255 signed longs. Not a problem.\nvar crcTable = makeTable();\n\n\nfunction crc32(crc, buf, len, pos) {\n    var t = crcTable, end = pos + len;\n\n    crc = crc ^ (-1);\n\n    for (var i = pos; i < end; i++ ) {\n        crc = (crc >>> 8) ^ t[(crc ^ buf[i]) & 0xFF];\n    }\n\n    return (crc ^ (-1)); // >>> 0;\n}\n\n// That's all for the pako functions.\n\n/**\n * Compute the crc32 of a string.\n * This is almost the same as the function crc32, but for strings. Using the\n * same function for the two use cases leads to horrible performances.\n * @param {Number} crc the starting value of the crc.\n * @param {String} str the string to use.\n * @param {Number} len the length of the string.\n * @param {Number} pos the starting position for the crc32 computation.\n * @return {Number} the computed crc32.\n */\nfunction crc32str(crc, str, len, pos) {\n    var t = crcTable, end = pos + len;\n\n    crc = crc ^ (-1);\n\n    for (var i = pos; i < end; i++ ) {\n        crc = (crc >>> 8) ^ t[(crc ^ str.charCodeAt(i)) & 0xFF];\n    }\n\n    return (crc ^ (-1)); // >>> 0;\n}\n\nmodule.exports = function crc32wrapper(input, crc) {\n    if (typeof input === \"undefined\" || !input.length) {\n        return 0;\n    }\n\n    var isArray = utils.getTypeOf(input) !== \"string\";\n\n    if(isArray) {\n        return crc32(crc|0, input, input.length, 0);\n    } else {\n        return crc32str(crc|0, input, input.length, 0);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/crc32.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/defaults.js":
/*!********************************************!*\
  !*** ./node_modules/jszip/lib/defaults.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nexports.base64 = false;\nexports.binary = false;\nexports.dir = false;\nexports.createFolders = true;\nexports.date = null;\nexports.compression = null;\nexports.compressionOptions = null;\nexports.comment = null;\nexports.unixPermissions = null;\nexports.dosPermissions = null;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL2RlZmF1bHRzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsY0FBYztBQUNkLGNBQWM7QUFDZCxXQUFXO0FBQ1gscUJBQXFCO0FBQ3JCLFlBQVk7QUFDWixtQkFBbUI7QUFDbkIsMEJBQTBCO0FBQzFCLGVBQWU7QUFDZix1QkFBdUI7QUFDdkIsc0JBQXNCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHh5aWVuXFxPbmVEcml2ZVxcRGVza3RvcFxcQWxsSm91cm5hbFxcam91cm5hbC1hcHBcXG5vZGVfbW9kdWxlc1xcanN6aXBcXGxpYlxcZGVmYXVsdHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5leHBvcnRzLmJhc2U2NCA9IGZhbHNlO1xuZXhwb3J0cy5iaW5hcnkgPSBmYWxzZTtcbmV4cG9ydHMuZGlyID0gZmFsc2U7XG5leHBvcnRzLmNyZWF0ZUZvbGRlcnMgPSB0cnVlO1xuZXhwb3J0cy5kYXRlID0gbnVsbDtcbmV4cG9ydHMuY29tcHJlc3Npb24gPSBudWxsO1xuZXhwb3J0cy5jb21wcmVzc2lvbk9wdGlvbnMgPSBudWxsO1xuZXhwb3J0cy5jb21tZW50ID0gbnVsbDtcbmV4cG9ydHMudW5peFBlcm1pc3Npb25zID0gbnVsbDtcbmV4cG9ydHMuZG9zUGVybWlzc2lvbnMgPSBudWxsO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/defaults.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/external.js":
/*!********************************************!*\
  !*** ./node_modules/jszip/lib/external.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n// load the global object first:\n// - it should be better integrated in the system (unhandledRejection in node)\n// - the environment may have a custom Promise implementation (see zone.js)\nvar ES6Promise = null;\nif (typeof Promise !== \"undefined\") {\n    ES6Promise = Promise;\n} else {\n    ES6Promise = __webpack_require__(/*! lie */ \"(ssr)/./node_modules/lie/lib/index.js\");\n}\n\n/**\n * Let the user use/change some implementations.\n */\nmodule.exports = {\n    Promise: ES6Promise\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL2V4dGVybmFsLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUU7QUFDRixpQkFBaUIsbUJBQU8sQ0FBQyxrREFBSztBQUM5Qjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xceHlpZW5cXE9uZURyaXZlXFxEZXNrdG9wXFxBbGxKb3VybmFsXFxqb3VybmFsLWFwcFxcbm9kZV9tb2R1bGVzXFxqc3ppcFxcbGliXFxleHRlcm5hbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLy8gbG9hZCB0aGUgZ2xvYmFsIG9iamVjdCBmaXJzdDpcbi8vIC0gaXQgc2hvdWxkIGJlIGJldHRlciBpbnRlZ3JhdGVkIGluIHRoZSBzeXN0ZW0gKHVuaGFuZGxlZFJlamVjdGlvbiBpbiBub2RlKVxuLy8gLSB0aGUgZW52aXJvbm1lbnQgbWF5IGhhdmUgYSBjdXN0b20gUHJvbWlzZSBpbXBsZW1lbnRhdGlvbiAoc2VlIHpvbmUuanMpXG52YXIgRVM2UHJvbWlzZSA9IG51bGw7XG5pZiAodHlwZW9mIFByb21pc2UgIT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICBFUzZQcm9taXNlID0gUHJvbWlzZTtcbn0gZWxzZSB7XG4gICAgRVM2UHJvbWlzZSA9IHJlcXVpcmUoXCJsaWVcIik7XG59XG5cbi8qKlxuICogTGV0IHRoZSB1c2VyIHVzZS9jaGFuZ2Ugc29tZSBpbXBsZW1lbnRhdGlvbnMuXG4gKi9cbm1vZHVsZS5leHBvcnRzID0ge1xuICAgIFByb21pc2U6IEVTNlByb21pc2Vcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/external.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/flate.js":
/*!*****************************************!*\
  !*** ./node_modules/jszip/lib/flate.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar USE_TYPEDARRAY = (typeof Uint8Array !== \"undefined\") && (typeof Uint16Array !== \"undefined\") && (typeof Uint32Array !== \"undefined\");\n\nvar pako = __webpack_require__(/*! pako */ \"(ssr)/./node_modules/pako/index.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\n\nvar ARRAY_TYPE = USE_TYPEDARRAY ? \"uint8array\" : \"array\";\n\nexports.magic = \"\\x08\\x00\";\n\n/**\n * Create a worker that uses pako to inflate/deflate.\n * @constructor\n * @param {String} action the name of the pako function to call : either \"Deflate\" or \"Inflate\".\n * @param {Object} options the options to use when (de)compressing.\n */\nfunction FlateWorker(action, options) {\n    GenericWorker.call(this, \"FlateWorker/\" + action);\n\n    this._pako = null;\n    this._pakoAction = action;\n    this._pakoOptions = options;\n    // the `meta` object from the last chunk received\n    // this allow this worker to pass around metadata\n    this.meta = {};\n}\n\nutils.inherits(FlateWorker, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nFlateWorker.prototype.processChunk = function (chunk) {\n    this.meta = chunk.meta;\n    if (this._pako === null) {\n        this._createPako();\n    }\n    this._pako.push(utils.transformTo(ARRAY_TYPE, chunk.data), false);\n};\n\n/**\n * @see GenericWorker.flush\n */\nFlateWorker.prototype.flush = function () {\n    GenericWorker.prototype.flush.call(this);\n    if (this._pako === null) {\n        this._createPako();\n    }\n    this._pako.push([], true);\n};\n/**\n * @see GenericWorker.cleanUp\n */\nFlateWorker.prototype.cleanUp = function () {\n    GenericWorker.prototype.cleanUp.call(this);\n    this._pako = null;\n};\n\n/**\n * Create the _pako object.\n * TODO: lazy-loading this object isn't the best solution but it's the\n * quickest. The best solution is to lazy-load the worker list. See also the\n * issue #446.\n */\nFlateWorker.prototype._createPako = function () {\n    this._pako = new pako[this._pakoAction]({\n        raw: true,\n        level: this._pakoOptions.level || -1 // default compression\n    });\n    var self = this;\n    this._pako.onData = function(data) {\n        self.push({\n            data : data,\n            meta : self.meta\n        });\n    };\n};\n\nexports.compressWorker = function (compressionOptions) {\n    return new FlateWorker(\"Deflate\", compressionOptions);\n};\nexports.uncompressWorker = function () {\n    return new FlateWorker(\"Inflate\", {});\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/flate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/generate/ZipFileWorker.js":
/*!**********************************************************!*\
  !*** ./node_modules/jszip/lib/generate/ZipFileWorker.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ../stream/GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\nvar utf8 = __webpack_require__(/*! ../utf8 */ \"(ssr)/./node_modules/jszip/lib/utf8.js\");\nvar crc32 = __webpack_require__(/*! ../crc32 */ \"(ssr)/./node_modules/jszip/lib/crc32.js\");\nvar signature = __webpack_require__(/*! ../signature */ \"(ssr)/./node_modules/jszip/lib/signature.js\");\n\n/**\n * Transform an integer into a string in hexadecimal.\n * @private\n * @param {number} dec the number to convert.\n * @param {number} bytes the number of bytes to generate.\n * @returns {string} the result.\n */\nvar decToHex = function(dec, bytes) {\n    var hex = \"\", i;\n    for (i = 0; i < bytes; i++) {\n        hex += String.fromCharCode(dec & 0xff);\n        dec = dec >>> 8;\n    }\n    return hex;\n};\n\n/**\n * Generate the UNIX part of the external file attributes.\n * @param {Object} unixPermissions the unix permissions or null.\n * @param {Boolean} isDir true if the entry is a directory, false otherwise.\n * @return {Number} a 32 bit integer.\n *\n * adapted from http://unix.stackexchange.com/questions/14705/the-zip-formats-external-file-attribute :\n *\n * TTTTsstrwxrwxrwx0000000000ADVSHR\n * ^^^^____________________________ file type, see zipinfo.c (UNX_*)\n *     ^^^_________________________ setuid, setgid, sticky\n *        ^^^^^^^^^________________ permissions\n *                 ^^^^^^^^^^______ not used ?\n *                           ^^^^^^ DOS attribute bits : Archive, Directory, Volume label, System file, Hidden, Read only\n */\nvar generateUnixExternalFileAttr = function (unixPermissions, isDir) {\n\n    var result = unixPermissions;\n    if (!unixPermissions) {\n        // I can't use octal values in strict mode, hence the hexa.\n        //  040775 => 0x41fd\n        // 0100664 => 0x81b4\n        result = isDir ? 0x41fd : 0x81b4;\n    }\n    return (result & 0xFFFF) << 16;\n};\n\n/**\n * Generate the DOS part of the external file attributes.\n * @param {Object} dosPermissions the dos permissions or null.\n * @param {Boolean} isDir true if the entry is a directory, false otherwise.\n * @return {Number} a 32 bit integer.\n *\n * Bit 0     Read-Only\n * Bit 1     Hidden\n * Bit 2     System\n * Bit 3     Volume Label\n * Bit 4     Directory\n * Bit 5     Archive\n */\nvar generateDosExternalFileAttr = function (dosPermissions) {\n    // the dir flag is already set for compatibility\n    return (dosPermissions || 0)  & 0x3F;\n};\n\n/**\n * Generate the various parts used in the construction of the final zip file.\n * @param {Object} streamInfo the hash with information about the compressed file.\n * @param {Boolean} streamedContent is the content streamed ?\n * @param {Boolean} streamingEnded is the stream finished ?\n * @param {number} offset the current offset from the start of the zip file.\n * @param {String} platform let's pretend we are this platform (change platform dependents fields)\n * @param {Function} encodeFileName the function to encode the file name / comment.\n * @return {Object} the zip parts.\n */\nvar generateZipParts = function(streamInfo, streamedContent, streamingEnded, offset, platform, encodeFileName) {\n    var file = streamInfo[\"file\"],\n        compression = streamInfo[\"compression\"],\n        useCustomEncoding = encodeFileName !== utf8.utf8encode,\n        encodedFileName = utils.transformTo(\"string\", encodeFileName(file.name)),\n        utfEncodedFileName = utils.transformTo(\"string\", utf8.utf8encode(file.name)),\n        comment = file.comment,\n        encodedComment = utils.transformTo(\"string\", encodeFileName(comment)),\n        utfEncodedComment = utils.transformTo(\"string\", utf8.utf8encode(comment)),\n        useUTF8ForFileName = utfEncodedFileName.length !== file.name.length,\n        useUTF8ForComment = utfEncodedComment.length !== comment.length,\n        dosTime,\n        dosDate,\n        extraFields = \"\",\n        unicodePathExtraField = \"\",\n        unicodeCommentExtraField = \"\",\n        dir = file.dir,\n        date = file.date;\n\n\n    var dataInfo = {\n        crc32 : 0,\n        compressedSize : 0,\n        uncompressedSize : 0\n    };\n\n    // if the content is streamed, the sizes/crc32 are only available AFTER\n    // the end of the stream.\n    if (!streamedContent || streamingEnded) {\n        dataInfo.crc32 = streamInfo[\"crc32\"];\n        dataInfo.compressedSize = streamInfo[\"compressedSize\"];\n        dataInfo.uncompressedSize = streamInfo[\"uncompressedSize\"];\n    }\n\n    var bitflag = 0;\n    if (streamedContent) {\n        // Bit 3: the sizes/crc32 are set to zero in the local header.\n        // The correct values are put in the data descriptor immediately\n        // following the compressed data.\n        bitflag |= 0x0008;\n    }\n    if (!useCustomEncoding && (useUTF8ForFileName || useUTF8ForComment)) {\n        // Bit 11: Language encoding flag (EFS).\n        bitflag |= 0x0800;\n    }\n\n\n    var extFileAttr = 0;\n    var versionMadeBy = 0;\n    if (dir) {\n        // dos or unix, we set the dos dir flag\n        extFileAttr |= 0x00010;\n    }\n    if(platform === \"UNIX\") {\n        versionMadeBy = 0x031E; // UNIX, version 3.0\n        extFileAttr |= generateUnixExternalFileAttr(file.unixPermissions, dir);\n    } else { // DOS or other, fallback to DOS\n        versionMadeBy = 0x0014; // DOS, version 2.0\n        extFileAttr |= generateDosExternalFileAttr(file.dosPermissions, dir);\n    }\n\n    // date\n    // @see http://www.delorie.com/djgpp/doc/rbinter/it/52/13.html\n    // @see http://www.delorie.com/djgpp/doc/rbinter/it/65/16.html\n    // @see http://www.delorie.com/djgpp/doc/rbinter/it/66/16.html\n\n    dosTime = date.getUTCHours();\n    dosTime = dosTime << 6;\n    dosTime = dosTime | date.getUTCMinutes();\n    dosTime = dosTime << 5;\n    dosTime = dosTime | date.getUTCSeconds() / 2;\n\n    dosDate = date.getUTCFullYear() - 1980;\n    dosDate = dosDate << 4;\n    dosDate = dosDate | (date.getUTCMonth() + 1);\n    dosDate = dosDate << 5;\n    dosDate = dosDate | date.getUTCDate();\n\n    if (useUTF8ForFileName) {\n        // set the unicode path extra field. unzip needs at least one extra\n        // field to correctly handle unicode path, so using the path is as good\n        // as any other information. This could improve the situation with\n        // other archive managers too.\n        // This field is usually used without the utf8 flag, with a non\n        // unicode path in the header (winrar, winzip). This helps (a bit)\n        // with the messy Windows' default compressed folders feature but\n        // breaks on p7zip which doesn't seek the unicode path extra field.\n        // So for now, UTF-8 everywhere !\n        unicodePathExtraField =\n            // Version\n            decToHex(1, 1) +\n            // NameCRC32\n            decToHex(crc32(encodedFileName), 4) +\n            // UnicodeName\n            utfEncodedFileName;\n\n        extraFields +=\n            // Info-ZIP Unicode Path Extra Field\n            \"\\x75\\x70\" +\n            // size\n            decToHex(unicodePathExtraField.length, 2) +\n            // content\n            unicodePathExtraField;\n    }\n\n    if(useUTF8ForComment) {\n\n        unicodeCommentExtraField =\n            // Version\n            decToHex(1, 1) +\n            // CommentCRC32\n            decToHex(crc32(encodedComment), 4) +\n            // UnicodeName\n            utfEncodedComment;\n\n        extraFields +=\n            // Info-ZIP Unicode Path Extra Field\n            \"\\x75\\x63\" +\n            // size\n            decToHex(unicodeCommentExtraField.length, 2) +\n            // content\n            unicodeCommentExtraField;\n    }\n\n    var header = \"\";\n\n    // version needed to extract\n    header += \"\\x0A\\x00\";\n    // general purpose bit flag\n    header += decToHex(bitflag, 2);\n    // compression method\n    header += compression.magic;\n    // last mod file time\n    header += decToHex(dosTime, 2);\n    // last mod file date\n    header += decToHex(dosDate, 2);\n    // crc-32\n    header += decToHex(dataInfo.crc32, 4);\n    // compressed size\n    header += decToHex(dataInfo.compressedSize, 4);\n    // uncompressed size\n    header += decToHex(dataInfo.uncompressedSize, 4);\n    // file name length\n    header += decToHex(encodedFileName.length, 2);\n    // extra field length\n    header += decToHex(extraFields.length, 2);\n\n\n    var fileRecord = signature.LOCAL_FILE_HEADER + header + encodedFileName + extraFields;\n\n    var dirRecord = signature.CENTRAL_FILE_HEADER +\n        // version made by (00: DOS)\n        decToHex(versionMadeBy, 2) +\n        // file header (common to file and central directory)\n        header +\n        // file comment length\n        decToHex(encodedComment.length, 2) +\n        // disk number start\n        \"\\x00\\x00\" +\n        // internal file attributes TODO\n        \"\\x00\\x00\" +\n        // external file attributes\n        decToHex(extFileAttr, 4) +\n        // relative offset of local header\n        decToHex(offset, 4) +\n        // file name\n        encodedFileName +\n        // extra field\n        extraFields +\n        // file comment\n        encodedComment;\n\n    return {\n        fileRecord: fileRecord,\n        dirRecord: dirRecord\n    };\n};\n\n/**\n * Generate the EOCD record.\n * @param {Number} entriesCount the number of entries in the zip file.\n * @param {Number} centralDirLength the length (in bytes) of the central dir.\n * @param {Number} localDirLength the length (in bytes) of the local dir.\n * @param {String} comment the zip file comment as a binary string.\n * @param {Function} encodeFileName the function to encode the comment.\n * @return {String} the EOCD record.\n */\nvar generateCentralDirectoryEnd = function (entriesCount, centralDirLength, localDirLength, comment, encodeFileName) {\n    var dirEnd = \"\";\n    var encodedComment = utils.transformTo(\"string\", encodeFileName(comment));\n\n    // end of central dir signature\n    dirEnd = signature.CENTRAL_DIRECTORY_END +\n        // number of this disk\n        \"\\x00\\x00\" +\n        // number of the disk with the start of the central directory\n        \"\\x00\\x00\" +\n        // total number of entries in the central directory on this disk\n        decToHex(entriesCount, 2) +\n        // total number of entries in the central directory\n        decToHex(entriesCount, 2) +\n        // size of the central directory   4 bytes\n        decToHex(centralDirLength, 4) +\n        // offset of start of central directory with respect to the starting disk number\n        decToHex(localDirLength, 4) +\n        // .ZIP file comment length\n        decToHex(encodedComment.length, 2) +\n        // .ZIP file comment\n        encodedComment;\n\n    return dirEnd;\n};\n\n/**\n * Generate data descriptors for a file entry.\n * @param {Object} streamInfo the hash generated by a worker, containing information\n * on the file entry.\n * @return {String} the data descriptors.\n */\nvar generateDataDescriptors = function (streamInfo) {\n    var descriptor = \"\";\n    descriptor = signature.DATA_DESCRIPTOR +\n        // crc-32                          4 bytes\n        decToHex(streamInfo[\"crc32\"], 4) +\n        // compressed size                 4 bytes\n        decToHex(streamInfo[\"compressedSize\"], 4) +\n        // uncompressed size               4 bytes\n        decToHex(streamInfo[\"uncompressedSize\"], 4);\n\n    return descriptor;\n};\n\n\n/**\n * A worker to concatenate other workers to create a zip file.\n * @param {Boolean} streamFiles `true` to stream the content of the files,\n * `false` to accumulate it.\n * @param {String} comment the comment to use.\n * @param {String} platform the platform to use, \"UNIX\" or \"DOS\".\n * @param {Function} encodeFileName the function to encode file names and comments.\n */\nfunction ZipFileWorker(streamFiles, comment, platform, encodeFileName) {\n    GenericWorker.call(this, \"ZipFileWorker\");\n    // The number of bytes written so far. This doesn't count accumulated chunks.\n    this.bytesWritten = 0;\n    // The comment of the zip file\n    this.zipComment = comment;\n    // The platform \"generating\" the zip file.\n    this.zipPlatform = platform;\n    // the function to encode file names and comments.\n    this.encodeFileName = encodeFileName;\n    // Should we stream the content of the files ?\n    this.streamFiles = streamFiles;\n    // If `streamFiles` is false, we will need to accumulate the content of the\n    // files to calculate sizes / crc32 (and write them *before* the content).\n    // This boolean indicates if we are accumulating chunks (it will change a lot\n    // during the lifetime of this worker).\n    this.accumulate = false;\n    // The buffer receiving chunks when accumulating content.\n    this.contentBuffer = [];\n    // The list of generated directory records.\n    this.dirRecords = [];\n    // The offset (in bytes) from the beginning of the zip file for the current source.\n    this.currentSourceOffset = 0;\n    // The total number of entries in this zip file.\n    this.entriesCount = 0;\n    // the name of the file currently being added, null when handling the end of the zip file.\n    // Used for the emitted metadata.\n    this.currentFile = null;\n\n\n\n    this._sources = [];\n}\nutils.inherits(ZipFileWorker, GenericWorker);\n\n/**\n * @see GenericWorker.push\n */\nZipFileWorker.prototype.push = function (chunk) {\n\n    var currentFilePercent = chunk.meta.percent || 0;\n    var entriesCount = this.entriesCount;\n    var remainingFiles = this._sources.length;\n\n    if(this.accumulate) {\n        this.contentBuffer.push(chunk);\n    } else {\n        this.bytesWritten += chunk.data.length;\n\n        GenericWorker.prototype.push.call(this, {\n            data : chunk.data,\n            meta : {\n                currentFile : this.currentFile,\n                percent : entriesCount ? (currentFilePercent + 100 * (entriesCount - remainingFiles - 1)) / entriesCount : 100\n            }\n        });\n    }\n};\n\n/**\n * The worker started a new source (an other worker).\n * @param {Object} streamInfo the streamInfo object from the new source.\n */\nZipFileWorker.prototype.openedSource = function (streamInfo) {\n    this.currentSourceOffset = this.bytesWritten;\n    this.currentFile = streamInfo[\"file\"].name;\n\n    var streamedContent = this.streamFiles && !streamInfo[\"file\"].dir;\n\n    // don't stream folders (because they don't have any content)\n    if(streamedContent) {\n        var record = generateZipParts(streamInfo, streamedContent, false, this.currentSourceOffset, this.zipPlatform, this.encodeFileName);\n        this.push({\n            data : record.fileRecord,\n            meta : {percent:0}\n        });\n    } else {\n        // we need to wait for the whole file before pushing anything\n        this.accumulate = true;\n    }\n};\n\n/**\n * The worker finished a source (an other worker).\n * @param {Object} streamInfo the streamInfo object from the finished source.\n */\nZipFileWorker.prototype.closedSource = function (streamInfo) {\n    this.accumulate = false;\n    var streamedContent = this.streamFiles && !streamInfo[\"file\"].dir;\n    var record = generateZipParts(streamInfo, streamedContent, true, this.currentSourceOffset, this.zipPlatform, this.encodeFileName);\n\n    this.dirRecords.push(record.dirRecord);\n    if(streamedContent) {\n        // after the streamed file, we put data descriptors\n        this.push({\n            data : generateDataDescriptors(streamInfo),\n            meta : {percent:100}\n        });\n    } else {\n        // the content wasn't streamed, we need to push everything now\n        // first the file record, then the content\n        this.push({\n            data : record.fileRecord,\n            meta : {percent:0}\n        });\n        while(this.contentBuffer.length) {\n            this.push(this.contentBuffer.shift());\n        }\n    }\n    this.currentFile = null;\n};\n\n/**\n * @see GenericWorker.flush\n */\nZipFileWorker.prototype.flush = function () {\n\n    var localDirLength = this.bytesWritten;\n    for(var i = 0; i < this.dirRecords.length; i++) {\n        this.push({\n            data : this.dirRecords[i],\n            meta : {percent:100}\n        });\n    }\n    var centralDirLength = this.bytesWritten - localDirLength;\n\n    var dirEnd = generateCentralDirectoryEnd(this.dirRecords.length, centralDirLength, localDirLength, this.zipComment, this.encodeFileName);\n\n    this.push({\n        data : dirEnd,\n        meta : {percent:100}\n    });\n};\n\n/**\n * Prepare the next source to be read.\n */\nZipFileWorker.prototype.prepareNextSource = function () {\n    this.previous = this._sources.shift();\n    this.openedSource(this.previous.streamInfo);\n    if (this.isPaused) {\n        this.previous.pause();\n    } else {\n        this.previous.resume();\n    }\n};\n\n/**\n * @see GenericWorker.registerPrevious\n */\nZipFileWorker.prototype.registerPrevious = function (previous) {\n    this._sources.push(previous);\n    var self = this;\n\n    previous.on(\"data\", function (chunk) {\n        self.processChunk(chunk);\n    });\n    previous.on(\"end\", function () {\n        self.closedSource(self.previous.streamInfo);\n        if(self._sources.length) {\n            self.prepareNextSource();\n        } else {\n            self.end();\n        }\n    });\n    previous.on(\"error\", function (e) {\n        self.error(e);\n    });\n    return this;\n};\n\n/**\n * @see GenericWorker.resume\n */\nZipFileWorker.prototype.resume = function () {\n    if(!GenericWorker.prototype.resume.call(this)) {\n        return false;\n    }\n\n    if (!this.previous && this._sources.length) {\n        this.prepareNextSource();\n        return true;\n    }\n    if (!this.previous && !this._sources.length && !this.generatedError) {\n        this.end();\n        return true;\n    }\n};\n\n/**\n * @see GenericWorker.error\n */\nZipFileWorker.prototype.error = function (e) {\n    var sources = this._sources;\n    if(!GenericWorker.prototype.error.call(this, e)) {\n        return false;\n    }\n    for(var i = 0; i < sources.length; i++) {\n        try {\n            sources[i].error(e);\n        } catch(e) {\n            // the `error` exploded, nothing to do\n        }\n    }\n    return true;\n};\n\n/**\n * @see GenericWorker.lock\n */\nZipFileWorker.prototype.lock = function () {\n    GenericWorker.prototype.lock.call(this);\n    var sources = this._sources;\n    for(var i = 0; i < sources.length; i++) {\n        sources[i].lock();\n    }\n};\n\nmodule.exports = ZipFileWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/generate/ZipFileWorker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/generate/index.js":
/*!**************************************************!*\
  !*** ./node_modules/jszip/lib/generate/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar compressions = __webpack_require__(/*! ../compressions */ \"(ssr)/./node_modules/jszip/lib/compressions.js\");\nvar ZipFileWorker = __webpack_require__(/*! ./ZipFileWorker */ \"(ssr)/./node_modules/jszip/lib/generate/ZipFileWorker.js\");\n\n/**\n * Find the compression to use.\n * @param {String} fileCompression the compression defined at the file level, if any.\n * @param {String} zipCompression the compression defined at the load() level.\n * @return {Object} the compression object to use.\n */\nvar getCompression = function (fileCompression, zipCompression) {\n\n    var compressionName = fileCompression || zipCompression;\n    var compression = compressions[compressionName];\n    if (!compression) {\n        throw new Error(compressionName + \" is not a valid compression method !\");\n    }\n    return compression;\n};\n\n/**\n * Create a worker to generate a zip file.\n * @param {JSZip} zip the JSZip instance at the right root level.\n * @param {Object} options to generate the zip file.\n * @param {String} comment the comment to use.\n */\nexports.generateWorker = function (zip, options, comment) {\n\n    var zipFileWorker = new ZipFileWorker(options.streamFiles, comment, options.platform, options.encodeFileName);\n    var entriesCount = 0;\n    try {\n\n        zip.forEach(function (relativePath, file) {\n            entriesCount++;\n            var compression = getCompression(file.options.compression, options.compression);\n            var compressionOptions = file.options.compressionOptions || options.compressionOptions || {};\n            var dir = file.dir, date = file.date;\n\n            file._compressWorker(compression, compressionOptions)\n                .withStreamInfo(\"file\", {\n                    name : relativePath,\n                    dir : dir,\n                    date : date,\n                    comment : file.comment || \"\",\n                    unixPermissions : file.unixPermissions,\n                    dosPermissions : file.dosPermissions\n                })\n                .pipe(zipFileWorker);\n        });\n        zipFileWorker.entriesCount = entriesCount;\n    } catch (e) {\n        zipFileWorker.error(e);\n    }\n\n    return zipFileWorker;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL2dlbmVyYXRlL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLG1CQUFtQixtQkFBTyxDQUFDLHVFQUFpQjtBQUM1QyxvQkFBb0IsbUJBQU8sQ0FBQyxpRkFBaUI7O0FBRTdDO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxRQUFRO0FBQ25CLFlBQVksUUFBUTtBQUNwQjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxXQUFXLE9BQU87QUFDbEIsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQjtBQUNBLHNCQUFzQjs7QUFFdEI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBLFNBQVM7QUFDVDtBQUNBLE1BQU07QUFDTjtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xceHlpZW5cXE9uZURyaXZlXFxEZXNrdG9wXFxBbGxKb3VybmFsXFxqb3VybmFsLWFwcFxcbm9kZV9tb2R1bGVzXFxqc3ppcFxcbGliXFxnZW5lcmF0ZVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbnZhciBjb21wcmVzc2lvbnMgPSByZXF1aXJlKFwiLi4vY29tcHJlc3Npb25zXCIpO1xudmFyIFppcEZpbGVXb3JrZXIgPSByZXF1aXJlKFwiLi9aaXBGaWxlV29ya2VyXCIpO1xuXG4vKipcbiAqIEZpbmQgdGhlIGNvbXByZXNzaW9uIHRvIHVzZS5cbiAqIEBwYXJhbSB7U3RyaW5nfSBmaWxlQ29tcHJlc3Npb24gdGhlIGNvbXByZXNzaW9uIGRlZmluZWQgYXQgdGhlIGZpbGUgbGV2ZWwsIGlmIGFueS5cbiAqIEBwYXJhbSB7U3RyaW5nfSB6aXBDb21wcmVzc2lvbiB0aGUgY29tcHJlc3Npb24gZGVmaW5lZCBhdCB0aGUgbG9hZCgpIGxldmVsLlxuICogQHJldHVybiB7T2JqZWN0fSB0aGUgY29tcHJlc3Npb24gb2JqZWN0IHRvIHVzZS5cbiAqL1xudmFyIGdldENvbXByZXNzaW9uID0gZnVuY3Rpb24gKGZpbGVDb21wcmVzc2lvbiwgemlwQ29tcHJlc3Npb24pIHtcblxuICAgIHZhciBjb21wcmVzc2lvbk5hbWUgPSBmaWxlQ29tcHJlc3Npb24gfHwgemlwQ29tcHJlc3Npb247XG4gICAgdmFyIGNvbXByZXNzaW9uID0gY29tcHJlc3Npb25zW2NvbXByZXNzaW9uTmFtZV07XG4gICAgaWYgKCFjb21wcmVzc2lvbikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoY29tcHJlc3Npb25OYW1lICsgXCIgaXMgbm90IGEgdmFsaWQgY29tcHJlc3Npb24gbWV0aG9kICFcIik7XG4gICAgfVxuICAgIHJldHVybiBjb21wcmVzc2lvbjtcbn07XG5cbi8qKlxuICogQ3JlYXRlIGEgd29ya2VyIHRvIGdlbmVyYXRlIGEgemlwIGZpbGUuXG4gKiBAcGFyYW0ge0pTWmlwfSB6aXAgdGhlIEpTWmlwIGluc3RhbmNlIGF0IHRoZSByaWdodCByb290IGxldmVsLlxuICogQHBhcmFtIHtPYmplY3R9IG9wdGlvbnMgdG8gZ2VuZXJhdGUgdGhlIHppcCBmaWxlLlxuICogQHBhcmFtIHtTdHJpbmd9IGNvbW1lbnQgdGhlIGNvbW1lbnQgdG8gdXNlLlxuICovXG5leHBvcnRzLmdlbmVyYXRlV29ya2VyID0gZnVuY3Rpb24gKHppcCwgb3B0aW9ucywgY29tbWVudCkge1xuXG4gICAgdmFyIHppcEZpbGVXb3JrZXIgPSBuZXcgWmlwRmlsZVdvcmtlcihvcHRpb25zLnN0cmVhbUZpbGVzLCBjb21tZW50LCBvcHRpb25zLnBsYXRmb3JtLCBvcHRpb25zLmVuY29kZUZpbGVOYW1lKTtcbiAgICB2YXIgZW50cmllc0NvdW50ID0gMDtcbiAgICB0cnkge1xuXG4gICAgICAgIHppcC5mb3JFYWNoKGZ1bmN0aW9uIChyZWxhdGl2ZVBhdGgsIGZpbGUpIHtcbiAgICAgICAgICAgIGVudHJpZXNDb3VudCsrO1xuICAgICAgICAgICAgdmFyIGNvbXByZXNzaW9uID0gZ2V0Q29tcHJlc3Npb24oZmlsZS5vcHRpb25zLmNvbXByZXNzaW9uLCBvcHRpb25zLmNvbXByZXNzaW9uKTtcbiAgICAgICAgICAgIHZhciBjb21wcmVzc2lvbk9wdGlvbnMgPSBmaWxlLm9wdGlvbnMuY29tcHJlc3Npb25PcHRpb25zIHx8IG9wdGlvbnMuY29tcHJlc3Npb25PcHRpb25zIHx8IHt9O1xuICAgICAgICAgICAgdmFyIGRpciA9IGZpbGUuZGlyLCBkYXRlID0gZmlsZS5kYXRlO1xuXG4gICAgICAgICAgICBmaWxlLl9jb21wcmVzc1dvcmtlcihjb21wcmVzc2lvbiwgY29tcHJlc3Npb25PcHRpb25zKVxuICAgICAgICAgICAgICAgIC53aXRoU3RyZWFtSW5mbyhcImZpbGVcIiwge1xuICAgICAgICAgICAgICAgICAgICBuYW1lIDogcmVsYXRpdmVQYXRoLFxuICAgICAgICAgICAgICAgICAgICBkaXIgOiBkaXIsXG4gICAgICAgICAgICAgICAgICAgIGRhdGUgOiBkYXRlLFxuICAgICAgICAgICAgICAgICAgICBjb21tZW50IDogZmlsZS5jb21tZW50IHx8IFwiXCIsXG4gICAgICAgICAgICAgICAgICAgIHVuaXhQZXJtaXNzaW9ucyA6IGZpbGUudW5peFBlcm1pc3Npb25zLFxuICAgICAgICAgICAgICAgICAgICBkb3NQZXJtaXNzaW9ucyA6IGZpbGUuZG9zUGVybWlzc2lvbnNcbiAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgIC5waXBlKHppcEZpbGVXb3JrZXIpO1xuICAgICAgICB9KTtcbiAgICAgICAgemlwRmlsZVdvcmtlci5lbnRyaWVzQ291bnQgPSBlbnRyaWVzQ291bnQ7XG4gICAgfSBjYXRjaCAoZSkge1xuICAgICAgICB6aXBGaWxlV29ya2VyLmVycm9yKGUpO1xuICAgIH1cblxuICAgIHJldHVybiB6aXBGaWxlV29ya2VyO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/generate/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/index.js":
/*!*****************************************!*\
  !*** ./node_modules/jszip/lib/index.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n/**\n * Representation a of zip file in js\n * @constructor\n */\nfunction JSZip() {\n    // if this constructor is used without `new`, it adds `new` before itself:\n    if(!(this instanceof JSZip)) {\n        return new JSZip();\n    }\n\n    if(arguments.length) {\n        throw new Error(\"The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.\");\n    }\n\n    // object containing the files :\n    // {\n    //   \"folder/\" : {...},\n    //   \"folder/data.txt\" : {...}\n    // }\n    // NOTE: we use a null prototype because we do not\n    // want filenames like \"toString\" coming from a zip file\n    // to overwrite methods and attributes in a normal Object.\n    this.files = Object.create(null);\n\n    this.comment = null;\n\n    // Where we are in the hierarchy\n    this.root = \"\";\n    this.clone = function() {\n        var newObj = new JSZip();\n        for (var i in this) {\n            if (typeof this[i] !== \"function\") {\n                newObj[i] = this[i];\n            }\n        }\n        return newObj;\n    };\n}\nJSZip.prototype = __webpack_require__(/*! ./object */ \"(ssr)/./node_modules/jszip/lib/object.js\");\nJSZip.prototype.loadAsync = __webpack_require__(/*! ./load */ \"(ssr)/./node_modules/jszip/lib/load.js\");\nJSZip.support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\nJSZip.defaults = __webpack_require__(/*! ./defaults */ \"(ssr)/./node_modules/jszip/lib/defaults.js\");\n\n// TODO find a better way to handle this version,\n// a require('package.json').version doesn't work with webpack, see #327\nJSZip.version = \"3.10.1\";\n\nJSZip.loadAsync = function (content, options) {\n    return new JSZip().loadAsync(content, options);\n};\n\nJSZip.external = __webpack_require__(/*! ./external */ \"(ssr)/./node_modules/jszip/lib/external.js\");\nmodule.exports = JSZip;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLHNCQUFzQixJQUFJO0FBQzFCLDhCQUE4QjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixtQkFBTyxDQUFDLDBEQUFVO0FBQ3BDLDRCQUE0QixtQkFBTyxDQUFDLHNEQUFRO0FBQzVDLGdCQUFnQixtQkFBTyxDQUFDLDREQUFXO0FBQ25DLGlCQUFpQixtQkFBTyxDQUFDLDhEQUFZOztBQUVyQztBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBLGlCQUFpQixtQkFBTyxDQUFDLDhEQUFZO0FBQ3JDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHh5aWVuXFxPbmVEcml2ZVxcRGVza3RvcFxcQWxsSm91cm5hbFxcam91cm5hbC1hcHBcXG5vZGVfbW9kdWxlc1xcanN6aXBcXGxpYlxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbi8qKlxuICogUmVwcmVzZW50YXRpb24gYSBvZiB6aXAgZmlsZSBpbiBqc1xuICogQGNvbnN0cnVjdG9yXG4gKi9cbmZ1bmN0aW9uIEpTWmlwKCkge1xuICAgIC8vIGlmIHRoaXMgY29uc3RydWN0b3IgaXMgdXNlZCB3aXRob3V0IGBuZXdgLCBpdCBhZGRzIGBuZXdgIGJlZm9yZSBpdHNlbGY6XG4gICAgaWYoISh0aGlzIGluc3RhbmNlb2YgSlNaaXApKSB7XG4gICAgICAgIHJldHVybiBuZXcgSlNaaXAoKTtcbiAgICB9XG5cbiAgICBpZihhcmd1bWVudHMubGVuZ3RoKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIlRoZSBjb25zdHJ1Y3RvciB3aXRoIHBhcmFtZXRlcnMgaGFzIGJlZW4gcmVtb3ZlZCBpbiBKU1ppcCAzLjAsIHBsZWFzZSBjaGVjayB0aGUgdXBncmFkZSBndWlkZS5cIik7XG4gICAgfVxuXG4gICAgLy8gb2JqZWN0IGNvbnRhaW5pbmcgdGhlIGZpbGVzIDpcbiAgICAvLyB7XG4gICAgLy8gICBcImZvbGRlci9cIiA6IHsuLi59LFxuICAgIC8vICAgXCJmb2xkZXIvZGF0YS50eHRcIiA6IHsuLi59XG4gICAgLy8gfVxuICAgIC8vIE5PVEU6IHdlIHVzZSBhIG51bGwgcHJvdG90eXBlIGJlY2F1c2Ugd2UgZG8gbm90XG4gICAgLy8gd2FudCBmaWxlbmFtZXMgbGlrZSBcInRvU3RyaW5nXCIgY29taW5nIGZyb20gYSB6aXAgZmlsZVxuICAgIC8vIHRvIG92ZXJ3cml0ZSBtZXRob2RzIGFuZCBhdHRyaWJ1dGVzIGluIGEgbm9ybWFsIE9iamVjdC5cbiAgICB0aGlzLmZpbGVzID0gT2JqZWN0LmNyZWF0ZShudWxsKTtcblxuICAgIHRoaXMuY29tbWVudCA9IG51bGw7XG5cbiAgICAvLyBXaGVyZSB3ZSBhcmUgaW4gdGhlIGhpZXJhcmNoeVxuICAgIHRoaXMucm9vdCA9IFwiXCI7XG4gICAgdGhpcy5jbG9uZSA9IGZ1bmN0aW9uKCkge1xuICAgICAgICB2YXIgbmV3T2JqID0gbmV3IEpTWmlwKCk7XG4gICAgICAgIGZvciAodmFyIGkgaW4gdGhpcykge1xuICAgICAgICAgICAgaWYgKHR5cGVvZiB0aGlzW2ldICE9PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgICAgICAgICAgICBuZXdPYmpbaV0gPSB0aGlzW2ldO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBuZXdPYmo7XG4gICAgfTtcbn1cbkpTWmlwLnByb3RvdHlwZSA9IHJlcXVpcmUoXCIuL29iamVjdFwiKTtcbkpTWmlwLnByb3RvdHlwZS5sb2FkQXN5bmMgPSByZXF1aXJlKFwiLi9sb2FkXCIpO1xuSlNaaXAuc3VwcG9ydCA9IHJlcXVpcmUoXCIuL3N1cHBvcnRcIik7XG5KU1ppcC5kZWZhdWx0cyA9IHJlcXVpcmUoXCIuL2RlZmF1bHRzXCIpO1xuXG4vLyBUT0RPIGZpbmQgYSBiZXR0ZXIgd2F5IHRvIGhhbmRsZSB0aGlzIHZlcnNpb24sXG4vLyBhIHJlcXVpcmUoJ3BhY2thZ2UuanNvbicpLnZlcnNpb24gZG9lc24ndCB3b3JrIHdpdGggd2VicGFjaywgc2VlICMzMjdcbkpTWmlwLnZlcnNpb24gPSBcIjMuMTAuMVwiO1xuXG5KU1ppcC5sb2FkQXN5bmMgPSBmdW5jdGlvbiAoY29udGVudCwgb3B0aW9ucykge1xuICAgIHJldHVybiBuZXcgSlNaaXAoKS5sb2FkQXN5bmMoY29udGVudCwgb3B0aW9ucyk7XG59O1xuXG5KU1ppcC5leHRlcm5hbCA9IHJlcXVpcmUoXCIuL2V4dGVybmFsXCIpO1xubW9kdWxlLmV4cG9ydHMgPSBKU1ppcDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/load.js":
/*!****************************************!*\
  !*** ./node_modules/jszip/lib/load.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar external = __webpack_require__(/*! ./external */ \"(ssr)/./node_modules/jszip/lib/external.js\");\nvar utf8 = __webpack_require__(/*! ./utf8 */ \"(ssr)/./node_modules/jszip/lib/utf8.js\");\nvar ZipEntries = __webpack_require__(/*! ./zipEntries */ \"(ssr)/./node_modules/jszip/lib/zipEntries.js\");\nvar Crc32Probe = __webpack_require__(/*! ./stream/Crc32Probe */ \"(ssr)/./node_modules/jszip/lib/stream/Crc32Probe.js\");\nvar nodejsUtils = __webpack_require__(/*! ./nodejsUtils */ \"(ssr)/./node_modules/jszip/lib/nodejsUtils.js\");\n\n/**\n * Check the CRC32 of an entry.\n * @param {ZipEntry} zipEntry the zip entry to check.\n * @return {Promise} the result.\n */\nfunction checkEntryCRC32(zipEntry) {\n    return new external.Promise(function (resolve, reject) {\n        var worker = zipEntry.decompressed.getContentWorker().pipe(new Crc32Probe());\n        worker.on(\"error\", function (e) {\n            reject(e);\n        })\n            .on(\"end\", function () {\n                if (worker.streamInfo.crc32 !== zipEntry.decompressed.crc32) {\n                    reject(new Error(\"Corrupted zip : CRC32 mismatch\"));\n                } else {\n                    resolve();\n                }\n            })\n            .resume();\n    });\n}\n\nmodule.exports = function (data, options) {\n    var zip = this;\n    options = utils.extend(options || {}, {\n        base64: false,\n        checkCRC32: false,\n        optimizedBinaryString: false,\n        createFolders: false,\n        decodeFileName: utf8.utf8decode\n    });\n\n    if (nodejsUtils.isNode && nodejsUtils.isStream(data)) {\n        return external.Promise.reject(new Error(\"JSZip can't accept a stream when loading a zip file.\"));\n    }\n\n    return utils.prepareContent(\"the loaded zip file\", data, true, options.optimizedBinaryString, options.base64)\n        .then(function (data) {\n            var zipEntries = new ZipEntries(options);\n            zipEntries.load(data);\n            return zipEntries;\n        }).then(function checkCRC32(zipEntries) {\n            var promises = [external.Promise.resolve(zipEntries)];\n            var files = zipEntries.files;\n            if (options.checkCRC32) {\n                for (var i = 0; i < files.length; i++) {\n                    promises.push(checkEntryCRC32(files[i]));\n                }\n            }\n            return external.Promise.all(promises);\n        }).then(function addFiles(results) {\n            var zipEntries = results.shift();\n            var files = zipEntries.files;\n            for (var i = 0; i < files.length; i++) {\n                var input = files[i];\n\n                var unsafeName = input.fileNameStr;\n                var safeName = utils.resolve(input.fileNameStr);\n\n                zip.file(safeName, input.decompressed, {\n                    binary: true,\n                    optimizedBinaryString: true,\n                    date: input.date,\n                    dir: input.dir,\n                    comment: input.fileCommentStr.length ? input.fileCommentStr : null,\n                    unixPermissions: input.unixPermissions,\n                    dosPermissions: input.dosPermissions,\n                    createFolders: options.createFolders\n                });\n                if (!input.dir) {\n                    zip.file(safeName).unsafeOriginalName = unsafeName;\n                }\n            }\n            if (zipEntries.zipComment.length) {\n                zip.comment = zipEntries.zipComment;\n            }\n\n            return zip;\n        });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/load.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js":
/*!*******************************************************************!*\
  !*** ./node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js ***!
  \*******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ../stream/GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\n\n/**\n * A worker that use a nodejs stream as source.\n * @constructor\n * @param {String} filename the name of the file entry for this stream.\n * @param {Readable} stream the nodejs stream.\n */\nfunction NodejsStreamInputAdapter(filename, stream) {\n    GenericWorker.call(this, \"Nodejs stream input adapter for \" + filename);\n    this._upstreamEnded = false;\n    this._bindStream(stream);\n}\n\nutils.inherits(NodejsStreamInputAdapter, GenericWorker);\n\n/**\n * Prepare the stream and bind the callbacks on it.\n * Do this ASAP on node 0.10 ! A lazy binding doesn't always work.\n * @param {Stream} stream the nodejs stream to use.\n */\nNodejsStreamInputAdapter.prototype._bindStream = function (stream) {\n    var self = this;\n    this._stream = stream;\n    stream.pause();\n    stream\n        .on(\"data\", function (chunk) {\n            self.push({\n                data: chunk,\n                meta : {\n                    percent : 0\n                }\n            });\n        })\n        .on(\"error\", function (e) {\n            if(self.isPaused) {\n                this.generatedError = e;\n            } else {\n                self.error(e);\n            }\n        })\n        .on(\"end\", function () {\n            if(self.isPaused) {\n                self._upstreamEnded = true;\n            } else {\n                self.end();\n            }\n        });\n};\nNodejsStreamInputAdapter.prototype.pause = function () {\n    if(!GenericWorker.prototype.pause.call(this)) {\n        return false;\n    }\n    this._stream.pause();\n    return true;\n};\nNodejsStreamInputAdapter.prototype.resume = function () {\n    if(!GenericWorker.prototype.resume.call(this)) {\n        return false;\n    }\n\n    if(this._upstreamEnded) {\n        this.end();\n    } else {\n        this._stream.resume();\n    }\n\n    return true;\n};\n\nmodule.exports = NodejsStreamInputAdapter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL25vZGVqcy9Ob2RlanNTdHJlYW1JbnB1dEFkYXB0ZXIuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsWUFBWSxtQkFBTyxDQUFDLHlEQUFVO0FBQzlCLG9CQUFvQixtQkFBTyxDQUFDLHVGQUF5Qjs7QUFFckQ7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsVUFBVTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTs7QUFFQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHh5aWVuXFxPbmVEcml2ZVxcRGVza3RvcFxcQWxsSm91cm5hbFxcam91cm5hbC1hcHBcXG5vZGVfbW9kdWxlc1xcanN6aXBcXGxpYlxcbm9kZWpzXFxOb2RlanNTdHJlYW1JbnB1dEFkYXB0ZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbnZhciB1dGlscyA9IHJlcXVpcmUoXCIuLi91dGlsc1wiKTtcbnZhciBHZW5lcmljV29ya2VyID0gcmVxdWlyZShcIi4uL3N0cmVhbS9HZW5lcmljV29ya2VyXCIpO1xuXG4vKipcbiAqIEEgd29ya2VyIHRoYXQgdXNlIGEgbm9kZWpzIHN0cmVhbSBhcyBzb3VyY2UuXG4gKiBAY29uc3RydWN0b3JcbiAqIEBwYXJhbSB7U3RyaW5nfSBmaWxlbmFtZSB0aGUgbmFtZSBvZiB0aGUgZmlsZSBlbnRyeSBmb3IgdGhpcyBzdHJlYW0uXG4gKiBAcGFyYW0ge1JlYWRhYmxlfSBzdHJlYW0gdGhlIG5vZGVqcyBzdHJlYW0uXG4gKi9cbmZ1bmN0aW9uIE5vZGVqc1N0cmVhbUlucHV0QWRhcHRlcihmaWxlbmFtZSwgc3RyZWFtKSB7XG4gICAgR2VuZXJpY1dvcmtlci5jYWxsKHRoaXMsIFwiTm9kZWpzIHN0cmVhbSBpbnB1dCBhZGFwdGVyIGZvciBcIiArIGZpbGVuYW1lKTtcbiAgICB0aGlzLl91cHN0cmVhbUVuZGVkID0gZmFsc2U7XG4gICAgdGhpcy5fYmluZFN0cmVhbShzdHJlYW0pO1xufVxuXG51dGlscy5pbmhlcml0cyhOb2RlanNTdHJlYW1JbnB1dEFkYXB0ZXIsIEdlbmVyaWNXb3JrZXIpO1xuXG4vKipcbiAqIFByZXBhcmUgdGhlIHN0cmVhbSBhbmQgYmluZCB0aGUgY2FsbGJhY2tzIG9uIGl0LlxuICogRG8gdGhpcyBBU0FQIG9uIG5vZGUgMC4xMCAhIEEgbGF6eSBiaW5kaW5nIGRvZXNuJ3QgYWx3YXlzIHdvcmsuXG4gKiBAcGFyYW0ge1N0cmVhbX0gc3RyZWFtIHRoZSBub2RlanMgc3RyZWFtIHRvIHVzZS5cbiAqL1xuTm9kZWpzU3RyZWFtSW5wdXRBZGFwdGVyLnByb3RvdHlwZS5fYmluZFN0cmVhbSA9IGZ1bmN0aW9uIChzdHJlYW0pIHtcbiAgICB2YXIgc2VsZiA9IHRoaXM7XG4gICAgdGhpcy5fc3RyZWFtID0gc3RyZWFtO1xuICAgIHN0cmVhbS5wYXVzZSgpO1xuICAgIHN0cmVhbVxuICAgICAgICAub24oXCJkYXRhXCIsIGZ1bmN0aW9uIChjaHVuaykge1xuICAgICAgICAgICAgc2VsZi5wdXNoKHtcbiAgICAgICAgICAgICAgICBkYXRhOiBjaHVuayxcbiAgICAgICAgICAgICAgICBtZXRhIDoge1xuICAgICAgICAgICAgICAgICAgICBwZXJjZW50IDogMFxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9KVxuICAgICAgICAub24oXCJlcnJvclwiLCBmdW5jdGlvbiAoZSkge1xuICAgICAgICAgICAgaWYoc2VsZi5pc1BhdXNlZCkge1xuICAgICAgICAgICAgICAgIHRoaXMuZ2VuZXJhdGVkRXJyb3IgPSBlO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBzZWxmLmVycm9yKGUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KVxuICAgICAgICAub24oXCJlbmRcIiwgZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgaWYoc2VsZi5pc1BhdXNlZCkge1xuICAgICAgICAgICAgICAgIHNlbGYuX3Vwc3RyZWFtRW5kZWQgPSB0cnVlO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBzZWxmLmVuZCgpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbn07XG5Ob2RlanNTdHJlYW1JbnB1dEFkYXB0ZXIucHJvdG90eXBlLnBhdXNlID0gZnVuY3Rpb24gKCkge1xuICAgIGlmKCFHZW5lcmljV29ya2VyLnByb3RvdHlwZS5wYXVzZS5jYWxsKHRoaXMpKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgdGhpcy5fc3RyZWFtLnBhdXNlKCk7XG4gICAgcmV0dXJuIHRydWU7XG59O1xuTm9kZWpzU3RyZWFtSW5wdXRBZGFwdGVyLnByb3RvdHlwZS5yZXN1bWUgPSBmdW5jdGlvbiAoKSB7XG4gICAgaWYoIUdlbmVyaWNXb3JrZXIucHJvdG90eXBlLnJlc3VtZS5jYWxsKHRoaXMpKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG5cbiAgICBpZih0aGlzLl91cHN0cmVhbUVuZGVkKSB7XG4gICAgICAgIHRoaXMuZW5kKCk7XG4gICAgfSBlbHNlIHtcbiAgICAgICAgdGhpcy5fc3RyZWFtLnJlc3VtZSgpO1xuICAgIH1cblxuICAgIHJldHVybiB0cnVlO1xufTtcblxubW9kdWxlLmV4cG9ydHMgPSBOb2RlanNTdHJlYW1JbnB1dEFkYXB0ZXI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js":
/*!********************************************************************!*\
  !*** ./node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Readable = (__webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\").Readable);\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nutils.inherits(NodejsStreamOutputAdapter, Readable);\n\n/**\n* A nodejs stream using a worker as source.\n* @see the SourceWrapper in http://nodejs.org/api/stream.html\n* @constructor\n* @param {StreamHelper} helper the helper wrapping the worker\n* @param {Object} options the nodejs stream options\n* @param {Function} updateCb the update callback.\n*/\nfunction NodejsStreamOutputAdapter(helper, options, updateCb) {\n    Readable.call(this, options);\n    this._helper = helper;\n\n    var self = this;\n    helper.on(\"data\", function (data, meta) {\n        if (!self.push(data)) {\n            self._helper.pause();\n        }\n        if(updateCb) {\n            updateCb(meta);\n        }\n    })\n        .on(\"error\", function(e) {\n            self.emit(\"error\", e);\n        })\n        .on(\"end\", function () {\n            self.push(null);\n        });\n}\n\n\nNodejsStreamOutputAdapter.prototype._read = function() {\n    this._helper.resume();\n};\n\nmodule.exports = NodejsStreamOutputAdapter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/nodejsUtils.js":
/*!***********************************************!*\
  !*** ./node_modules/jszip/lib/nodejsUtils.js ***!
  \***********************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = {\n    /**\n     * True if this is running in Nodejs, will be undefined in a browser.\n     * In a browser, browserify won't include this file and the whole module\n     * will be resolved an empty object.\n     */\n    isNode : typeof Buffer !== \"undefined\",\n    /**\n     * Create a new nodejs Buffer from an existing content.\n     * @param {Object} data the data to pass to the constructor.\n     * @param {String} encoding the encoding to use.\n     * @return {Buffer} a new Buffer.\n     */\n    newBufferFrom: function(data, encoding) {\n        if (Buffer.from && Buffer.from !== Uint8Array.from) {\n            return Buffer.from(data, encoding);\n        } else {\n            if (typeof data === \"number\") {\n                // Safeguard for old Node.js versions. On newer versions,\n                // Buffer.from(number) / Buffer(number, encoding) already throw.\n                throw new Error(\"The \\\"data\\\" argument must not be a number\");\n            }\n            return new Buffer(data, encoding);\n        }\n    },\n    /**\n     * Create a new nodejs Buffer with the specified size.\n     * @param {Integer} size the size of the buffer.\n     * @return {Buffer} a new Buffer.\n     */\n    allocBuffer: function (size) {\n        if (Buffer.alloc) {\n            return Buffer.alloc(size);\n        } else {\n            var buf = new Buffer(size);\n            buf.fill(0);\n            return buf;\n        }\n    },\n    /**\n     * Find out if an object is a Buffer.\n     * @param {Object} b the object to test.\n     * @return {Boolean} true if the object is a Buffer, false otherwise.\n     */\n    isBuffer : function(b){\n        return Buffer.isBuffer(b);\n    },\n\n    isStream : function (obj) {\n        return obj &&\n            typeof obj.on === \"function\" &&\n            typeof obj.pause === \"function\" &&\n            typeof obj.resume === \"function\";\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/nodejsUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/object.js":
/*!******************************************!*\
  !*** ./node_modules/jszip/lib/object.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar utf8 = __webpack_require__(/*! ./utf8 */ \"(ssr)/./node_modules/jszip/lib/utf8.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\nvar StreamHelper = __webpack_require__(/*! ./stream/StreamHelper */ \"(ssr)/./node_modules/jszip/lib/stream/StreamHelper.js\");\nvar defaults = __webpack_require__(/*! ./defaults */ \"(ssr)/./node_modules/jszip/lib/defaults.js\");\nvar CompressedObject = __webpack_require__(/*! ./compressedObject */ \"(ssr)/./node_modules/jszip/lib/compressedObject.js\");\nvar ZipObject = __webpack_require__(/*! ./zipObject */ \"(ssr)/./node_modules/jszip/lib/zipObject.js\");\nvar generate = __webpack_require__(/*! ./generate */ \"(ssr)/./node_modules/jszip/lib/generate/index.js\");\nvar nodejsUtils = __webpack_require__(/*! ./nodejsUtils */ \"(ssr)/./node_modules/jszip/lib/nodejsUtils.js\");\nvar NodejsStreamInputAdapter = __webpack_require__(/*! ./nodejs/NodejsStreamInputAdapter */ \"(ssr)/./node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js\");\n\n\n/**\n * Add a file in the current folder.\n * @private\n * @param {string} name the name of the file\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the data of the file\n * @param {Object} originalOptions the options of the file\n * @return {Object} the new file.\n */\nvar fileAdd = function(name, data, originalOptions) {\n    // be sure sub folders exist\n    var dataType = utils.getTypeOf(data),\n        parent;\n\n\n    /*\n     * Correct options.\n     */\n\n    var o = utils.extend(originalOptions || {}, defaults);\n    o.date = o.date || new Date();\n    if (o.compression !== null) {\n        o.compression = o.compression.toUpperCase();\n    }\n\n    if (typeof o.unixPermissions === \"string\") {\n        o.unixPermissions = parseInt(o.unixPermissions, 8);\n    }\n\n    // UNX_IFDIR  0040000 see zipinfo.c\n    if (o.unixPermissions && (o.unixPermissions & 0x4000)) {\n        o.dir = true;\n    }\n    // Bit 4    Directory\n    if (o.dosPermissions && (o.dosPermissions & 0x0010)) {\n        o.dir = true;\n    }\n\n    if (o.dir) {\n        name = forceTrailingSlash(name);\n    }\n    if (o.createFolders && (parent = parentFolder(name))) {\n        folderAdd.call(this, parent, true);\n    }\n\n    var isUnicodeString = dataType === \"string\" && o.binary === false && o.base64 === false;\n    if (!originalOptions || typeof originalOptions.binary === \"undefined\") {\n        o.binary = !isUnicodeString;\n    }\n\n\n    var isCompressedEmpty = (data instanceof CompressedObject) && data.uncompressedSize === 0;\n\n    if (isCompressedEmpty || o.dir || !data || data.length === 0) {\n        o.base64 = false;\n        o.binary = true;\n        data = \"\";\n        o.compression = \"STORE\";\n        dataType = \"string\";\n    }\n\n    /*\n     * Convert content to fit.\n     */\n\n    var zipObjectContent = null;\n    if (data instanceof CompressedObject || data instanceof GenericWorker) {\n        zipObjectContent = data;\n    } else if (nodejsUtils.isNode && nodejsUtils.isStream(data)) {\n        zipObjectContent = new NodejsStreamInputAdapter(name, data);\n    } else {\n        zipObjectContent = utils.prepareContent(name, data, o.binary, o.optimizedBinaryString, o.base64);\n    }\n\n    var object = new ZipObject(name, zipObjectContent, o);\n    this.files[name] = object;\n    /*\n    TODO: we can't throw an exception because we have async promises\n    (we can have a promise of a Date() for example) but returning a\n    promise is useless because file(name, data) returns the JSZip\n    object for chaining. Should we break that to allow the user\n    to catch the error ?\n\n    return external.Promise.resolve(zipObjectContent)\n    .then(function () {\n        return object;\n    });\n    */\n};\n\n/**\n * Find the parent folder of the path.\n * @private\n * @param {string} path the path to use\n * @return {string} the parent folder, or \"\"\n */\nvar parentFolder = function (path) {\n    if (path.slice(-1) === \"/\") {\n        path = path.substring(0, path.length - 1);\n    }\n    var lastSlash = path.lastIndexOf(\"/\");\n    return (lastSlash > 0) ? path.substring(0, lastSlash) : \"\";\n};\n\n/**\n * Returns the path with a slash at the end.\n * @private\n * @param {String} path the path to check.\n * @return {String} the path with a trailing slash.\n */\nvar forceTrailingSlash = function(path) {\n    // Check the name ends with a /\n    if (path.slice(-1) !== \"/\") {\n        path += \"/\"; // IE doesn't like substr(-1)\n    }\n    return path;\n};\n\n/**\n * Add a (sub) folder in the current folder.\n * @private\n * @param {string} name the folder's name\n * @param {boolean=} [createFolders] If true, automatically create sub\n *  folders. Defaults to false.\n * @return {Object} the new folder.\n */\nvar folderAdd = function(name, createFolders) {\n    createFolders = (typeof createFolders !== \"undefined\") ? createFolders : defaults.createFolders;\n\n    name = forceTrailingSlash(name);\n\n    // Does this folder already exist?\n    if (!this.files[name]) {\n        fileAdd.call(this, name, null, {\n            dir: true,\n            createFolders: createFolders\n        });\n    }\n    return this.files[name];\n};\n\n/**\n* Cross-window, cross-Node-context regular expression detection\n* @param  {Object}  object Anything\n* @return {Boolean}        true if the object is a regular expression,\n* false otherwise\n*/\nfunction isRegExp(object) {\n    return Object.prototype.toString.call(object) === \"[object RegExp]\";\n}\n\n// return the actual prototype of JSZip\nvar out = {\n    /**\n     * @see loadAsync\n     */\n    load: function() {\n        throw new Error(\"This method has been removed in JSZip 3.0, please check the upgrade guide.\");\n    },\n\n\n    /**\n     * Call a callback function for each entry at this folder level.\n     * @param {Function} cb the callback function:\n     * function (relativePath, file) {...}\n     * It takes 2 arguments : the relative path and the file.\n     */\n    forEach: function(cb) {\n        var filename, relativePath, file;\n        // ignore warning about unwanted properties because this.files is a null prototype object\n        /* eslint-disable-next-line guard-for-in */\n        for (filename in this.files) {\n            file = this.files[filename];\n            relativePath = filename.slice(this.root.length, filename.length);\n            if (relativePath && filename.slice(0, this.root.length) === this.root) { // the file is in the current root\n                cb(relativePath, file); // TODO reverse the parameters ? need to be clean AND consistent with the filter search fn...\n            }\n        }\n    },\n\n    /**\n     * Filter nested files/folders with the specified function.\n     * @param {Function} search the predicate to use :\n     * function (relativePath, file) {...}\n     * It takes 2 arguments : the relative path and the file.\n     * @return {Array} An array of matching elements.\n     */\n    filter: function(search) {\n        var result = [];\n        this.forEach(function (relativePath, entry) {\n            if (search(relativePath, entry)) { // the file matches the function\n                result.push(entry);\n            }\n\n        });\n        return result;\n    },\n\n    /**\n     * Add a file to the zip file, or search a file.\n     * @param   {string|RegExp} name The name of the file to add (if data is defined),\n     * the name of the file to find (if no data) or a regex to match files.\n     * @param   {String|ArrayBuffer|Uint8Array|Buffer} data  The file data, either raw or base64 encoded\n     * @param   {Object} o     File options\n     * @return  {JSZip|Object|Array} this JSZip object (when adding a file),\n     * a file (when searching by string) or an array of files (when searching by regex).\n     */\n    file: function(name, data, o) {\n        if (arguments.length === 1) {\n            if (isRegExp(name)) {\n                var regexp = name;\n                return this.filter(function(relativePath, file) {\n                    return !file.dir && regexp.test(relativePath);\n                });\n            }\n            else { // text\n                var obj = this.files[this.root + name];\n                if (obj && !obj.dir) {\n                    return obj;\n                } else {\n                    return null;\n                }\n            }\n        }\n        else { // more than one argument : we have data !\n            name = this.root + name;\n            fileAdd.call(this, name, data, o);\n        }\n        return this;\n    },\n\n    /**\n     * Add a directory to the zip file, or search.\n     * @param   {String|RegExp} arg The name of the directory to add, or a regex to search folders.\n     * @return  {JSZip} an object with the new directory as the root, or an array containing matching folders.\n     */\n    folder: function(arg) {\n        if (!arg) {\n            return this;\n        }\n\n        if (isRegExp(arg)) {\n            return this.filter(function(relativePath, file) {\n                return file.dir && arg.test(relativePath);\n            });\n        }\n\n        // else, name is a new folder\n        var name = this.root + arg;\n        var newFolder = folderAdd.call(this, name);\n\n        // Allow chaining by returning a new object with this folder as the root\n        var ret = this.clone();\n        ret.root = newFolder.name;\n        return ret;\n    },\n\n    /**\n     * Delete a file, or a directory and all sub-files, from the zip\n     * @param {string} name the name of the file to delete\n     * @return {JSZip} this JSZip object\n     */\n    remove: function(name) {\n        name = this.root + name;\n        var file = this.files[name];\n        if (!file) {\n            // Look for any folders\n            if (name.slice(-1) !== \"/\") {\n                name += \"/\";\n            }\n            file = this.files[name];\n        }\n\n        if (file && !file.dir) {\n            // file\n            delete this.files[name];\n        } else {\n            // maybe a folder, delete recursively\n            var kids = this.filter(function(relativePath, file) {\n                return file.name.slice(0, name.length) === name;\n            });\n            for (var i = 0; i < kids.length; i++) {\n                delete this.files[kids[i].name];\n            }\n        }\n\n        return this;\n    },\n\n    /**\n     * @deprecated This method has been removed in JSZip 3.0, please check the upgrade guide.\n     */\n    generate: function() {\n        throw new Error(\"This method has been removed in JSZip 3.0, please check the upgrade guide.\");\n    },\n\n    /**\n     * Generate the complete zip file as an internal stream.\n     * @param {Object} options the options to generate the zip file :\n     * - compression, \"STORE\" by default.\n     * - type, \"base64\" by default. Values are : string, base64, uint8array, arraybuffer, blob.\n     * @return {StreamHelper} the streamed zip file.\n     */\n    generateInternalStream: function(options) {\n        var worker, opts = {};\n        try {\n            opts = utils.extend(options || {}, {\n                streamFiles: false,\n                compression: \"STORE\",\n                compressionOptions : null,\n                type: \"\",\n                platform: \"DOS\",\n                comment: null,\n                mimeType: \"application/zip\",\n                encodeFileName: utf8.utf8encode\n            });\n\n            opts.type = opts.type.toLowerCase();\n            opts.compression = opts.compression.toUpperCase();\n\n            // \"binarystring\" is preferred but the internals use \"string\".\n            if(opts.type === \"binarystring\") {\n                opts.type = \"string\";\n            }\n\n            if (!opts.type) {\n                throw new Error(\"No output type specified.\");\n            }\n\n            utils.checkSupport(opts.type);\n\n            // accept nodejs `process.platform`\n            if(\n                opts.platform === \"darwin\" ||\n                opts.platform === \"freebsd\" ||\n                opts.platform === \"linux\" ||\n                opts.platform === \"sunos\"\n            ) {\n                opts.platform = \"UNIX\";\n            }\n            if (opts.platform === \"win32\") {\n                opts.platform = \"DOS\";\n            }\n\n            var comment = opts.comment || this.comment || \"\";\n            worker = generate.generateWorker(this, opts, comment);\n        } catch (e) {\n            worker = new GenericWorker(\"error\");\n            worker.error(e);\n        }\n        return new StreamHelper(worker, opts.type || \"string\", opts.mimeType);\n    },\n    /**\n     * Generate the complete zip file asynchronously.\n     * @see generateInternalStream\n     */\n    generateAsync: function(options, onUpdate) {\n        return this.generateInternalStream(options).accumulate(onUpdate);\n    },\n    /**\n     * Generate the complete zip file asynchronously.\n     * @see generateInternalStream\n     */\n    generateNodeStream: function(options, onUpdate) {\n        options = options || {};\n        if (!options.type) {\n            options.type = \"nodebuffer\";\n        }\n        return this.generateInternalStream(options).toNodejsStream(onUpdate);\n    }\n};\nmodule.exports = out;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/object.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/reader/ArrayReader.js":
/*!******************************************************!*\
  !*** ./node_modules/jszip/lib/reader/ArrayReader.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar DataReader = __webpack_require__(/*! ./DataReader */ \"(ssr)/./node_modules/jszip/lib/reader/DataReader.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\n\nfunction ArrayReader(data) {\n    DataReader.call(this, data);\n    for(var i = 0; i < this.data.length; i++) {\n        data[i] = data[i] & 0xFF;\n    }\n}\nutils.inherits(ArrayReader, DataReader);\n/**\n * @see DataReader.byteAt\n */\nArrayReader.prototype.byteAt = function(i) {\n    return this.data[this.zero + i];\n};\n/**\n * @see DataReader.lastIndexOfSignature\n */\nArrayReader.prototype.lastIndexOfSignature = function(sig) {\n    var sig0 = sig.charCodeAt(0),\n        sig1 = sig.charCodeAt(1),\n        sig2 = sig.charCodeAt(2),\n        sig3 = sig.charCodeAt(3);\n    for (var i = this.length - 4; i >= 0; --i) {\n        if (this.data[i] === sig0 && this.data[i + 1] === sig1 && this.data[i + 2] === sig2 && this.data[i + 3] === sig3) {\n            return i - this.zero;\n        }\n    }\n\n    return -1;\n};\n/**\n * @see DataReader.readAndCheckSignature\n */\nArrayReader.prototype.readAndCheckSignature = function (sig) {\n    var sig0 = sig.charCodeAt(0),\n        sig1 = sig.charCodeAt(1),\n        sig2 = sig.charCodeAt(2),\n        sig3 = sig.charCodeAt(3),\n        data = this.readData(4);\n    return sig0 === data[0] && sig1 === data[1] && sig2 === data[2] && sig3 === data[3];\n};\n/**\n * @see DataReader.readData\n */\nArrayReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    if(size === 0) {\n        return [];\n    }\n    var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = ArrayReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/reader/ArrayReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/reader/DataReader.js":
/*!*****************************************************!*\
  !*** ./node_modules/jszip/lib/reader/DataReader.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\n\nfunction DataReader(data) {\n    this.data = data; // type : see implementation\n    this.length = data.length;\n    this.index = 0;\n    this.zero = 0;\n}\nDataReader.prototype = {\n    /**\n     * Check that the offset will not go too far.\n     * @param {string} offset the additional offset to check.\n     * @throws {Error} an Error if the offset is out of bounds.\n     */\n    checkOffset: function(offset) {\n        this.checkIndex(this.index + offset);\n    },\n    /**\n     * Check that the specified index will not be too far.\n     * @param {string} newIndex the index to check.\n     * @throws {Error} an Error if the index is out of bounds.\n     */\n    checkIndex: function(newIndex) {\n        if (this.length < this.zero + newIndex || newIndex < 0) {\n            throw new Error(\"End of data reached (data length = \" + this.length + \", asked index = \" + (newIndex) + \"). Corrupted zip ?\");\n        }\n    },\n    /**\n     * Change the index.\n     * @param {number} newIndex The new index.\n     * @throws {Error} if the new index is out of the data.\n     */\n    setIndex: function(newIndex) {\n        this.checkIndex(newIndex);\n        this.index = newIndex;\n    },\n    /**\n     * Skip the next n bytes.\n     * @param {number} n the number of bytes to skip.\n     * @throws {Error} if the new index is out of the data.\n     */\n    skip: function(n) {\n        this.setIndex(this.index + n);\n    },\n    /**\n     * Get the byte at the specified index.\n     * @param {number} i the index to use.\n     * @return {number} a byte.\n     */\n    byteAt: function() {\n        // see implementations\n    },\n    /**\n     * Get the next number with a given byte size.\n     * @param {number} size the number of bytes to read.\n     * @return {number} the corresponding number.\n     */\n    readInt: function(size) {\n        var result = 0,\n            i;\n        this.checkOffset(size);\n        for (i = this.index + size - 1; i >= this.index; i--) {\n            result = (result << 8) + this.byteAt(i);\n        }\n        this.index += size;\n        return result;\n    },\n    /**\n     * Get the next string with a given byte size.\n     * @param {number} size the number of bytes to read.\n     * @return {string} the corresponding string.\n     */\n    readString: function(size) {\n        return utils.transformTo(\"string\", this.readData(size));\n    },\n    /**\n     * Get raw data without conversion, <size> bytes.\n     * @param {number} size the number of bytes to read.\n     * @return {Object} the raw data, implementation specific.\n     */\n    readData: function() {\n        // see implementations\n    },\n    /**\n     * Find the last occurrence of a zip signature (4 bytes).\n     * @param {string} sig the signature to find.\n     * @return {number} the index of the last occurrence, -1 if not found.\n     */\n    lastIndexOfSignature: function() {\n        // see implementations\n    },\n    /**\n     * Read the signature (4 bytes) at the current position and compare it with sig.\n     * @param {string} sig the expected signature\n     * @return {boolean} true if the signature matches, false otherwise.\n     */\n    readAndCheckSignature: function() {\n        // see implementations\n    },\n    /**\n     * Get the next date.\n     * @return {Date} the date.\n     */\n    readDate: function() {\n        var dostime = this.readInt(4);\n        return new Date(Date.UTC(\n            ((dostime >> 25) & 0x7f) + 1980, // year\n            ((dostime >> 21) & 0x0f) - 1, // month\n            (dostime >> 16) & 0x1f, // day\n            (dostime >> 11) & 0x1f, // hour\n            (dostime >> 5) & 0x3f, // minute\n            (dostime & 0x1f) << 1)); // second\n    }\n};\nmodule.exports = DataReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/reader/DataReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/reader/NodeBufferReader.js":
/*!***********************************************************!*\
  !*** ./node_modules/jszip/lib/reader/NodeBufferReader.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar Uint8ArrayReader = __webpack_require__(/*! ./Uint8ArrayReader */ \"(ssr)/./node_modules/jszip/lib/reader/Uint8ArrayReader.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\n\nfunction NodeBufferReader(data) {\n    Uint8ArrayReader.call(this, data);\n}\nutils.inherits(NodeBufferReader, Uint8ArrayReader);\n\n/**\n * @see DataReader.readData\n */\nNodeBufferReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = NodeBufferReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL3JlYWRlci9Ob2RlQnVmZmVyUmVhZGVyLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsdUJBQXVCLG1CQUFPLENBQUMscUZBQW9CO0FBQ25ELFlBQVksbUJBQU8sQ0FBQyx5REFBVTs7QUFFOUI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xceHlpZW5cXE9uZURyaXZlXFxEZXNrdG9wXFxBbGxKb3VybmFsXFxqb3VybmFsLWFwcFxcbm9kZV9tb2R1bGVzXFxqc3ppcFxcbGliXFxyZWFkZXJcXE5vZGVCdWZmZXJSZWFkZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG52YXIgVWludDhBcnJheVJlYWRlciA9IHJlcXVpcmUoXCIuL1VpbnQ4QXJyYXlSZWFkZXJcIik7XG52YXIgdXRpbHMgPSByZXF1aXJlKFwiLi4vdXRpbHNcIik7XG5cbmZ1bmN0aW9uIE5vZGVCdWZmZXJSZWFkZXIoZGF0YSkge1xuICAgIFVpbnQ4QXJyYXlSZWFkZXIuY2FsbCh0aGlzLCBkYXRhKTtcbn1cbnV0aWxzLmluaGVyaXRzKE5vZGVCdWZmZXJSZWFkZXIsIFVpbnQ4QXJyYXlSZWFkZXIpO1xuXG4vKipcbiAqIEBzZWUgRGF0YVJlYWRlci5yZWFkRGF0YVxuICovXG5Ob2RlQnVmZmVyUmVhZGVyLnByb3RvdHlwZS5yZWFkRGF0YSA9IGZ1bmN0aW9uKHNpemUpIHtcbiAgICB0aGlzLmNoZWNrT2Zmc2V0KHNpemUpO1xuICAgIHZhciByZXN1bHQgPSB0aGlzLmRhdGEuc2xpY2UodGhpcy56ZXJvICsgdGhpcy5pbmRleCwgdGhpcy56ZXJvICsgdGhpcy5pbmRleCArIHNpemUpO1xuICAgIHRoaXMuaW5kZXggKz0gc2l6ZTtcbiAgICByZXR1cm4gcmVzdWx0O1xufTtcbm1vZHVsZS5leHBvcnRzID0gTm9kZUJ1ZmZlclJlYWRlcjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/reader/NodeBufferReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/reader/StringReader.js":
/*!*******************************************************!*\
  !*** ./node_modules/jszip/lib/reader/StringReader.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar DataReader = __webpack_require__(/*! ./DataReader */ \"(ssr)/./node_modules/jszip/lib/reader/DataReader.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\n\nfunction StringReader(data) {\n    DataReader.call(this, data);\n}\nutils.inherits(StringReader, DataReader);\n/**\n * @see DataReader.byteAt\n */\nStringReader.prototype.byteAt = function(i) {\n    return this.data.charCodeAt(this.zero + i);\n};\n/**\n * @see DataReader.lastIndexOfSignature\n */\nStringReader.prototype.lastIndexOfSignature = function(sig) {\n    return this.data.lastIndexOf(sig) - this.zero;\n};\n/**\n * @see DataReader.readAndCheckSignature\n */\nStringReader.prototype.readAndCheckSignature = function (sig) {\n    var data = this.readData(4);\n    return sig === data;\n};\n/**\n * @see DataReader.readData\n */\nStringReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    // this will work because the constructor applied the \"& 0xff\" mask.\n    var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = StringReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/reader/StringReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/reader/Uint8ArrayReader.js":
/*!***********************************************************!*\
  !*** ./node_modules/jszip/lib/reader/Uint8ArrayReader.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar ArrayReader = __webpack_require__(/*! ./ArrayReader */ \"(ssr)/./node_modules/jszip/lib/reader/ArrayReader.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\n\nfunction Uint8ArrayReader(data) {\n    ArrayReader.call(this, data);\n}\nutils.inherits(Uint8ArrayReader, ArrayReader);\n/**\n * @see DataReader.readData\n */\nUint8ArrayReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    if(size === 0) {\n        // in IE10, when using subarray(idx, idx), we get the array [0x00] instead of [].\n        return new Uint8Array(0);\n    }\n    var result = this.data.subarray(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = Uint8ArrayReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL3JlYWRlci9VaW50OEFycmF5UmVhZGVyLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2Isa0JBQWtCLG1CQUFPLENBQUMsMkVBQWU7QUFDekMsWUFBWSxtQkFBTyxDQUFDLHlEQUFVOztBQUU5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xceHlpZW5cXE9uZURyaXZlXFxEZXNrdG9wXFxBbGxKb3VybmFsXFxqb3VybmFsLWFwcFxcbm9kZV9tb2R1bGVzXFxqc3ppcFxcbGliXFxyZWFkZXJcXFVpbnQ4QXJyYXlSZWFkZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG52YXIgQXJyYXlSZWFkZXIgPSByZXF1aXJlKFwiLi9BcnJheVJlYWRlclwiKTtcbnZhciB1dGlscyA9IHJlcXVpcmUoXCIuLi91dGlsc1wiKTtcblxuZnVuY3Rpb24gVWludDhBcnJheVJlYWRlcihkYXRhKSB7XG4gICAgQXJyYXlSZWFkZXIuY2FsbCh0aGlzLCBkYXRhKTtcbn1cbnV0aWxzLmluaGVyaXRzKFVpbnQ4QXJyYXlSZWFkZXIsIEFycmF5UmVhZGVyKTtcbi8qKlxuICogQHNlZSBEYXRhUmVhZGVyLnJlYWREYXRhXG4gKi9cblVpbnQ4QXJyYXlSZWFkZXIucHJvdG90eXBlLnJlYWREYXRhID0gZnVuY3Rpb24oc2l6ZSkge1xuICAgIHRoaXMuY2hlY2tPZmZzZXQoc2l6ZSk7XG4gICAgaWYoc2l6ZSA9PT0gMCkge1xuICAgICAgICAvLyBpbiBJRTEwLCB3aGVuIHVzaW5nIHN1YmFycmF5KGlkeCwgaWR4KSwgd2UgZ2V0IHRoZSBhcnJheSBbMHgwMF0gaW5zdGVhZCBvZiBbXS5cbiAgICAgICAgcmV0dXJuIG5ldyBVaW50OEFycmF5KDApO1xuICAgIH1cbiAgICB2YXIgcmVzdWx0ID0gdGhpcy5kYXRhLnN1YmFycmF5KHRoaXMuemVybyArIHRoaXMuaW5kZXgsIHRoaXMuemVybyArIHRoaXMuaW5kZXggKyBzaXplKTtcbiAgICB0aGlzLmluZGV4ICs9IHNpemU7XG4gICAgcmV0dXJuIHJlc3VsdDtcbn07XG5tb2R1bGUuZXhwb3J0cyA9IFVpbnQ4QXJyYXlSZWFkZXI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/reader/Uint8ArrayReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/reader/readerFor.js":
/*!****************************************************!*\
  !*** ./node_modules/jszip/lib/reader/readerFor.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar support = __webpack_require__(/*! ../support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\nvar ArrayReader = __webpack_require__(/*! ./ArrayReader */ \"(ssr)/./node_modules/jszip/lib/reader/ArrayReader.js\");\nvar StringReader = __webpack_require__(/*! ./StringReader */ \"(ssr)/./node_modules/jszip/lib/reader/StringReader.js\");\nvar NodeBufferReader = __webpack_require__(/*! ./NodeBufferReader */ \"(ssr)/./node_modules/jszip/lib/reader/NodeBufferReader.js\");\nvar Uint8ArrayReader = __webpack_require__(/*! ./Uint8ArrayReader */ \"(ssr)/./node_modules/jszip/lib/reader/Uint8ArrayReader.js\");\n\n/**\n * Create a reader adapted to the data.\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the data to read.\n * @return {DataReader} the data reader.\n */\nmodule.exports = function (data) {\n    var type = utils.getTypeOf(data);\n    utils.checkSupport(type);\n    if (type === \"string\" && !support.uint8array) {\n        return new StringReader(data);\n    }\n    if (type === \"nodebuffer\") {\n        return new NodeBufferReader(data);\n    }\n    if (support.uint8array) {\n        return new Uint8ArrayReader(utils.transformTo(\"uint8array\", data));\n    }\n    return new ArrayReader(utils.transformTo(\"array\", data));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/reader/readerFor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/signature.js":
/*!*********************************************!*\
  !*** ./node_modules/jszip/lib/signature.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nexports.LOCAL_FILE_HEADER = \"PK\\x03\\x04\";\nexports.CENTRAL_FILE_HEADER = \"PK\\x01\\x02\";\nexports.CENTRAL_DIRECTORY_END = \"PK\\x05\\x06\";\nexports.ZIP64_CENTRAL_DIRECTORY_LOCATOR = \"PK\\x06\\x07\";\nexports.ZIP64_CENTRAL_DIRECTORY_END = \"PK\\x06\\x06\";\nexports.DATA_DESCRIPTOR = \"PK\\x07\\x08\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL3NpZ25hdHVyZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLHlCQUF5QjtBQUN6QiwyQkFBMkI7QUFDM0IsNkJBQTZCO0FBQzdCLHVDQUF1QztBQUN2QyxtQ0FBbUM7QUFDbkMsdUJBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHh5aWVuXFxPbmVEcml2ZVxcRGVza3RvcFxcQWxsSm91cm5hbFxcam91cm5hbC1hcHBcXG5vZGVfbW9kdWxlc1xcanN6aXBcXGxpYlxcc2lnbmF0dXJlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuZXhwb3J0cy5MT0NBTF9GSUxFX0hFQURFUiA9IFwiUEtcXHgwM1xceDA0XCI7XG5leHBvcnRzLkNFTlRSQUxfRklMRV9IRUFERVIgPSBcIlBLXFx4MDFcXHgwMlwiO1xuZXhwb3J0cy5DRU5UUkFMX0RJUkVDVE9SWV9FTkQgPSBcIlBLXFx4MDVcXHgwNlwiO1xuZXhwb3J0cy5aSVA2NF9DRU5UUkFMX0RJUkVDVE9SWV9MT0NBVE9SID0gXCJQS1xceDA2XFx4MDdcIjtcbmV4cG9ydHMuWklQNjRfQ0VOVFJBTF9ESVJFQ1RPUllfRU5EID0gXCJQS1xceDA2XFx4MDZcIjtcbmV4cG9ydHMuREFUQV9ERVNDUklQVE9SID0gXCJQS1xceDA3XFx4MDhcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/signature.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/stream/ConvertWorker.js":
/*!********************************************************!*\
  !*** ./node_modules/jszip/lib/stream/ConvertWorker.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\n\n/**\n * A worker which convert chunks to a specified type.\n * @constructor\n * @param {String} destType the destination type.\n */\nfunction ConvertWorker(destType) {\n    GenericWorker.call(this, \"ConvertWorker to \" + destType);\n    this.destType = destType;\n}\nutils.inherits(ConvertWorker, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nConvertWorker.prototype.processChunk = function (chunk) {\n    this.push({\n        data : utils.transformTo(this.destType, chunk.data),\n        meta : chunk.meta\n    });\n};\nmodule.exports = ConvertWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL3N0cmVhbS9Db252ZXJ0V29ya2VyLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLG9CQUFvQixtQkFBTyxDQUFDLCtFQUFpQjtBQUM3QyxZQUFZLG1CQUFPLENBQUMseURBQVU7O0FBRTlCO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx4eWllblxcT25lRHJpdmVcXERlc2t0b3BcXEFsbEpvdXJuYWxcXGpvdXJuYWwtYXBwXFxub2RlX21vZHVsZXNcXGpzemlwXFxsaWJcXHN0cmVhbVxcQ29udmVydFdvcmtlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIEdlbmVyaWNXb3JrZXIgPSByZXF1aXJlKFwiLi9HZW5lcmljV29ya2VyXCIpO1xudmFyIHV0aWxzID0gcmVxdWlyZShcIi4uL3V0aWxzXCIpO1xuXG4vKipcbiAqIEEgd29ya2VyIHdoaWNoIGNvbnZlcnQgY2h1bmtzIHRvIGEgc3BlY2lmaWVkIHR5cGUuXG4gKiBAY29uc3RydWN0b3JcbiAqIEBwYXJhbSB7U3RyaW5nfSBkZXN0VHlwZSB0aGUgZGVzdGluYXRpb24gdHlwZS5cbiAqL1xuZnVuY3Rpb24gQ29udmVydFdvcmtlcihkZXN0VHlwZSkge1xuICAgIEdlbmVyaWNXb3JrZXIuY2FsbCh0aGlzLCBcIkNvbnZlcnRXb3JrZXIgdG8gXCIgKyBkZXN0VHlwZSk7XG4gICAgdGhpcy5kZXN0VHlwZSA9IGRlc3RUeXBlO1xufVxudXRpbHMuaW5oZXJpdHMoQ29udmVydFdvcmtlciwgR2VuZXJpY1dvcmtlcik7XG5cbi8qKlxuICogQHNlZSBHZW5lcmljV29ya2VyLnByb2Nlc3NDaHVua1xuICovXG5Db252ZXJ0V29ya2VyLnByb3RvdHlwZS5wcm9jZXNzQ2h1bmsgPSBmdW5jdGlvbiAoY2h1bmspIHtcbiAgICB0aGlzLnB1c2goe1xuICAgICAgICBkYXRhIDogdXRpbHMudHJhbnNmb3JtVG8odGhpcy5kZXN0VHlwZSwgY2h1bmsuZGF0YSksXG4gICAgICAgIG1ldGEgOiBjaHVuay5tZXRhXG4gICAgfSk7XG59O1xubW9kdWxlLmV4cG9ydHMgPSBDb252ZXJ0V29ya2VyO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/stream/ConvertWorker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/stream/Crc32Probe.js":
/*!*****************************************************!*\
  !*** ./node_modules/jszip/lib/stream/Crc32Probe.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\nvar crc32 = __webpack_require__(/*! ../crc32 */ \"(ssr)/./node_modules/jszip/lib/crc32.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\n\n/**\n * A worker which calculate the crc32 of the data flowing through.\n * @constructor\n */\nfunction Crc32Probe() {\n    GenericWorker.call(this, \"Crc32Probe\");\n    this.withStreamInfo(\"crc32\", 0);\n}\nutils.inherits(Crc32Probe, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nCrc32Probe.prototype.processChunk = function (chunk) {\n    this.streamInfo.crc32 = crc32(chunk.data, this.streamInfo.crc32 || 0);\n    this.push(chunk);\n};\nmodule.exports = Crc32Probe;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL3N0cmVhbS9DcmMzMlByb2JlLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLG9CQUFvQixtQkFBTyxDQUFDLCtFQUFpQjtBQUM3QyxZQUFZLG1CQUFPLENBQUMseURBQVU7QUFDOUIsWUFBWSxtQkFBTyxDQUFDLHlEQUFVOztBQUU5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx4eWllblxcT25lRHJpdmVcXERlc2t0b3BcXEFsbEpvdXJuYWxcXGpvdXJuYWwtYXBwXFxub2RlX21vZHVsZXNcXGpzemlwXFxsaWJcXHN0cmVhbVxcQ3JjMzJQcm9iZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIEdlbmVyaWNXb3JrZXIgPSByZXF1aXJlKFwiLi9HZW5lcmljV29ya2VyXCIpO1xudmFyIGNyYzMyID0gcmVxdWlyZShcIi4uL2NyYzMyXCIpO1xudmFyIHV0aWxzID0gcmVxdWlyZShcIi4uL3V0aWxzXCIpO1xuXG4vKipcbiAqIEEgd29ya2VyIHdoaWNoIGNhbGN1bGF0ZSB0aGUgY3JjMzIgb2YgdGhlIGRhdGEgZmxvd2luZyB0aHJvdWdoLlxuICogQGNvbnN0cnVjdG9yXG4gKi9cbmZ1bmN0aW9uIENyYzMyUHJvYmUoKSB7XG4gICAgR2VuZXJpY1dvcmtlci5jYWxsKHRoaXMsIFwiQ3JjMzJQcm9iZVwiKTtcbiAgICB0aGlzLndpdGhTdHJlYW1JbmZvKFwiY3JjMzJcIiwgMCk7XG59XG51dGlscy5pbmhlcml0cyhDcmMzMlByb2JlLCBHZW5lcmljV29ya2VyKTtcblxuLyoqXG4gKiBAc2VlIEdlbmVyaWNXb3JrZXIucHJvY2Vzc0NodW5rXG4gKi9cbkNyYzMyUHJvYmUucHJvdG90eXBlLnByb2Nlc3NDaHVuayA9IGZ1bmN0aW9uIChjaHVuaykge1xuICAgIHRoaXMuc3RyZWFtSW5mby5jcmMzMiA9IGNyYzMyKGNodW5rLmRhdGEsIHRoaXMuc3RyZWFtSW5mby5jcmMzMiB8fCAwKTtcbiAgICB0aGlzLnB1c2goY2h1bmspO1xufTtcbm1vZHVsZS5leHBvcnRzID0gQ3JjMzJQcm9iZTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/stream/Crc32Probe.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/stream/DataLengthProbe.js":
/*!**********************************************************!*\
  !*** ./node_modules/jszip/lib/stream/DataLengthProbe.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\n\n/**\n * A worker which calculate the total length of the data flowing through.\n * @constructor\n * @param {String} propName the name used to expose the length\n */\nfunction DataLengthProbe(propName) {\n    GenericWorker.call(this, \"DataLengthProbe for \" + propName);\n    this.propName = propName;\n    this.withStreamInfo(propName, 0);\n}\nutils.inherits(DataLengthProbe, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nDataLengthProbe.prototype.processChunk = function (chunk) {\n    if(chunk) {\n        var length = this.streamInfo[this.propName] || 0;\n        this.streamInfo[this.propName] = length + chunk.data.length;\n    }\n    GenericWorker.prototype.processChunk.call(this, chunk);\n};\nmodule.exports = DataLengthProbe;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL3N0cmVhbS9EYXRhTGVuZ3RoUHJvYmUuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsWUFBWSxtQkFBTyxDQUFDLHlEQUFVO0FBQzlCLG9CQUFvQixtQkFBTyxDQUFDLCtFQUFpQjs7QUFFN0M7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xceHlpZW5cXE9uZURyaXZlXFxEZXNrdG9wXFxBbGxKb3VybmFsXFxqb3VybmFsLWFwcFxcbm9kZV9tb2R1bGVzXFxqc3ppcFxcbGliXFxzdHJlYW1cXERhdGFMZW5ndGhQcm9iZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIHV0aWxzID0gcmVxdWlyZShcIi4uL3V0aWxzXCIpO1xudmFyIEdlbmVyaWNXb3JrZXIgPSByZXF1aXJlKFwiLi9HZW5lcmljV29ya2VyXCIpO1xuXG4vKipcbiAqIEEgd29ya2VyIHdoaWNoIGNhbGN1bGF0ZSB0aGUgdG90YWwgbGVuZ3RoIG9mIHRoZSBkYXRhIGZsb3dpbmcgdGhyb3VnaC5cbiAqIEBjb25zdHJ1Y3RvclxuICogQHBhcmFtIHtTdHJpbmd9IHByb3BOYW1lIHRoZSBuYW1lIHVzZWQgdG8gZXhwb3NlIHRoZSBsZW5ndGhcbiAqL1xuZnVuY3Rpb24gRGF0YUxlbmd0aFByb2JlKHByb3BOYW1lKSB7XG4gICAgR2VuZXJpY1dvcmtlci5jYWxsKHRoaXMsIFwiRGF0YUxlbmd0aFByb2JlIGZvciBcIiArIHByb3BOYW1lKTtcbiAgICB0aGlzLnByb3BOYW1lID0gcHJvcE5hbWU7XG4gICAgdGhpcy53aXRoU3RyZWFtSW5mbyhwcm9wTmFtZSwgMCk7XG59XG51dGlscy5pbmhlcml0cyhEYXRhTGVuZ3RoUHJvYmUsIEdlbmVyaWNXb3JrZXIpO1xuXG4vKipcbiAqIEBzZWUgR2VuZXJpY1dvcmtlci5wcm9jZXNzQ2h1bmtcbiAqL1xuRGF0YUxlbmd0aFByb2JlLnByb3RvdHlwZS5wcm9jZXNzQ2h1bmsgPSBmdW5jdGlvbiAoY2h1bmspIHtcbiAgICBpZihjaHVuaykge1xuICAgICAgICB2YXIgbGVuZ3RoID0gdGhpcy5zdHJlYW1JbmZvW3RoaXMucHJvcE5hbWVdIHx8IDA7XG4gICAgICAgIHRoaXMuc3RyZWFtSW5mb1t0aGlzLnByb3BOYW1lXSA9IGxlbmd0aCArIGNodW5rLmRhdGEubGVuZ3RoO1xuICAgIH1cbiAgICBHZW5lcmljV29ya2VyLnByb3RvdHlwZS5wcm9jZXNzQ2h1bmsuY2FsbCh0aGlzLCBjaHVuayk7XG59O1xubW9kdWxlLmV4cG9ydHMgPSBEYXRhTGVuZ3RoUHJvYmU7XG5cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/stream/DataLengthProbe.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/stream/DataWorker.js":
/*!*****************************************************!*\
  !*** ./node_modules/jszip/lib/stream/DataWorker.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\n\n// the size of the generated chunks\n// TODO expose this as a public variable\nvar DEFAULT_BLOCK_SIZE = 16 * 1024;\n\n/**\n * A worker that reads a content and emits chunks.\n * @constructor\n * @param {Promise} dataP the promise of the data to split\n */\nfunction DataWorker(dataP) {\n    GenericWorker.call(this, \"DataWorker\");\n    var self = this;\n    this.dataIsReady = false;\n    this.index = 0;\n    this.max = 0;\n    this.data = null;\n    this.type = \"\";\n\n    this._tickScheduled = false;\n\n    dataP.then(function (data) {\n        self.dataIsReady = true;\n        self.data = data;\n        self.max = data && data.length || 0;\n        self.type = utils.getTypeOf(data);\n        if(!self.isPaused) {\n            self._tickAndRepeat();\n        }\n    }, function (e) {\n        self.error(e);\n    });\n}\n\nutils.inherits(DataWorker, GenericWorker);\n\n/**\n * @see GenericWorker.cleanUp\n */\nDataWorker.prototype.cleanUp = function () {\n    GenericWorker.prototype.cleanUp.call(this);\n    this.data = null;\n};\n\n/**\n * @see GenericWorker.resume\n */\nDataWorker.prototype.resume = function () {\n    if(!GenericWorker.prototype.resume.call(this)) {\n        return false;\n    }\n\n    if (!this._tickScheduled && this.dataIsReady) {\n        this._tickScheduled = true;\n        utils.delay(this._tickAndRepeat, [], this);\n    }\n    return true;\n};\n\n/**\n * Trigger a tick a schedule an other call to this function.\n */\nDataWorker.prototype._tickAndRepeat = function() {\n    this._tickScheduled = false;\n    if(this.isPaused || this.isFinished) {\n        return;\n    }\n    this._tick();\n    if(!this.isFinished) {\n        utils.delay(this._tickAndRepeat, [], this);\n        this._tickScheduled = true;\n    }\n};\n\n/**\n * Read and push a chunk.\n */\nDataWorker.prototype._tick = function() {\n\n    if(this.isPaused || this.isFinished) {\n        return false;\n    }\n\n    var size = DEFAULT_BLOCK_SIZE;\n    var data = null, nextIndex = Math.min(this.max, this.index + size);\n    if (this.index >= this.max) {\n        // EOF\n        return this.end();\n    } else {\n        switch(this.type) {\n        case \"string\":\n            data = this.data.substring(this.index, nextIndex);\n            break;\n        case \"uint8array\":\n            data = this.data.subarray(this.index, nextIndex);\n            break;\n        case \"array\":\n        case \"nodebuffer\":\n            data = this.data.slice(this.index, nextIndex);\n            break;\n        }\n        this.index = nextIndex;\n        return this.push({\n            data : data,\n            meta : {\n                percent : this.max ? this.index / this.max * 100 : 0\n            }\n        });\n    }\n};\n\nmodule.exports = DataWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL3N0cmVhbS9EYXRhV29ya2VyLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLFlBQVksbUJBQU8sQ0FBQyx5REFBVTtBQUM5QixvQkFBb0IsbUJBQU8sQ0FBQywrRUFBaUI7O0FBRTdDO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xceHlpZW5cXE9uZURyaXZlXFxEZXNrdG9wXFxBbGxKb3VybmFsXFxqb3VybmFsLWFwcFxcbm9kZV9tb2R1bGVzXFxqc3ppcFxcbGliXFxzdHJlYW1cXERhdGFXb3JrZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbnZhciB1dGlscyA9IHJlcXVpcmUoXCIuLi91dGlsc1wiKTtcbnZhciBHZW5lcmljV29ya2VyID0gcmVxdWlyZShcIi4vR2VuZXJpY1dvcmtlclwiKTtcblxuLy8gdGhlIHNpemUgb2YgdGhlIGdlbmVyYXRlZCBjaHVua3Ncbi8vIFRPRE8gZXhwb3NlIHRoaXMgYXMgYSBwdWJsaWMgdmFyaWFibGVcbnZhciBERUZBVUxUX0JMT0NLX1NJWkUgPSAxNiAqIDEwMjQ7XG5cbi8qKlxuICogQSB3b3JrZXIgdGhhdCByZWFkcyBhIGNvbnRlbnQgYW5kIGVtaXRzIGNodW5rcy5cbiAqIEBjb25zdHJ1Y3RvclxuICogQHBhcmFtIHtQcm9taXNlfSBkYXRhUCB0aGUgcHJvbWlzZSBvZiB0aGUgZGF0YSB0byBzcGxpdFxuICovXG5mdW5jdGlvbiBEYXRhV29ya2VyKGRhdGFQKSB7XG4gICAgR2VuZXJpY1dvcmtlci5jYWxsKHRoaXMsIFwiRGF0YVdvcmtlclwiKTtcbiAgICB2YXIgc2VsZiA9IHRoaXM7XG4gICAgdGhpcy5kYXRhSXNSZWFkeSA9IGZhbHNlO1xuICAgIHRoaXMuaW5kZXggPSAwO1xuICAgIHRoaXMubWF4ID0gMDtcbiAgICB0aGlzLmRhdGEgPSBudWxsO1xuICAgIHRoaXMudHlwZSA9IFwiXCI7XG5cbiAgICB0aGlzLl90aWNrU2NoZWR1bGVkID0gZmFsc2U7XG5cbiAgICBkYXRhUC50aGVuKGZ1bmN0aW9uIChkYXRhKSB7XG4gICAgICAgIHNlbGYuZGF0YUlzUmVhZHkgPSB0cnVlO1xuICAgICAgICBzZWxmLmRhdGEgPSBkYXRhO1xuICAgICAgICBzZWxmLm1heCA9IGRhdGEgJiYgZGF0YS5sZW5ndGggfHwgMDtcbiAgICAgICAgc2VsZi50eXBlID0gdXRpbHMuZ2V0VHlwZU9mKGRhdGEpO1xuICAgICAgICBpZighc2VsZi5pc1BhdXNlZCkge1xuICAgICAgICAgICAgc2VsZi5fdGlja0FuZFJlcGVhdCgpO1xuICAgICAgICB9XG4gICAgfSwgZnVuY3Rpb24gKGUpIHtcbiAgICAgICAgc2VsZi5lcnJvcihlKTtcbiAgICB9KTtcbn1cblxudXRpbHMuaW5oZXJpdHMoRGF0YVdvcmtlciwgR2VuZXJpY1dvcmtlcik7XG5cbi8qKlxuICogQHNlZSBHZW5lcmljV29ya2VyLmNsZWFuVXBcbiAqL1xuRGF0YVdvcmtlci5wcm90b3R5cGUuY2xlYW5VcCA9IGZ1bmN0aW9uICgpIHtcbiAgICBHZW5lcmljV29ya2VyLnByb3RvdHlwZS5jbGVhblVwLmNhbGwodGhpcyk7XG4gICAgdGhpcy5kYXRhID0gbnVsbDtcbn07XG5cbi8qKlxuICogQHNlZSBHZW5lcmljV29ya2VyLnJlc3VtZVxuICovXG5EYXRhV29ya2VyLnByb3RvdHlwZS5yZXN1bWUgPSBmdW5jdGlvbiAoKSB7XG4gICAgaWYoIUdlbmVyaWNXb3JrZXIucHJvdG90eXBlLnJlc3VtZS5jYWxsKHRoaXMpKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG5cbiAgICBpZiAoIXRoaXMuX3RpY2tTY2hlZHVsZWQgJiYgdGhpcy5kYXRhSXNSZWFkeSkge1xuICAgICAgICB0aGlzLl90aWNrU2NoZWR1bGVkID0gdHJ1ZTtcbiAgICAgICAgdXRpbHMuZGVsYXkodGhpcy5fdGlja0FuZFJlcGVhdCwgW10sIHRoaXMpO1xuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbn07XG5cbi8qKlxuICogVHJpZ2dlciBhIHRpY2sgYSBzY2hlZHVsZSBhbiBvdGhlciBjYWxsIHRvIHRoaXMgZnVuY3Rpb24uXG4gKi9cbkRhdGFXb3JrZXIucHJvdG90eXBlLl90aWNrQW5kUmVwZWF0ID0gZnVuY3Rpb24oKSB7XG4gICAgdGhpcy5fdGlja1NjaGVkdWxlZCA9IGZhbHNlO1xuICAgIGlmKHRoaXMuaXNQYXVzZWQgfHwgdGhpcy5pc0ZpbmlzaGVkKSB7XG4gICAgICAgIHJldHVybjtcbiAgICB9XG4gICAgdGhpcy5fdGljaygpO1xuICAgIGlmKCF0aGlzLmlzRmluaXNoZWQpIHtcbiAgICAgICAgdXRpbHMuZGVsYXkodGhpcy5fdGlja0FuZFJlcGVhdCwgW10sIHRoaXMpO1xuICAgICAgICB0aGlzLl90aWNrU2NoZWR1bGVkID0gdHJ1ZTtcbiAgICB9XG59O1xuXG4vKipcbiAqIFJlYWQgYW5kIHB1c2ggYSBjaHVuay5cbiAqL1xuRGF0YVdvcmtlci5wcm90b3R5cGUuX3RpY2sgPSBmdW5jdGlvbigpIHtcblxuICAgIGlmKHRoaXMuaXNQYXVzZWQgfHwgdGhpcy5pc0ZpbmlzaGVkKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG5cbiAgICB2YXIgc2l6ZSA9IERFRkFVTFRfQkxPQ0tfU0laRTtcbiAgICB2YXIgZGF0YSA9IG51bGwsIG5leHRJbmRleCA9IE1hdGgubWluKHRoaXMubWF4LCB0aGlzLmluZGV4ICsgc2l6ZSk7XG4gICAgaWYgKHRoaXMuaW5kZXggPj0gdGhpcy5tYXgpIHtcbiAgICAgICAgLy8gRU9GXG4gICAgICAgIHJldHVybiB0aGlzLmVuZCgpO1xuICAgIH0gZWxzZSB7XG4gICAgICAgIHN3aXRjaCh0aGlzLnR5cGUpIHtcbiAgICAgICAgY2FzZSBcInN0cmluZ1wiOlxuICAgICAgICAgICAgZGF0YSA9IHRoaXMuZGF0YS5zdWJzdHJpbmcodGhpcy5pbmRleCwgbmV4dEluZGV4KTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIFwidWludDhhcnJheVwiOlxuICAgICAgICAgICAgZGF0YSA9IHRoaXMuZGF0YS5zdWJhcnJheSh0aGlzLmluZGV4LCBuZXh0SW5kZXgpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgXCJhcnJheVwiOlxuICAgICAgICBjYXNlIFwibm9kZWJ1ZmZlclwiOlxuICAgICAgICAgICAgZGF0YSA9IHRoaXMuZGF0YS5zbGljZSh0aGlzLmluZGV4LCBuZXh0SW5kZXgpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5pbmRleCA9IG5leHRJbmRleDtcbiAgICAgICAgcmV0dXJuIHRoaXMucHVzaCh7XG4gICAgICAgICAgICBkYXRhIDogZGF0YSxcbiAgICAgICAgICAgIG1ldGEgOiB7XG4gICAgICAgICAgICAgICAgcGVyY2VudCA6IHRoaXMubWF4ID8gdGhpcy5pbmRleCAvIHRoaXMubWF4ICogMTAwIDogMFxuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICB9XG59O1xuXG5tb2R1bGUuZXhwb3J0cyA9IERhdGFXb3JrZXI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/stream/DataWorker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js":
/*!********************************************************!*\
  !*** ./node_modules/jszip/lib/stream/GenericWorker.js ***!
  \********************************************************/
/***/ ((module) => {

eval("\n\n/**\n * A worker that does nothing but passing chunks to the next one. This is like\n * a nodejs stream but with some differences. On the good side :\n * - it works on IE 6-9 without any issue / polyfill\n * - it weights less than the full dependencies bundled with browserify\n * - it forwards errors (no need to declare an error handler EVERYWHERE)\n *\n * A chunk is an object with 2 attributes : `meta` and `data`. The former is an\n * object containing anything (`percent` for example), see each worker for more\n * details. The latter is the real data (String, Uint8Array, etc).\n *\n * @constructor\n * @param {String} name the name of the stream (mainly used for debugging purposes)\n */\nfunction GenericWorker(name) {\n    // the name of the worker\n    this.name = name || \"default\";\n    // an object containing metadata about the workers chain\n    this.streamInfo = {};\n    // an error which happened when the worker was paused\n    this.generatedError = null;\n    // an object containing metadata to be merged by this worker into the general metadata\n    this.extraStreamInfo = {};\n    // true if the stream is paused (and should not do anything), false otherwise\n    this.isPaused = true;\n    // true if the stream is finished (and should not do anything), false otherwise\n    this.isFinished = false;\n    // true if the stream is locked to prevent further structure updates (pipe), false otherwise\n    this.isLocked = false;\n    // the event listeners\n    this._listeners = {\n        \"data\":[],\n        \"end\":[],\n        \"error\":[]\n    };\n    // the previous worker, if any\n    this.previous = null;\n}\n\nGenericWorker.prototype = {\n    /**\n     * Push a chunk to the next workers.\n     * @param {Object} chunk the chunk to push\n     */\n    push : function (chunk) {\n        this.emit(\"data\", chunk);\n    },\n    /**\n     * End the stream.\n     * @return {Boolean} true if this call ended the worker, false otherwise.\n     */\n    end : function () {\n        if (this.isFinished) {\n            return false;\n        }\n\n        this.flush();\n        try {\n            this.emit(\"end\");\n            this.cleanUp();\n            this.isFinished = true;\n        } catch (e) {\n            this.emit(\"error\", e);\n        }\n        return true;\n    },\n    /**\n     * End the stream with an error.\n     * @param {Error} e the error which caused the premature end.\n     * @return {Boolean} true if this call ended the worker with an error, false otherwise.\n     */\n    error : function (e) {\n        if (this.isFinished) {\n            return false;\n        }\n\n        if(this.isPaused) {\n            this.generatedError = e;\n        } else {\n            this.isFinished = true;\n\n            this.emit(\"error\", e);\n\n            // in the workers chain exploded in the middle of the chain,\n            // the error event will go downward but we also need to notify\n            // workers upward that there has been an error.\n            if(this.previous) {\n                this.previous.error(e);\n            }\n\n            this.cleanUp();\n        }\n        return true;\n    },\n    /**\n     * Add a callback on an event.\n     * @param {String} name the name of the event (data, end, error)\n     * @param {Function} listener the function to call when the event is triggered\n     * @return {GenericWorker} the current object for chainability\n     */\n    on : function (name, listener) {\n        this._listeners[name].push(listener);\n        return this;\n    },\n    /**\n     * Clean any references when a worker is ending.\n     */\n    cleanUp : function () {\n        this.streamInfo = this.generatedError = this.extraStreamInfo = null;\n        this._listeners = [];\n    },\n    /**\n     * Trigger an event. This will call registered callback with the provided arg.\n     * @param {String} name the name of the event (data, end, error)\n     * @param {Object} arg the argument to call the callback with.\n     */\n    emit : function (name, arg) {\n        if (this._listeners[name]) {\n            for(var i = 0; i < this._listeners[name].length; i++) {\n                this._listeners[name][i].call(this, arg);\n            }\n        }\n    },\n    /**\n     * Chain a worker with an other.\n     * @param {Worker} next the worker receiving events from the current one.\n     * @return {worker} the next worker for chainability\n     */\n    pipe : function (next) {\n        return next.registerPrevious(this);\n    },\n    /**\n     * Same as `pipe` in the other direction.\n     * Using an API with `pipe(next)` is very easy.\n     * Implementing the API with the point of view of the next one registering\n     * a source is easier, see the ZipFileWorker.\n     * @param {Worker} previous the previous worker, sending events to this one\n     * @return {Worker} the current worker for chainability\n     */\n    registerPrevious : function (previous) {\n        if (this.isLocked) {\n            throw new Error(\"The stream '\" + this + \"' has already been used.\");\n        }\n\n        // sharing the streamInfo...\n        this.streamInfo = previous.streamInfo;\n        // ... and adding our own bits\n        this.mergeStreamInfo();\n        this.previous =  previous;\n        var self = this;\n        previous.on(\"data\", function (chunk) {\n            self.processChunk(chunk);\n        });\n        previous.on(\"end\", function () {\n            self.end();\n        });\n        previous.on(\"error\", function (e) {\n            self.error(e);\n        });\n        return this;\n    },\n    /**\n     * Pause the stream so it doesn't send events anymore.\n     * @return {Boolean} true if this call paused the worker, false otherwise.\n     */\n    pause : function () {\n        if(this.isPaused || this.isFinished) {\n            return false;\n        }\n        this.isPaused = true;\n\n        if(this.previous) {\n            this.previous.pause();\n        }\n        return true;\n    },\n    /**\n     * Resume a paused stream.\n     * @return {Boolean} true if this call resumed the worker, false otherwise.\n     */\n    resume : function () {\n        if(!this.isPaused || this.isFinished) {\n            return false;\n        }\n        this.isPaused = false;\n\n        // if true, the worker tried to resume but failed\n        var withError = false;\n        if(this.generatedError) {\n            this.error(this.generatedError);\n            withError = true;\n        }\n        if(this.previous) {\n            this.previous.resume();\n        }\n\n        return !withError;\n    },\n    /**\n     * Flush any remaining bytes as the stream is ending.\n     */\n    flush : function () {},\n    /**\n     * Process a chunk. This is usually the method overridden.\n     * @param {Object} chunk the chunk to process.\n     */\n    processChunk : function(chunk) {\n        this.push(chunk);\n    },\n    /**\n     * Add a key/value to be added in the workers chain streamInfo once activated.\n     * @param {String} key the key to use\n     * @param {Object} value the associated value\n     * @return {Worker} the current worker for chainability\n     */\n    withStreamInfo : function (key, value) {\n        this.extraStreamInfo[key] = value;\n        this.mergeStreamInfo();\n        return this;\n    },\n    /**\n     * Merge this worker's streamInfo into the chain's streamInfo.\n     */\n    mergeStreamInfo : function () {\n        for(var key in this.extraStreamInfo) {\n            if (!Object.prototype.hasOwnProperty.call(this.extraStreamInfo, key)) {\n                continue;\n            }\n            this.streamInfo[key] = this.extraStreamInfo[key];\n        }\n    },\n\n    /**\n     * Lock the stream to prevent further updates on the workers chain.\n     * After calling this method, all calls to pipe will fail.\n     */\n    lock: function () {\n        if (this.isLocked) {\n            throw new Error(\"The stream '\" + this + \"' has already been used.\");\n        }\n        this.isLocked = true;\n        if (this.previous) {\n            this.previous.lock();\n        }\n    },\n\n    /**\n     *\n     * Pretty print the workers chain.\n     */\n    toString : function () {\n        var me = \"Worker \" + this.name;\n        if (this.previous) {\n            return this.previous + \" -> \" + me;\n        } else {\n            return me;\n        }\n    }\n};\n\nmodule.exports = GenericWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/stream/StreamHelper.js":
/*!*******************************************************!*\
  !*** ./node_modules/jszip/lib/stream/StreamHelper.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar ConvertWorker = __webpack_require__(/*! ./ConvertWorker */ \"(ssr)/./node_modules/jszip/lib/stream/ConvertWorker.js\");\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\nvar base64 = __webpack_require__(/*! ../base64 */ \"(ssr)/./node_modules/jszip/lib/base64.js\");\nvar support = __webpack_require__(/*! ../support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\nvar external = __webpack_require__(/*! ../external */ \"(ssr)/./node_modules/jszip/lib/external.js\");\n\nvar NodejsStreamOutputAdapter = null;\nif (support.nodestream) {\n    try {\n        NodejsStreamOutputAdapter = __webpack_require__(/*! ../nodejs/NodejsStreamOutputAdapter */ \"(ssr)/./node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js\");\n    } catch(e) {\n        // ignore\n    }\n}\n\n/**\n * Apply the final transformation of the data. If the user wants a Blob for\n * example, it's easier to work with an U8intArray and finally do the\n * ArrayBuffer/Blob conversion.\n * @param {String} type the name of the final type\n * @param {String|Uint8Array|Buffer} content the content to transform\n * @param {String} mimeType the mime type of the content, if applicable.\n * @return {String|Uint8Array|ArrayBuffer|Buffer|Blob} the content in the right format.\n */\nfunction transformZipOutput(type, content, mimeType) {\n    switch(type) {\n    case \"blob\" :\n        return utils.newBlob(utils.transformTo(\"arraybuffer\", content), mimeType);\n    case \"base64\" :\n        return base64.encode(content);\n    default :\n        return utils.transformTo(type, content);\n    }\n}\n\n/**\n * Concatenate an array of data of the given type.\n * @param {String} type the type of the data in the given array.\n * @param {Array} dataArray the array containing the data chunks to concatenate\n * @return {String|Uint8Array|Buffer} the concatenated data\n * @throws Error if the asked type is unsupported\n */\nfunction concat (type, dataArray) {\n    var i, index = 0, res = null, totalLength = 0;\n    for(i = 0; i < dataArray.length; i++) {\n        totalLength += dataArray[i].length;\n    }\n    switch(type) {\n    case \"string\":\n        return dataArray.join(\"\");\n    case \"array\":\n        return Array.prototype.concat.apply([], dataArray);\n    case \"uint8array\":\n        res = new Uint8Array(totalLength);\n        for(i = 0; i < dataArray.length; i++) {\n            res.set(dataArray[i], index);\n            index += dataArray[i].length;\n        }\n        return res;\n    case \"nodebuffer\":\n        return Buffer.concat(dataArray);\n    default:\n        throw new Error(\"concat : unsupported type '\"  + type + \"'\");\n    }\n}\n\n/**\n * Listen a StreamHelper, accumulate its content and concatenate it into a\n * complete block.\n * @param {StreamHelper} helper the helper to use.\n * @param {Function} updateCallback a callback called on each update. Called\n * with one arg :\n * - the metadata linked to the update received.\n * @return Promise the promise for the accumulation.\n */\nfunction accumulate(helper, updateCallback) {\n    return new external.Promise(function (resolve, reject){\n        var dataArray = [];\n        var chunkType = helper._internalType,\n            resultType = helper._outputType,\n            mimeType = helper._mimeType;\n        helper\n            .on(\"data\", function (data, meta) {\n                dataArray.push(data);\n                if(updateCallback) {\n                    updateCallback(meta);\n                }\n            })\n            .on(\"error\", function(err) {\n                dataArray = [];\n                reject(err);\n            })\n            .on(\"end\", function (){\n                try {\n                    var result = transformZipOutput(resultType, concat(chunkType, dataArray), mimeType);\n                    resolve(result);\n                } catch (e) {\n                    reject(e);\n                }\n                dataArray = [];\n            })\n            .resume();\n    });\n}\n\n/**\n * An helper to easily use workers outside of JSZip.\n * @constructor\n * @param {Worker} worker the worker to wrap\n * @param {String} outputType the type of data expected by the use\n * @param {String} mimeType the mime type of the content, if applicable.\n */\nfunction StreamHelper(worker, outputType, mimeType) {\n    var internalType = outputType;\n    switch(outputType) {\n    case \"blob\":\n    case \"arraybuffer\":\n        internalType = \"uint8array\";\n        break;\n    case \"base64\":\n        internalType = \"string\";\n        break;\n    }\n\n    try {\n        // the type used internally\n        this._internalType = internalType;\n        // the type used to output results\n        this._outputType = outputType;\n        // the mime type\n        this._mimeType = mimeType;\n        utils.checkSupport(internalType);\n        this._worker = worker.pipe(new ConvertWorker(internalType));\n        // the last workers can be rewired without issues but we need to\n        // prevent any updates on previous workers.\n        worker.lock();\n    } catch(e) {\n        this._worker = new GenericWorker(\"error\");\n        this._worker.error(e);\n    }\n}\n\nStreamHelper.prototype = {\n    /**\n     * Listen a StreamHelper, accumulate its content and concatenate it into a\n     * complete block.\n     * @param {Function} updateCb the update callback.\n     * @return Promise the promise for the accumulation.\n     */\n    accumulate : function (updateCb) {\n        return accumulate(this, updateCb);\n    },\n    /**\n     * Add a listener on an event triggered on a stream.\n     * @param {String} evt the name of the event\n     * @param {Function} fn the listener\n     * @return {StreamHelper} the current helper.\n     */\n    on : function (evt, fn) {\n        var self = this;\n\n        if(evt === \"data\") {\n            this._worker.on(evt, function (chunk) {\n                fn.call(self, chunk.data, chunk.meta);\n            });\n        } else {\n            this._worker.on(evt, function () {\n                utils.delay(fn, arguments, self);\n            });\n        }\n        return this;\n    },\n    /**\n     * Resume the flow of chunks.\n     * @return {StreamHelper} the current helper.\n     */\n    resume : function () {\n        utils.delay(this._worker.resume, [], this._worker);\n        return this;\n    },\n    /**\n     * Pause the flow of chunks.\n     * @return {StreamHelper} the current helper.\n     */\n    pause : function () {\n        this._worker.pause();\n        return this;\n    },\n    /**\n     * Return a nodejs stream for this helper.\n     * @param {Function} updateCb the update callback.\n     * @return {NodejsStreamOutputAdapter} the nodejs stream.\n     */\n    toNodejsStream : function (updateCb) {\n        utils.checkSupport(\"nodestream\");\n        if (this._outputType !== \"nodebuffer\") {\n            // an object stream containing blob/arraybuffer/uint8array/string\n            // is strange and I don't know if it would be useful.\n            // I you find this comment and have a good usecase, please open a\n            // bug report !\n            throw new Error(this._outputType + \" is not supported by this method\");\n        }\n\n        return new NodejsStreamOutputAdapter(this, {\n            objectMode : this._outputType !== \"nodebuffer\"\n        }, updateCb);\n    }\n};\n\n\nmodule.exports = StreamHelper;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/stream/StreamHelper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/support.js":
/*!*******************************************!*\
  !*** ./node_modules/jszip/lib/support.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nexports.base64 = true;\nexports.array = true;\nexports.string = true;\nexports.arraybuffer = typeof ArrayBuffer !== \"undefined\" && typeof Uint8Array !== \"undefined\";\nexports.nodebuffer = typeof Buffer !== \"undefined\";\n// contains true if JSZip can read/generate Uint8Array, false otherwise.\nexports.uint8array = typeof Uint8Array !== \"undefined\";\n\nif (typeof ArrayBuffer === \"undefined\") {\n    exports.blob = false;\n}\nelse {\n    var buffer = new ArrayBuffer(0);\n    try {\n        exports.blob = new Blob([buffer], {\n            type: \"application/zip\"\n        }).size === 0;\n    }\n    catch (e) {\n        try {\n            var Builder = self.BlobBuilder || self.WebKitBlobBuilder || self.MozBlobBuilder || self.MSBlobBuilder;\n            var builder = new Builder();\n            builder.append(buffer);\n            exports.blob = builder.getBlob(\"application/zip\").size === 0;\n        }\n        catch (e) {\n            exports.blob = false;\n        }\n    }\n}\n\ntry {\n    exports.nodestream = !!(__webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\").Readable);\n} catch(e) {\n    exports.nodestream = false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL3N1cHBvcnQuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsY0FBYztBQUNkLGFBQWE7QUFDYixjQUFjO0FBQ2QsbUJBQW1CO0FBQ25CLGtCQUFrQjtBQUNsQjtBQUNBLGtCQUFrQjs7QUFFbEI7QUFDQSxJQUFJLFlBQVk7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLFlBQVk7QUFDcEI7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxZQUFZO0FBQ3hCO0FBQ0E7QUFDQSxZQUFZLFlBQVk7QUFDeEI7QUFDQTtBQUNBOztBQUVBO0FBQ0EsSUFBSSxrQkFBa0IsS0FBSyx5R0FBbUM7QUFDOUQsRUFBRTtBQUNGLElBQUksa0JBQWtCO0FBQ3RCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHh5aWVuXFxPbmVEcml2ZVxcRGVza3RvcFxcQWxsSm91cm5hbFxcam91cm5hbC1hcHBcXG5vZGVfbW9kdWxlc1xcanN6aXBcXGxpYlxcc3VwcG9ydC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuZXhwb3J0cy5iYXNlNjQgPSB0cnVlO1xuZXhwb3J0cy5hcnJheSA9IHRydWU7XG5leHBvcnRzLnN0cmluZyA9IHRydWU7XG5leHBvcnRzLmFycmF5YnVmZmVyID0gdHlwZW9mIEFycmF5QnVmZmVyICE9PSBcInVuZGVmaW5lZFwiICYmIHR5cGVvZiBVaW50OEFycmF5ICE9PSBcInVuZGVmaW5lZFwiO1xuZXhwb3J0cy5ub2RlYnVmZmVyID0gdHlwZW9mIEJ1ZmZlciAhPT0gXCJ1bmRlZmluZWRcIjtcbi8vIGNvbnRhaW5zIHRydWUgaWYgSlNaaXAgY2FuIHJlYWQvZ2VuZXJhdGUgVWludDhBcnJheSwgZmFsc2Ugb3RoZXJ3aXNlLlxuZXhwb3J0cy51aW50OGFycmF5ID0gdHlwZW9mIFVpbnQ4QXJyYXkgIT09IFwidW5kZWZpbmVkXCI7XG5cbmlmICh0eXBlb2YgQXJyYXlCdWZmZXIgPT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICBleHBvcnRzLmJsb2IgPSBmYWxzZTtcbn1cbmVsc2Uge1xuICAgIHZhciBidWZmZXIgPSBuZXcgQXJyYXlCdWZmZXIoMCk7XG4gICAgdHJ5IHtcbiAgICAgICAgZXhwb3J0cy5ibG9iID0gbmV3IEJsb2IoW2J1ZmZlcl0sIHtcbiAgICAgICAgICAgIHR5cGU6IFwiYXBwbGljYXRpb24vemlwXCJcbiAgICAgICAgfSkuc2l6ZSA9PT0gMDtcbiAgICB9XG4gICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIHZhciBCdWlsZGVyID0gc2VsZi5CbG9iQnVpbGRlciB8fCBzZWxmLldlYktpdEJsb2JCdWlsZGVyIHx8IHNlbGYuTW96QmxvYkJ1aWxkZXIgfHwgc2VsZi5NU0Jsb2JCdWlsZGVyO1xuICAgICAgICAgICAgdmFyIGJ1aWxkZXIgPSBuZXcgQnVpbGRlcigpO1xuICAgICAgICAgICAgYnVpbGRlci5hcHBlbmQoYnVmZmVyKTtcbiAgICAgICAgICAgIGV4cG9ydHMuYmxvYiA9IGJ1aWxkZXIuZ2V0QmxvYihcImFwcGxpY2F0aW9uL3ppcFwiKS5zaXplID09PSAwO1xuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlKSB7XG4gICAgICAgICAgICBleHBvcnRzLmJsb2IgPSBmYWxzZTtcbiAgICAgICAgfVxuICAgIH1cbn1cblxudHJ5IHtcbiAgICBleHBvcnRzLm5vZGVzdHJlYW0gPSAhIXJlcXVpcmUoXCJyZWFkYWJsZS1zdHJlYW1cIikuUmVhZGFibGU7XG59IGNhdGNoKGUpIHtcbiAgICBleHBvcnRzLm5vZGVzdHJlYW0gPSBmYWxzZTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/support.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/utf8.js":
/*!****************************************!*\
  !*** ./node_modules/jszip/lib/utf8.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\nvar nodejsUtils = __webpack_require__(/*! ./nodejsUtils */ \"(ssr)/./node_modules/jszip/lib/nodejsUtils.js\");\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\n\n/**\n * The following functions come from pako, from pako/lib/utils/strings\n * released under the MIT license, see pako https://github.com/nodeca/pako/\n */\n\n// Table with utf8 lengths (calculated by first byte of sequence)\n// Note, that 5 & 6-byte values and some 4-byte values can not be represented in JS,\n// because max possible codepoint is 0x10ffff\nvar _utf8len = new Array(256);\nfor (var i=0; i<256; i++) {\n    _utf8len[i] = (i >= 252 ? 6 : i >= 248 ? 5 : i >= 240 ? 4 : i >= 224 ? 3 : i >= 192 ? 2 : 1);\n}\n_utf8len[254]=_utf8len[254]=1; // Invalid sequence start\n\n// convert string to array (typed, when possible)\nvar string2buf = function (str) {\n    var buf, c, c2, m_pos, i, str_len = str.length, buf_len = 0;\n\n    // count binary size\n    for (m_pos = 0; m_pos < str_len; m_pos++) {\n        c = str.charCodeAt(m_pos);\n        if ((c & 0xfc00) === 0xd800 && (m_pos+1 < str_len)) {\n            c2 = str.charCodeAt(m_pos+1);\n            if ((c2 & 0xfc00) === 0xdc00) {\n                c = 0x10000 + ((c - 0xd800) << 10) + (c2 - 0xdc00);\n                m_pos++;\n            }\n        }\n        buf_len += c < 0x80 ? 1 : c < 0x800 ? 2 : c < 0x10000 ? 3 : 4;\n    }\n\n    // allocate buffer\n    if (support.uint8array) {\n        buf = new Uint8Array(buf_len);\n    } else {\n        buf = new Array(buf_len);\n    }\n\n    // convert\n    for (i=0, m_pos = 0; i < buf_len; m_pos++) {\n        c = str.charCodeAt(m_pos);\n        if ((c & 0xfc00) === 0xd800 && (m_pos+1 < str_len)) {\n            c2 = str.charCodeAt(m_pos+1);\n            if ((c2 & 0xfc00) === 0xdc00) {\n                c = 0x10000 + ((c - 0xd800) << 10) + (c2 - 0xdc00);\n                m_pos++;\n            }\n        }\n        if (c < 0x80) {\n            /* one byte */\n            buf[i++] = c;\n        } else if (c < 0x800) {\n            /* two bytes */\n            buf[i++] = 0xC0 | (c >>> 6);\n            buf[i++] = 0x80 | (c & 0x3f);\n        } else if (c < 0x10000) {\n            /* three bytes */\n            buf[i++] = 0xE0 | (c >>> 12);\n            buf[i++] = 0x80 | (c >>> 6 & 0x3f);\n            buf[i++] = 0x80 | (c & 0x3f);\n        } else {\n            /* four bytes */\n            buf[i++] = 0xf0 | (c >>> 18);\n            buf[i++] = 0x80 | (c >>> 12 & 0x3f);\n            buf[i++] = 0x80 | (c >>> 6 & 0x3f);\n            buf[i++] = 0x80 | (c & 0x3f);\n        }\n    }\n\n    return buf;\n};\n\n// Calculate max possible position in utf8 buffer,\n// that will not break sequence. If that's not possible\n// - (very small limits) return max size as is.\n//\n// buf[] - utf8 bytes array\n// max   - length limit (mandatory);\nvar utf8border = function(buf, max) {\n    var pos;\n\n    max = max || buf.length;\n    if (max > buf.length) { max = buf.length; }\n\n    // go back from last position, until start of sequence found\n    pos = max-1;\n    while (pos >= 0 && (buf[pos] & 0xC0) === 0x80) { pos--; }\n\n    // Fuckup - very small and broken sequence,\n    // return max, because we should return something anyway.\n    if (pos < 0) { return max; }\n\n    // If we came to start of buffer - that means vuffer is too small,\n    // return max too.\n    if (pos === 0) { return max; }\n\n    return (pos + _utf8len[buf[pos]] > max) ? pos : max;\n};\n\n// convert array to string\nvar buf2string = function (buf) {\n    var i, out, c, c_len;\n    var len = buf.length;\n\n    // Reserve max possible length (2 words per char)\n    // NB: by unknown reasons, Array is significantly faster for\n    //     String.fromCharCode.apply than Uint16Array.\n    var utf16buf = new Array(len*2);\n\n    for (out=0, i=0; i<len;) {\n        c = buf[i++];\n        // quick process ascii\n        if (c < 0x80) { utf16buf[out++] = c; continue; }\n\n        c_len = _utf8len[c];\n        // skip 5 & 6 byte codes\n        if (c_len > 4) { utf16buf[out++] = 0xfffd; i += c_len-1; continue; }\n\n        // apply mask on first byte\n        c &= c_len === 2 ? 0x1f : c_len === 3 ? 0x0f : 0x07;\n        // join the rest\n        while (c_len > 1 && i < len) {\n            c = (c << 6) | (buf[i++] & 0x3f);\n            c_len--;\n        }\n\n        // terminated by end of string?\n        if (c_len > 1) { utf16buf[out++] = 0xfffd; continue; }\n\n        if (c < 0x10000) {\n            utf16buf[out++] = c;\n        } else {\n            c -= 0x10000;\n            utf16buf[out++] = 0xd800 | ((c >> 10) & 0x3ff);\n            utf16buf[out++] = 0xdc00 | (c & 0x3ff);\n        }\n    }\n\n    // shrinkBuf(utf16buf, out)\n    if (utf16buf.length !== out) {\n        if(utf16buf.subarray) {\n            utf16buf = utf16buf.subarray(0, out);\n        } else {\n            utf16buf.length = out;\n        }\n    }\n\n    // return String.fromCharCode.apply(null, utf16buf);\n    return utils.applyFromCharCode(utf16buf);\n};\n\n\n// That's all for the pako functions.\n\n\n/**\n * Transform a javascript string into an array (typed if possible) of bytes,\n * UTF-8 encoded.\n * @param {String} str the string to encode\n * @return {Array|Uint8Array|Buffer} the UTF-8 encoded string.\n */\nexports.utf8encode = function utf8encode(str) {\n    if (support.nodebuffer) {\n        return nodejsUtils.newBufferFrom(str, \"utf-8\");\n    }\n\n    return string2buf(str);\n};\n\n\n/**\n * Transform a bytes array (or a representation) representing an UTF-8 encoded\n * string into a javascript string.\n * @param {Array|Uint8Array|Buffer} buf the data de decode\n * @return {String} the decoded string.\n */\nexports.utf8decode = function utf8decode(buf) {\n    if (support.nodebuffer) {\n        return utils.transformTo(\"nodebuffer\", buf).toString(\"utf-8\");\n    }\n\n    buf = utils.transformTo(support.uint8array ? \"uint8array\" : \"array\", buf);\n\n    return buf2string(buf);\n};\n\n/**\n * A worker to decode utf8 encoded binary chunks into string chunks.\n * @constructor\n */\nfunction Utf8DecodeWorker() {\n    GenericWorker.call(this, \"utf-8 decode\");\n    // the last bytes if a chunk didn't end with a complete codepoint.\n    this.leftOver = null;\n}\nutils.inherits(Utf8DecodeWorker, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nUtf8DecodeWorker.prototype.processChunk = function (chunk) {\n\n    var data = utils.transformTo(support.uint8array ? \"uint8array\" : \"array\", chunk.data);\n\n    // 1st step, re-use what's left of the previous chunk\n    if (this.leftOver && this.leftOver.length) {\n        if(support.uint8array) {\n            var previousData = data;\n            data = new Uint8Array(previousData.length + this.leftOver.length);\n            data.set(this.leftOver, 0);\n            data.set(previousData, this.leftOver.length);\n        } else {\n            data = this.leftOver.concat(data);\n        }\n        this.leftOver = null;\n    }\n\n    var nextBoundary = utf8border(data);\n    var usableData = data;\n    if (nextBoundary !== data.length) {\n        if (support.uint8array) {\n            usableData = data.subarray(0, nextBoundary);\n            this.leftOver = data.subarray(nextBoundary, data.length);\n        } else {\n            usableData = data.slice(0, nextBoundary);\n            this.leftOver = data.slice(nextBoundary, data.length);\n        }\n    }\n\n    this.push({\n        data : exports.utf8decode(usableData),\n        meta : chunk.meta\n    });\n};\n\n/**\n * @see GenericWorker.flush\n */\nUtf8DecodeWorker.prototype.flush = function () {\n    if(this.leftOver && this.leftOver.length) {\n        this.push({\n            data : exports.utf8decode(this.leftOver),\n            meta : {}\n        });\n        this.leftOver = null;\n    }\n};\nexports.Utf8DecodeWorker = Utf8DecodeWorker;\n\n/**\n * A worker to endcode string chunks into utf8 encoded binary chunks.\n * @constructor\n */\nfunction Utf8EncodeWorker() {\n    GenericWorker.call(this, \"utf-8 encode\");\n}\nutils.inherits(Utf8EncodeWorker, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nUtf8EncodeWorker.prototype.processChunk = function (chunk) {\n    this.push({\n        data : exports.utf8encode(chunk.data),\n        meta : chunk.meta\n    });\n};\nexports.Utf8EncodeWorker = Utf8EncodeWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/utf8.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/utils.js":
/*!*****************************************!*\
  !*** ./node_modules/jszip/lib/utils.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\nvar base64 = __webpack_require__(/*! ./base64 */ \"(ssr)/./node_modules/jszip/lib/base64.js\");\nvar nodejsUtils = __webpack_require__(/*! ./nodejsUtils */ \"(ssr)/./node_modules/jszip/lib/nodejsUtils.js\");\nvar external = __webpack_require__(/*! ./external */ \"(ssr)/./node_modules/jszip/lib/external.js\");\n__webpack_require__(/*! setimmediate */ \"(ssr)/./node_modules/next/dist/compiled/setimmediate/setImmediate.js\");\n\n\n/**\n * Convert a string that pass as a \"binary string\": it should represent a byte\n * array but may have > 255 char codes. Be sure to take only the first byte\n * and returns the byte array.\n * @param {String} str the string to transform.\n * @return {Array|Uint8Array} the string in a binary format.\n */\nfunction string2binary(str) {\n    var result = null;\n    if (support.uint8array) {\n        result = new Uint8Array(str.length);\n    } else {\n        result = new Array(str.length);\n    }\n    return stringToArrayLike(str, result);\n}\n\n/**\n * Create a new blob with the given content and the given type.\n * @param {String|ArrayBuffer} part the content to put in the blob. DO NOT use\n * an Uint8Array because the stock browser of android 4 won't accept it (it\n * will be silently converted to a string, \"[object Uint8Array]\").\n *\n * Use only ONE part to build the blob to avoid a memory leak in IE11 / Edge:\n * when a large amount of Array is used to create the Blob, the amount of\n * memory consumed is nearly 100 times the original data amount.\n *\n * @param {String} type the mime type of the blob.\n * @return {Blob} the created blob.\n */\nexports.newBlob = function(part, type) {\n    exports.checkSupport(\"blob\");\n\n    try {\n        // Blob constructor\n        return new Blob([part], {\n            type: type\n        });\n    }\n    catch (e) {\n\n        try {\n            // deprecated, browser only, old way\n            var Builder = self.BlobBuilder || self.WebKitBlobBuilder || self.MozBlobBuilder || self.MSBlobBuilder;\n            var builder = new Builder();\n            builder.append(part);\n            return builder.getBlob(type);\n        }\n        catch (e) {\n\n            // well, fuck ?!\n            throw new Error(\"Bug : can't construct the Blob.\");\n        }\n    }\n\n\n};\n/**\n * The identity function.\n * @param {Object} input the input.\n * @return {Object} the same input.\n */\nfunction identity(input) {\n    return input;\n}\n\n/**\n * Fill in an array with a string.\n * @param {String} str the string to use.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to fill in (will be mutated).\n * @return {Array|ArrayBuffer|Uint8Array|Buffer} the updated array.\n */\nfunction stringToArrayLike(str, array) {\n    for (var i = 0; i < str.length; ++i) {\n        array[i] = str.charCodeAt(i) & 0xFF;\n    }\n    return array;\n}\n\n/**\n * An helper for the function arrayLikeToString.\n * This contains static information and functions that\n * can be optimized by the browser JIT compiler.\n */\nvar arrayToStringHelper = {\n    /**\n     * Transform an array of int into a string, chunk by chunk.\n     * See the performances notes on arrayLikeToString.\n     * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to transform.\n     * @param {String} type the type of the array.\n     * @param {Integer} chunk the chunk size.\n     * @return {String} the resulting string.\n     * @throws Error if the chunk is too big for the stack.\n     */\n    stringifyByChunk: function(array, type, chunk) {\n        var result = [], k = 0, len = array.length;\n        // shortcut\n        if (len <= chunk) {\n            return String.fromCharCode.apply(null, array);\n        }\n        while (k < len) {\n            if (type === \"array\" || type === \"nodebuffer\") {\n                result.push(String.fromCharCode.apply(null, array.slice(k, Math.min(k + chunk, len))));\n            }\n            else {\n                result.push(String.fromCharCode.apply(null, array.subarray(k, Math.min(k + chunk, len))));\n            }\n            k += chunk;\n        }\n        return result.join(\"\");\n    },\n    /**\n     * Call String.fromCharCode on every item in the array.\n     * This is the naive implementation, which generate A LOT of intermediate string.\n     * This should be used when everything else fail.\n     * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to transform.\n     * @return {String} the result.\n     */\n    stringifyByChar: function(array){\n        var resultStr = \"\";\n        for(var i = 0; i < array.length; i++) {\n            resultStr += String.fromCharCode(array[i]);\n        }\n        return resultStr;\n    },\n    applyCanBeUsed : {\n        /**\n         * true if the browser accepts to use String.fromCharCode on Uint8Array\n         */\n        uint8array : (function () {\n            try {\n                return support.uint8array && String.fromCharCode.apply(null, new Uint8Array(1)).length === 1;\n            } catch (e) {\n                return false;\n            }\n        })(),\n        /**\n         * true if the browser accepts to use String.fromCharCode on nodejs Buffer.\n         */\n        nodebuffer : (function () {\n            try {\n                return support.nodebuffer && String.fromCharCode.apply(null, nodejsUtils.allocBuffer(1)).length === 1;\n            } catch (e) {\n                return false;\n            }\n        })()\n    }\n};\n\n/**\n * Transform an array-like object to a string.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to transform.\n * @return {String} the result.\n */\nfunction arrayLikeToString(array) {\n    // Performances notes :\n    // --------------------\n    // String.fromCharCode.apply(null, array) is the fastest, see\n    // see http://jsperf.com/converting-a-uint8array-to-a-string/2\n    // but the stack is limited (and we can get huge arrays !).\n    //\n    // result += String.fromCharCode(array[i]); generate too many strings !\n    //\n    // This code is inspired by http://jsperf.com/arraybuffer-to-string-apply-performance/2\n    // TODO : we now have workers that split the work. Do we still need that ?\n    var chunk = 65536,\n        type = exports.getTypeOf(array),\n        canUseApply = true;\n    if (type === \"uint8array\") {\n        canUseApply = arrayToStringHelper.applyCanBeUsed.uint8array;\n    } else if (type === \"nodebuffer\") {\n        canUseApply = arrayToStringHelper.applyCanBeUsed.nodebuffer;\n    }\n\n    if (canUseApply) {\n        while (chunk > 1) {\n            try {\n                return arrayToStringHelper.stringifyByChunk(array, type, chunk);\n            } catch (e) {\n                chunk = Math.floor(chunk / 2);\n            }\n        }\n    }\n\n    // no apply or chunk error : slow and painful algorithm\n    // default browser on android 4.*\n    return arrayToStringHelper.stringifyByChar(array);\n}\n\nexports.applyFromCharCode = arrayLikeToString;\n\n\n/**\n * Copy the data from an array-like to an other array-like.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} arrayFrom the origin array.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} arrayTo the destination array which will be mutated.\n * @return {Array|ArrayBuffer|Uint8Array|Buffer} the updated destination array.\n */\nfunction arrayLikeToArrayLike(arrayFrom, arrayTo) {\n    for (var i = 0; i < arrayFrom.length; i++) {\n        arrayTo[i] = arrayFrom[i];\n    }\n    return arrayTo;\n}\n\n// a matrix containing functions to transform everything into everything.\nvar transform = {};\n\n// string to ?\ntransform[\"string\"] = {\n    \"string\": identity,\n    \"array\": function(input) {\n        return stringToArrayLike(input, new Array(input.length));\n    },\n    \"arraybuffer\": function(input) {\n        return transform[\"string\"][\"uint8array\"](input).buffer;\n    },\n    \"uint8array\": function(input) {\n        return stringToArrayLike(input, new Uint8Array(input.length));\n    },\n    \"nodebuffer\": function(input) {\n        return stringToArrayLike(input, nodejsUtils.allocBuffer(input.length));\n    }\n};\n\n// array to ?\ntransform[\"array\"] = {\n    \"string\": arrayLikeToString,\n    \"array\": identity,\n    \"arraybuffer\": function(input) {\n        return (new Uint8Array(input)).buffer;\n    },\n    \"uint8array\": function(input) {\n        return new Uint8Array(input);\n    },\n    \"nodebuffer\": function(input) {\n        return nodejsUtils.newBufferFrom(input);\n    }\n};\n\n// arraybuffer to ?\ntransform[\"arraybuffer\"] = {\n    \"string\": function(input) {\n        return arrayLikeToString(new Uint8Array(input));\n    },\n    \"array\": function(input) {\n        return arrayLikeToArrayLike(new Uint8Array(input), new Array(input.byteLength));\n    },\n    \"arraybuffer\": identity,\n    \"uint8array\": function(input) {\n        return new Uint8Array(input);\n    },\n    \"nodebuffer\": function(input) {\n        return nodejsUtils.newBufferFrom(new Uint8Array(input));\n    }\n};\n\n// uint8array to ?\ntransform[\"uint8array\"] = {\n    \"string\": arrayLikeToString,\n    \"array\": function(input) {\n        return arrayLikeToArrayLike(input, new Array(input.length));\n    },\n    \"arraybuffer\": function(input) {\n        return input.buffer;\n    },\n    \"uint8array\": identity,\n    \"nodebuffer\": function(input) {\n        return nodejsUtils.newBufferFrom(input);\n    }\n};\n\n// nodebuffer to ?\ntransform[\"nodebuffer\"] = {\n    \"string\": arrayLikeToString,\n    \"array\": function(input) {\n        return arrayLikeToArrayLike(input, new Array(input.length));\n    },\n    \"arraybuffer\": function(input) {\n        return transform[\"nodebuffer\"][\"uint8array\"](input).buffer;\n    },\n    \"uint8array\": function(input) {\n        return arrayLikeToArrayLike(input, new Uint8Array(input.length));\n    },\n    \"nodebuffer\": identity\n};\n\n/**\n * Transform an input into any type.\n * The supported output type are : string, array, uint8array, arraybuffer, nodebuffer.\n * If no output type is specified, the unmodified input will be returned.\n * @param {String} outputType the output type.\n * @param {String|Array|ArrayBuffer|Uint8Array|Buffer} input the input to convert.\n * @throws {Error} an Error if the browser doesn't support the requested output type.\n */\nexports.transformTo = function(outputType, input) {\n    if (!input) {\n        // undefined, null, etc\n        // an empty string won't harm.\n        input = \"\";\n    }\n    if (!outputType) {\n        return input;\n    }\n    exports.checkSupport(outputType);\n    var inputType = exports.getTypeOf(input);\n    var result = transform[inputType][outputType](input);\n    return result;\n};\n\n/**\n * Resolve all relative path components, \".\" and \"..\", in a path. If these relative components\n * traverse above the root then the resulting path will only contain the final path component.\n *\n * All empty components, e.g. \"//\", are removed.\n * @param {string} path A path with / or \\ separators\n * @returns {string} The path with all relative path components resolved.\n */\nexports.resolve = function(path) {\n    var parts = path.split(\"/\");\n    var result = [];\n    for (var index = 0; index < parts.length; index++) {\n        var part = parts[index];\n        // Allow the first and last component to be empty for trailing slashes.\n        if (part === \".\" || (part === \"\" && index !== 0 && index !== parts.length - 1)) {\n            continue;\n        } else if (part === \"..\") {\n            result.pop();\n        } else {\n            result.push(part);\n        }\n    }\n    return result.join(\"/\");\n};\n\n/**\n * Return the type of the input.\n * The type will be in a format valid for JSZip.utils.transformTo : string, array, uint8array, arraybuffer.\n * @param {Object} input the input to identify.\n * @return {String} the (lowercase) type of the input.\n */\nexports.getTypeOf = function(input) {\n    if (typeof input === \"string\") {\n        return \"string\";\n    }\n    if (Object.prototype.toString.call(input) === \"[object Array]\") {\n        return \"array\";\n    }\n    if (support.nodebuffer && nodejsUtils.isBuffer(input)) {\n        return \"nodebuffer\";\n    }\n    if (support.uint8array && input instanceof Uint8Array) {\n        return \"uint8array\";\n    }\n    if (support.arraybuffer && input instanceof ArrayBuffer) {\n        return \"arraybuffer\";\n    }\n};\n\n/**\n * Throw an exception if the type is not supported.\n * @param {String} type the type to check.\n * @throws {Error} an Error if the browser doesn't support the requested type.\n */\nexports.checkSupport = function(type) {\n    var supported = support[type.toLowerCase()];\n    if (!supported) {\n        throw new Error(type + \" is not supported by this platform\");\n    }\n};\n\nexports.MAX_VALUE_16BITS = 65535;\nexports.MAX_VALUE_32BITS = -1; // well, \"\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\" is parsed as -1\n\n/**\n * Prettify a string read as binary.\n * @param {string} str the string to prettify.\n * @return {string} a pretty string.\n */\nexports.pretty = function(str) {\n    var res = \"\",\n        code, i;\n    for (i = 0; i < (str || \"\").length; i++) {\n        code = str.charCodeAt(i);\n        res += \"\\\\x\" + (code < 16 ? \"0\" : \"\") + code.toString(16).toUpperCase();\n    }\n    return res;\n};\n\n/**\n * Defer the call of a function.\n * @param {Function} callback the function to call asynchronously.\n * @param {Array} args the arguments to give to the callback.\n */\nexports.delay = function(callback, args, self) {\n    setImmediate(function () {\n        callback.apply(self || null, args || []);\n    });\n};\n\n/**\n * Extends a prototype with an other, without calling a constructor with\n * side effects. Inspired by nodejs' `utils.inherits`\n * @param {Function} ctor the constructor to augment\n * @param {Function} superCtor the parent constructor to use\n */\nexports.inherits = function (ctor, superCtor) {\n    var Obj = function() {};\n    Obj.prototype = superCtor.prototype;\n    ctor.prototype = new Obj();\n};\n\n/**\n * Merge the objects passed as parameters into a new one.\n * @private\n * @param {...Object} var_args All objects to merge.\n * @return {Object} a new object with the data of the others.\n */\nexports.extend = function() {\n    var result = {}, i, attr;\n    for (i = 0; i < arguments.length; i++) { // arguments is not enumerable in some browsers\n        for (attr in arguments[i]) {\n            if (Object.prototype.hasOwnProperty.call(arguments[i], attr) && typeof result[attr] === \"undefined\") {\n                result[attr] = arguments[i][attr];\n            }\n        }\n    }\n    return result;\n};\n\n/**\n * Transform arbitrary content into a Promise.\n * @param {String} name a name for the content being processed.\n * @param {Object} inputData the content to process.\n * @param {Boolean} isBinary true if the content is not an unicode string\n * @param {Boolean} isOptimizedBinaryString true if the string content only has one byte per character.\n * @param {Boolean} isBase64 true if the string content is encoded with base64.\n * @return {Promise} a promise in a format usable by JSZip.\n */\nexports.prepareContent = function(name, inputData, isBinary, isOptimizedBinaryString, isBase64) {\n\n    // if inputData is already a promise, this flatten it.\n    var promise = external.Promise.resolve(inputData).then(function(data) {\n\n\n        var isBlob = support.blob && (data instanceof Blob || [\"[object File]\", \"[object Blob]\"].indexOf(Object.prototype.toString.call(data)) !== -1);\n\n        if (isBlob && typeof FileReader !== \"undefined\") {\n            return new external.Promise(function (resolve, reject) {\n                var reader = new FileReader();\n\n                reader.onload = function(e) {\n                    resolve(e.target.result);\n                };\n                reader.onerror = function(e) {\n                    reject(e.target.error);\n                };\n                reader.readAsArrayBuffer(data);\n            });\n        } else {\n            return data;\n        }\n    });\n\n    return promise.then(function(data) {\n        var dataType = exports.getTypeOf(data);\n\n        if (!dataType) {\n            return external.Promise.reject(\n                new Error(\"Can't read the data of '\" + name + \"'. Is it \" +\n                          \"in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?\")\n            );\n        }\n        // special case : it's way easier to work with Uint8Array than with ArrayBuffer\n        if (dataType === \"arraybuffer\") {\n            data = exports.transformTo(\"uint8array\", data);\n        } else if (dataType === \"string\") {\n            if (isBase64) {\n                data = base64.decode(data);\n            }\n            else if (isBinary) {\n                // optimizedBinaryString === true means that the file has already been filtered with a 0xFF mask\n                if (isOptimizedBinaryString !== true) {\n                    // this is a string, not in a base64 format.\n                    // Be sure that this is a correct \"binary string\"\n                    data = string2binary(data);\n                }\n            }\n        }\n        return data;\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL3V0aWxzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLGNBQWMsbUJBQU8sQ0FBQyw0REFBVztBQUNqQyxhQUFhLG1CQUFPLENBQUMsMERBQVU7QUFDL0Isa0JBQWtCLG1CQUFPLENBQUMsb0VBQWU7QUFDekMsZUFBZSxtQkFBTyxDQUFDLDhEQUFZO0FBQ25DLG1CQUFPLENBQUMsMEZBQWM7OztBQUd0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixZQUFZLGtCQUFrQjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixZQUFZLE1BQU07QUFDbEI7QUFDQSxlQUFlO0FBQ2Y7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOzs7QUFHQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsWUFBWSxRQUFRO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxxQ0FBcUM7QUFDaEQsWUFBWSxxQ0FBcUM7QUFDakQ7QUFDQTtBQUNBLG9CQUFvQixnQkFBZ0I7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxxQ0FBcUM7QUFDcEQsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsU0FBUztBQUN4QixnQkFBZ0IsUUFBUTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLHFDQUFxQztBQUNwRCxnQkFBZ0IsUUFBUTtBQUN4QjtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsa0JBQWtCO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFdBQVcscUNBQXFDO0FBQ2hELFlBQVksUUFBUTtBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0RBQWdEO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUI7OztBQUd6QjtBQUNBO0FBQ0EsV0FBVyxxQ0FBcUM7QUFDaEQsV0FBVyxxQ0FBcUM7QUFDaEQsWUFBWSxxQ0FBcUM7QUFDakQ7QUFDQTtBQUNBLG9CQUFvQixzQkFBc0I7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsNENBQTRDO0FBQ3ZELFlBQVksT0FBTztBQUNuQjtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWEsUUFBUTtBQUNyQjtBQUNBLGVBQWU7QUFDZjtBQUNBO0FBQ0Esd0JBQXdCLHNCQUFzQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixZQUFZLFFBQVE7QUFDcEI7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixZQUFZLE9BQU87QUFDbkI7QUFDQSxvQkFBb0I7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx3QkFBd0I7QUFDeEIsd0JBQXdCLE9BQU87O0FBRS9CO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsWUFBWSxRQUFRO0FBQ3BCO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQSxnQkFBZ0Isd0JBQXdCO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFdBQVcsVUFBVTtBQUNyQixXQUFXLE9BQU87QUFDbEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFVBQVU7QUFDckIsV0FBVyxVQUFVO0FBQ3JCO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsV0FBVztBQUN0QixZQUFZLFFBQVE7QUFDcEI7QUFDQSxjQUFjO0FBQ2QsbUJBQW1CO0FBQ25CLGdCQUFnQixzQkFBc0IsT0FBTztBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxRQUFRO0FBQ25CLFdBQVcsU0FBUztBQUNwQixXQUFXLFNBQVM7QUFDcEIsV0FBVyxTQUFTO0FBQ3BCLFlBQVksU0FBUztBQUNyQjtBQUNBLHNCQUFzQjs7QUFFdEI7QUFDQTs7O0FBR0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFVBQVU7QUFDVjtBQUNBO0FBQ0EsS0FBSzs7QUFFTDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx4eWllblxcT25lRHJpdmVcXERlc2t0b3BcXEFsbEpvdXJuYWxcXGpvdXJuYWwtYXBwXFxub2RlX21vZHVsZXNcXGpzemlwXFxsaWJcXHV0aWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG52YXIgc3VwcG9ydCA9IHJlcXVpcmUoXCIuL3N1cHBvcnRcIik7XG52YXIgYmFzZTY0ID0gcmVxdWlyZShcIi4vYmFzZTY0XCIpO1xudmFyIG5vZGVqc1V0aWxzID0gcmVxdWlyZShcIi4vbm9kZWpzVXRpbHNcIik7XG52YXIgZXh0ZXJuYWwgPSByZXF1aXJlKFwiLi9leHRlcm5hbFwiKTtcbnJlcXVpcmUoXCJzZXRpbW1lZGlhdGVcIik7XG5cblxuLyoqXG4gKiBDb252ZXJ0IGEgc3RyaW5nIHRoYXQgcGFzcyBhcyBhIFwiYmluYXJ5IHN0cmluZ1wiOiBpdCBzaG91bGQgcmVwcmVzZW50IGEgYnl0ZVxuICogYXJyYXkgYnV0IG1heSBoYXZlID4gMjU1IGNoYXIgY29kZXMuIEJlIHN1cmUgdG8gdGFrZSBvbmx5IHRoZSBmaXJzdCBieXRlXG4gKiBhbmQgcmV0dXJucyB0aGUgYnl0ZSBhcnJheS5cbiAqIEBwYXJhbSB7U3RyaW5nfSBzdHIgdGhlIHN0cmluZyB0byB0cmFuc2Zvcm0uXG4gKiBAcmV0dXJuIHtBcnJheXxVaW50OEFycmF5fSB0aGUgc3RyaW5nIGluIGEgYmluYXJ5IGZvcm1hdC5cbiAqL1xuZnVuY3Rpb24gc3RyaW5nMmJpbmFyeShzdHIpIHtcbiAgICB2YXIgcmVzdWx0ID0gbnVsbDtcbiAgICBpZiAoc3VwcG9ydC51aW50OGFycmF5KSB7XG4gICAgICAgIHJlc3VsdCA9IG5ldyBVaW50OEFycmF5KHN0ci5sZW5ndGgpO1xuICAgIH0gZWxzZSB7XG4gICAgICAgIHJlc3VsdCA9IG5ldyBBcnJheShzdHIubGVuZ3RoKTtcbiAgICB9XG4gICAgcmV0dXJuIHN0cmluZ1RvQXJyYXlMaWtlKHN0ciwgcmVzdWx0KTtcbn1cblxuLyoqXG4gKiBDcmVhdGUgYSBuZXcgYmxvYiB3aXRoIHRoZSBnaXZlbiBjb250ZW50IGFuZCB0aGUgZ2l2ZW4gdHlwZS5cbiAqIEBwYXJhbSB7U3RyaW5nfEFycmF5QnVmZmVyfSBwYXJ0IHRoZSBjb250ZW50IHRvIHB1dCBpbiB0aGUgYmxvYi4gRE8gTk9UIHVzZVxuICogYW4gVWludDhBcnJheSBiZWNhdXNlIHRoZSBzdG9jayBicm93c2VyIG9mIGFuZHJvaWQgNCB3b24ndCBhY2NlcHQgaXQgKGl0XG4gKiB3aWxsIGJlIHNpbGVudGx5IGNvbnZlcnRlZCB0byBhIHN0cmluZywgXCJbb2JqZWN0IFVpbnQ4QXJyYXldXCIpLlxuICpcbiAqIFVzZSBvbmx5IE9ORSBwYXJ0IHRvIGJ1aWxkIHRoZSBibG9iIHRvIGF2b2lkIGEgbWVtb3J5IGxlYWsgaW4gSUUxMSAvIEVkZ2U6XG4gKiB3aGVuIGEgbGFyZ2UgYW1vdW50IG9mIEFycmF5IGlzIHVzZWQgdG8gY3JlYXRlIHRoZSBCbG9iLCB0aGUgYW1vdW50IG9mXG4gKiBtZW1vcnkgY29uc3VtZWQgaXMgbmVhcmx5IDEwMCB0aW1lcyB0aGUgb3JpZ2luYWwgZGF0YSBhbW91bnQuXG4gKlxuICogQHBhcmFtIHtTdHJpbmd9IHR5cGUgdGhlIG1pbWUgdHlwZSBvZiB0aGUgYmxvYi5cbiAqIEByZXR1cm4ge0Jsb2J9IHRoZSBjcmVhdGVkIGJsb2IuXG4gKi9cbmV4cG9ydHMubmV3QmxvYiA9IGZ1bmN0aW9uKHBhcnQsIHR5cGUpIHtcbiAgICBleHBvcnRzLmNoZWNrU3VwcG9ydChcImJsb2JcIik7XG5cbiAgICB0cnkge1xuICAgICAgICAvLyBCbG9iIGNvbnN0cnVjdG9yXG4gICAgICAgIHJldHVybiBuZXcgQmxvYihbcGFydF0sIHtcbiAgICAgICAgICAgIHR5cGU6IHR5cGVcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIGNhdGNoIChlKSB7XG5cbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIC8vIGRlcHJlY2F0ZWQsIGJyb3dzZXIgb25seSwgb2xkIHdheVxuICAgICAgICAgICAgdmFyIEJ1aWxkZXIgPSBzZWxmLkJsb2JCdWlsZGVyIHx8IHNlbGYuV2ViS2l0QmxvYkJ1aWxkZXIgfHwgc2VsZi5Nb3pCbG9iQnVpbGRlciB8fCBzZWxmLk1TQmxvYkJ1aWxkZXI7XG4gICAgICAgICAgICB2YXIgYnVpbGRlciA9IG5ldyBCdWlsZGVyKCk7XG4gICAgICAgICAgICBidWlsZGVyLmFwcGVuZChwYXJ0KTtcbiAgICAgICAgICAgIHJldHVybiBidWlsZGVyLmdldEJsb2IodHlwZSk7XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKGUpIHtcblxuICAgICAgICAgICAgLy8gd2VsbCwgZnVjayA/IVxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiQnVnIDogY2FuJ3QgY29uc3RydWN0IHRoZSBCbG9iLlwiKTtcbiAgICAgICAgfVxuICAgIH1cblxuXG59O1xuLyoqXG4gKiBUaGUgaWRlbnRpdHkgZnVuY3Rpb24uXG4gKiBAcGFyYW0ge09iamVjdH0gaW5wdXQgdGhlIGlucHV0LlxuICogQHJldHVybiB7T2JqZWN0fSB0aGUgc2FtZSBpbnB1dC5cbiAqL1xuZnVuY3Rpb24gaWRlbnRpdHkoaW5wdXQpIHtcbiAgICByZXR1cm4gaW5wdXQ7XG59XG5cbi8qKlxuICogRmlsbCBpbiBhbiBhcnJheSB3aXRoIGEgc3RyaW5nLlxuICogQHBhcmFtIHtTdHJpbmd9IHN0ciB0aGUgc3RyaW5nIHRvIHVzZS5cbiAqIEBwYXJhbSB7QXJyYXl8QXJyYXlCdWZmZXJ8VWludDhBcnJheXxCdWZmZXJ9IGFycmF5IHRoZSBhcnJheSB0byBmaWxsIGluICh3aWxsIGJlIG11dGF0ZWQpLlxuICogQHJldHVybiB7QXJyYXl8QXJyYXlCdWZmZXJ8VWludDhBcnJheXxCdWZmZXJ9IHRoZSB1cGRhdGVkIGFycmF5LlxuICovXG5mdW5jdGlvbiBzdHJpbmdUb0FycmF5TGlrZShzdHIsIGFycmF5KSB7XG4gICAgZm9yICh2YXIgaSA9IDA7IGkgPCBzdHIubGVuZ3RoOyArK2kpIHtcbiAgICAgICAgYXJyYXlbaV0gPSBzdHIuY2hhckNvZGVBdChpKSAmIDB4RkY7XG4gICAgfVxuICAgIHJldHVybiBhcnJheTtcbn1cblxuLyoqXG4gKiBBbiBoZWxwZXIgZm9yIHRoZSBmdW5jdGlvbiBhcnJheUxpa2VUb1N0cmluZy5cbiAqIFRoaXMgY29udGFpbnMgc3RhdGljIGluZm9ybWF0aW9uIGFuZCBmdW5jdGlvbnMgdGhhdFxuICogY2FuIGJlIG9wdGltaXplZCBieSB0aGUgYnJvd3NlciBKSVQgY29tcGlsZXIuXG4gKi9cbnZhciBhcnJheVRvU3RyaW5nSGVscGVyID0ge1xuICAgIC8qKlxuICAgICAqIFRyYW5zZm9ybSBhbiBhcnJheSBvZiBpbnQgaW50byBhIHN0cmluZywgY2h1bmsgYnkgY2h1bmsuXG4gICAgICogU2VlIHRoZSBwZXJmb3JtYW5jZXMgbm90ZXMgb24gYXJyYXlMaWtlVG9TdHJpbmcuXG4gICAgICogQHBhcmFtIHtBcnJheXxBcnJheUJ1ZmZlcnxVaW50OEFycmF5fEJ1ZmZlcn0gYXJyYXkgdGhlIGFycmF5IHRvIHRyYW5zZm9ybS5cbiAgICAgKiBAcGFyYW0ge1N0cmluZ30gdHlwZSB0aGUgdHlwZSBvZiB0aGUgYXJyYXkuXG4gICAgICogQHBhcmFtIHtJbnRlZ2VyfSBjaHVuayB0aGUgY2h1bmsgc2l6ZS5cbiAgICAgKiBAcmV0dXJuIHtTdHJpbmd9IHRoZSByZXN1bHRpbmcgc3RyaW5nLlxuICAgICAqIEB0aHJvd3MgRXJyb3IgaWYgdGhlIGNodW5rIGlzIHRvbyBiaWcgZm9yIHRoZSBzdGFjay5cbiAgICAgKi9cbiAgICBzdHJpbmdpZnlCeUNodW5rOiBmdW5jdGlvbihhcnJheSwgdHlwZSwgY2h1bmspIHtcbiAgICAgICAgdmFyIHJlc3VsdCA9IFtdLCBrID0gMCwgbGVuID0gYXJyYXkubGVuZ3RoO1xuICAgICAgICAvLyBzaG9ydGN1dFxuICAgICAgICBpZiAobGVuIDw9IGNodW5rKSB7XG4gICAgICAgICAgICByZXR1cm4gU3RyaW5nLmZyb21DaGFyQ29kZS5hcHBseShudWxsLCBhcnJheSk7XG4gICAgICAgIH1cbiAgICAgICAgd2hpbGUgKGsgPCBsZW4pIHtcbiAgICAgICAgICAgIGlmICh0eXBlID09PSBcImFycmF5XCIgfHwgdHlwZSA9PT0gXCJub2RlYnVmZmVyXCIpIHtcbiAgICAgICAgICAgICAgICByZXN1bHQucHVzaChTdHJpbmcuZnJvbUNoYXJDb2RlLmFwcGx5KG51bGwsIGFycmF5LnNsaWNlKGssIE1hdGgubWluKGsgKyBjaHVuaywgbGVuKSkpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIHJlc3VsdC5wdXNoKFN0cmluZy5mcm9tQ2hhckNvZGUuYXBwbHkobnVsbCwgYXJyYXkuc3ViYXJyYXkoaywgTWF0aC5taW4oayArIGNodW5rLCBsZW4pKSkpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgayArPSBjaHVuaztcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcmVzdWx0LmpvaW4oXCJcIik7XG4gICAgfSxcbiAgICAvKipcbiAgICAgKiBDYWxsIFN0cmluZy5mcm9tQ2hhckNvZGUgb24gZXZlcnkgaXRlbSBpbiB0aGUgYXJyYXkuXG4gICAgICogVGhpcyBpcyB0aGUgbmFpdmUgaW1wbGVtZW50YXRpb24sIHdoaWNoIGdlbmVyYXRlIEEgTE9UIG9mIGludGVybWVkaWF0ZSBzdHJpbmcuXG4gICAgICogVGhpcyBzaG91bGQgYmUgdXNlZCB3aGVuIGV2ZXJ5dGhpbmcgZWxzZSBmYWlsLlxuICAgICAqIEBwYXJhbSB7QXJyYXl8QXJyYXlCdWZmZXJ8VWludDhBcnJheXxCdWZmZXJ9IGFycmF5IHRoZSBhcnJheSB0byB0cmFuc2Zvcm0uXG4gICAgICogQHJldHVybiB7U3RyaW5nfSB0aGUgcmVzdWx0LlxuICAgICAqL1xuICAgIHN0cmluZ2lmeUJ5Q2hhcjogZnVuY3Rpb24oYXJyYXkpe1xuICAgICAgICB2YXIgcmVzdWx0U3RyID0gXCJcIjtcbiAgICAgICAgZm9yKHZhciBpID0gMDsgaSA8IGFycmF5Lmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICByZXN1bHRTdHIgKz0gU3RyaW5nLmZyb21DaGFyQ29kZShhcnJheVtpXSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHJlc3VsdFN0cjtcbiAgICB9LFxuICAgIGFwcGx5Q2FuQmVVc2VkIDoge1xuICAgICAgICAvKipcbiAgICAgICAgICogdHJ1ZSBpZiB0aGUgYnJvd3NlciBhY2NlcHRzIHRvIHVzZSBTdHJpbmcuZnJvbUNoYXJDb2RlIG9uIFVpbnQ4QXJyYXlcbiAgICAgICAgICovXG4gICAgICAgIHVpbnQ4YXJyYXkgOiAoZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICByZXR1cm4gc3VwcG9ydC51aW50OGFycmF5ICYmIFN0cmluZy5mcm9tQ2hhckNvZGUuYXBwbHkobnVsbCwgbmV3IFVpbnQ4QXJyYXkoMSkpLmxlbmd0aCA9PT0gMTtcbiAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pKCksXG4gICAgICAgIC8qKlxuICAgICAgICAgKiB0cnVlIGlmIHRoZSBicm93c2VyIGFjY2VwdHMgdG8gdXNlIFN0cmluZy5mcm9tQ2hhckNvZGUgb24gbm9kZWpzIEJ1ZmZlci5cbiAgICAgICAgICovXG4gICAgICAgIG5vZGVidWZmZXIgOiAoZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICByZXR1cm4gc3VwcG9ydC5ub2RlYnVmZmVyICYmIFN0cmluZy5mcm9tQ2hhckNvZGUuYXBwbHkobnVsbCwgbm9kZWpzVXRpbHMuYWxsb2NCdWZmZXIoMSkpLmxlbmd0aCA9PT0gMTtcbiAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pKClcbiAgICB9XG59O1xuXG4vKipcbiAqIFRyYW5zZm9ybSBhbiBhcnJheS1saWtlIG9iamVjdCB0byBhIHN0cmluZy5cbiAqIEBwYXJhbSB7QXJyYXl8QXJyYXlCdWZmZXJ8VWludDhBcnJheXxCdWZmZXJ9IGFycmF5IHRoZSBhcnJheSB0byB0cmFuc2Zvcm0uXG4gKiBAcmV0dXJuIHtTdHJpbmd9IHRoZSByZXN1bHQuXG4gKi9cbmZ1bmN0aW9uIGFycmF5TGlrZVRvU3RyaW5nKGFycmF5KSB7XG4gICAgLy8gUGVyZm9ybWFuY2VzIG5vdGVzIDpcbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLVxuICAgIC8vIFN0cmluZy5mcm9tQ2hhckNvZGUuYXBwbHkobnVsbCwgYXJyYXkpIGlzIHRoZSBmYXN0ZXN0LCBzZWVcbiAgICAvLyBzZWUgaHR0cDovL2pzcGVyZi5jb20vY29udmVydGluZy1hLXVpbnQ4YXJyYXktdG8tYS1zdHJpbmcvMlxuICAgIC8vIGJ1dCB0aGUgc3RhY2sgaXMgbGltaXRlZCAoYW5kIHdlIGNhbiBnZXQgaHVnZSBhcnJheXMgISkuXG4gICAgLy9cbiAgICAvLyByZXN1bHQgKz0gU3RyaW5nLmZyb21DaGFyQ29kZShhcnJheVtpXSk7IGdlbmVyYXRlIHRvbyBtYW55IHN0cmluZ3MgIVxuICAgIC8vXG4gICAgLy8gVGhpcyBjb2RlIGlzIGluc3BpcmVkIGJ5IGh0dHA6Ly9qc3BlcmYuY29tL2FycmF5YnVmZmVyLXRvLXN0cmluZy1hcHBseS1wZXJmb3JtYW5jZS8yXG4gICAgLy8gVE9ETyA6IHdlIG5vdyBoYXZlIHdvcmtlcnMgdGhhdCBzcGxpdCB0aGUgd29yay4gRG8gd2Ugc3RpbGwgbmVlZCB0aGF0ID9cbiAgICB2YXIgY2h1bmsgPSA2NTUzNixcbiAgICAgICAgdHlwZSA9IGV4cG9ydHMuZ2V0VHlwZU9mKGFycmF5KSxcbiAgICAgICAgY2FuVXNlQXBwbHkgPSB0cnVlO1xuICAgIGlmICh0eXBlID09PSBcInVpbnQ4YXJyYXlcIikge1xuICAgICAgICBjYW5Vc2VBcHBseSA9IGFycmF5VG9TdHJpbmdIZWxwZXIuYXBwbHlDYW5CZVVzZWQudWludDhhcnJheTtcbiAgICB9IGVsc2UgaWYgKHR5cGUgPT09IFwibm9kZWJ1ZmZlclwiKSB7XG4gICAgICAgIGNhblVzZUFwcGx5ID0gYXJyYXlUb1N0cmluZ0hlbHBlci5hcHBseUNhbkJlVXNlZC5ub2RlYnVmZmVyO1xuICAgIH1cblxuICAgIGlmIChjYW5Vc2VBcHBseSkge1xuICAgICAgICB3aGlsZSAoY2h1bmsgPiAxKSB7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIHJldHVybiBhcnJheVRvU3RyaW5nSGVscGVyLnN0cmluZ2lmeUJ5Q2h1bmsoYXJyYXksIHR5cGUsIGNodW5rKTtcbiAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgICAgICAgICBjaHVuayA9IE1hdGguZmxvb3IoY2h1bmsgLyAyKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIG5vIGFwcGx5IG9yIGNodW5rIGVycm9yIDogc2xvdyBhbmQgcGFpbmZ1bCBhbGdvcml0aG1cbiAgICAvLyBkZWZhdWx0IGJyb3dzZXIgb24gYW5kcm9pZCA0LipcbiAgICByZXR1cm4gYXJyYXlUb1N0cmluZ0hlbHBlci5zdHJpbmdpZnlCeUNoYXIoYXJyYXkpO1xufVxuXG5leHBvcnRzLmFwcGx5RnJvbUNoYXJDb2RlID0gYXJyYXlMaWtlVG9TdHJpbmc7XG5cblxuLyoqXG4gKiBDb3B5IHRoZSBkYXRhIGZyb20gYW4gYXJyYXktbGlrZSB0byBhbiBvdGhlciBhcnJheS1saWtlLlxuICogQHBhcmFtIHtBcnJheXxBcnJheUJ1ZmZlcnxVaW50OEFycmF5fEJ1ZmZlcn0gYXJyYXlGcm9tIHRoZSBvcmlnaW4gYXJyYXkuXG4gKiBAcGFyYW0ge0FycmF5fEFycmF5QnVmZmVyfFVpbnQ4QXJyYXl8QnVmZmVyfSBhcnJheVRvIHRoZSBkZXN0aW5hdGlvbiBhcnJheSB3aGljaCB3aWxsIGJlIG11dGF0ZWQuXG4gKiBAcmV0dXJuIHtBcnJheXxBcnJheUJ1ZmZlcnxVaW50OEFycmF5fEJ1ZmZlcn0gdGhlIHVwZGF0ZWQgZGVzdGluYXRpb24gYXJyYXkuXG4gKi9cbmZ1bmN0aW9uIGFycmF5TGlrZVRvQXJyYXlMaWtlKGFycmF5RnJvbSwgYXJyYXlUbykge1xuICAgIGZvciAodmFyIGkgPSAwOyBpIDwgYXJyYXlGcm9tLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGFycmF5VG9baV0gPSBhcnJheUZyb21baV07XG4gICAgfVxuICAgIHJldHVybiBhcnJheVRvO1xufVxuXG4vLyBhIG1hdHJpeCBjb250YWluaW5nIGZ1bmN0aW9ucyB0byB0cmFuc2Zvcm0gZXZlcnl0aGluZyBpbnRvIGV2ZXJ5dGhpbmcuXG52YXIgdHJhbnNmb3JtID0ge307XG5cbi8vIHN0cmluZyB0byA/XG50cmFuc2Zvcm1bXCJzdHJpbmdcIl0gPSB7XG4gICAgXCJzdHJpbmdcIjogaWRlbnRpdHksXG4gICAgXCJhcnJheVwiOiBmdW5jdGlvbihpbnB1dCkge1xuICAgICAgICByZXR1cm4gc3RyaW5nVG9BcnJheUxpa2UoaW5wdXQsIG5ldyBBcnJheShpbnB1dC5sZW5ndGgpKTtcbiAgICB9LFxuICAgIFwiYXJyYXlidWZmZXJcIjogZnVuY3Rpb24oaW5wdXQpIHtcbiAgICAgICAgcmV0dXJuIHRyYW5zZm9ybVtcInN0cmluZ1wiXVtcInVpbnQ4YXJyYXlcIl0oaW5wdXQpLmJ1ZmZlcjtcbiAgICB9LFxuICAgIFwidWludDhhcnJheVwiOiBmdW5jdGlvbihpbnB1dCkge1xuICAgICAgICByZXR1cm4gc3RyaW5nVG9BcnJheUxpa2UoaW5wdXQsIG5ldyBVaW50OEFycmF5KGlucHV0Lmxlbmd0aCkpO1xuICAgIH0sXG4gICAgXCJub2RlYnVmZmVyXCI6IGZ1bmN0aW9uKGlucHV0KSB7XG4gICAgICAgIHJldHVybiBzdHJpbmdUb0FycmF5TGlrZShpbnB1dCwgbm9kZWpzVXRpbHMuYWxsb2NCdWZmZXIoaW5wdXQubGVuZ3RoKSk7XG4gICAgfVxufTtcblxuLy8gYXJyYXkgdG8gP1xudHJhbnNmb3JtW1wiYXJyYXlcIl0gPSB7XG4gICAgXCJzdHJpbmdcIjogYXJyYXlMaWtlVG9TdHJpbmcsXG4gICAgXCJhcnJheVwiOiBpZGVudGl0eSxcbiAgICBcImFycmF5YnVmZmVyXCI6IGZ1bmN0aW9uKGlucHV0KSB7XG4gICAgICAgIHJldHVybiAobmV3IFVpbnQ4QXJyYXkoaW5wdXQpKS5idWZmZXI7XG4gICAgfSxcbiAgICBcInVpbnQ4YXJyYXlcIjogZnVuY3Rpb24oaW5wdXQpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBVaW50OEFycmF5KGlucHV0KTtcbiAgICB9LFxuICAgIFwibm9kZWJ1ZmZlclwiOiBmdW5jdGlvbihpbnB1dCkge1xuICAgICAgICByZXR1cm4gbm9kZWpzVXRpbHMubmV3QnVmZmVyRnJvbShpbnB1dCk7XG4gICAgfVxufTtcblxuLy8gYXJyYXlidWZmZXIgdG8gP1xudHJhbnNmb3JtW1wiYXJyYXlidWZmZXJcIl0gPSB7XG4gICAgXCJzdHJpbmdcIjogZnVuY3Rpb24oaW5wdXQpIHtcbiAgICAgICAgcmV0dXJuIGFycmF5TGlrZVRvU3RyaW5nKG5ldyBVaW50OEFycmF5KGlucHV0KSk7XG4gICAgfSxcbiAgICBcImFycmF5XCI6IGZ1bmN0aW9uKGlucHV0KSB7XG4gICAgICAgIHJldHVybiBhcnJheUxpa2VUb0FycmF5TGlrZShuZXcgVWludDhBcnJheShpbnB1dCksIG5ldyBBcnJheShpbnB1dC5ieXRlTGVuZ3RoKSk7XG4gICAgfSxcbiAgICBcImFycmF5YnVmZmVyXCI6IGlkZW50aXR5LFxuICAgIFwidWludDhhcnJheVwiOiBmdW5jdGlvbihpbnB1dCkge1xuICAgICAgICByZXR1cm4gbmV3IFVpbnQ4QXJyYXkoaW5wdXQpO1xuICAgIH0sXG4gICAgXCJub2RlYnVmZmVyXCI6IGZ1bmN0aW9uKGlucHV0KSB7XG4gICAgICAgIHJldHVybiBub2RlanNVdGlscy5uZXdCdWZmZXJGcm9tKG5ldyBVaW50OEFycmF5KGlucHV0KSk7XG4gICAgfVxufTtcblxuLy8gdWludDhhcnJheSB0byA/XG50cmFuc2Zvcm1bXCJ1aW50OGFycmF5XCJdID0ge1xuICAgIFwic3RyaW5nXCI6IGFycmF5TGlrZVRvU3RyaW5nLFxuICAgIFwiYXJyYXlcIjogZnVuY3Rpb24oaW5wdXQpIHtcbiAgICAgICAgcmV0dXJuIGFycmF5TGlrZVRvQXJyYXlMaWtlKGlucHV0LCBuZXcgQXJyYXkoaW5wdXQubGVuZ3RoKSk7XG4gICAgfSxcbiAgICBcImFycmF5YnVmZmVyXCI6IGZ1bmN0aW9uKGlucHV0KSB7XG4gICAgICAgIHJldHVybiBpbnB1dC5idWZmZXI7XG4gICAgfSxcbiAgICBcInVpbnQ4YXJyYXlcIjogaWRlbnRpdHksXG4gICAgXCJub2RlYnVmZmVyXCI6IGZ1bmN0aW9uKGlucHV0KSB7XG4gICAgICAgIHJldHVybiBub2RlanNVdGlscy5uZXdCdWZmZXJGcm9tKGlucHV0KTtcbiAgICB9XG59O1xuXG4vLyBub2RlYnVmZmVyIHRvID9cbnRyYW5zZm9ybVtcIm5vZGVidWZmZXJcIl0gPSB7XG4gICAgXCJzdHJpbmdcIjogYXJyYXlMaWtlVG9TdHJpbmcsXG4gICAgXCJhcnJheVwiOiBmdW5jdGlvbihpbnB1dCkge1xuICAgICAgICByZXR1cm4gYXJyYXlMaWtlVG9BcnJheUxpa2UoaW5wdXQsIG5ldyBBcnJheShpbnB1dC5sZW5ndGgpKTtcbiAgICB9LFxuICAgIFwiYXJyYXlidWZmZXJcIjogZnVuY3Rpb24oaW5wdXQpIHtcbiAgICAgICAgcmV0dXJuIHRyYW5zZm9ybVtcIm5vZGVidWZmZXJcIl1bXCJ1aW50OGFycmF5XCJdKGlucHV0KS5idWZmZXI7XG4gICAgfSxcbiAgICBcInVpbnQ4YXJyYXlcIjogZnVuY3Rpb24oaW5wdXQpIHtcbiAgICAgICAgcmV0dXJuIGFycmF5TGlrZVRvQXJyYXlMaWtlKGlucHV0LCBuZXcgVWludDhBcnJheShpbnB1dC5sZW5ndGgpKTtcbiAgICB9LFxuICAgIFwibm9kZWJ1ZmZlclwiOiBpZGVudGl0eVxufTtcblxuLyoqXG4gKiBUcmFuc2Zvcm0gYW4gaW5wdXQgaW50byBhbnkgdHlwZS5cbiAqIFRoZSBzdXBwb3J0ZWQgb3V0cHV0IHR5cGUgYXJlIDogc3RyaW5nLCBhcnJheSwgdWludDhhcnJheSwgYXJyYXlidWZmZXIsIG5vZGVidWZmZXIuXG4gKiBJZiBubyBvdXRwdXQgdHlwZSBpcyBzcGVjaWZpZWQsIHRoZSB1bm1vZGlmaWVkIGlucHV0IHdpbGwgYmUgcmV0dXJuZWQuXG4gKiBAcGFyYW0ge1N0cmluZ30gb3V0cHV0VHlwZSB0aGUgb3V0cHV0IHR5cGUuXG4gKiBAcGFyYW0ge1N0cmluZ3xBcnJheXxBcnJheUJ1ZmZlcnxVaW50OEFycmF5fEJ1ZmZlcn0gaW5wdXQgdGhlIGlucHV0IHRvIGNvbnZlcnQuXG4gKiBAdGhyb3dzIHtFcnJvcn0gYW4gRXJyb3IgaWYgdGhlIGJyb3dzZXIgZG9lc24ndCBzdXBwb3J0IHRoZSByZXF1ZXN0ZWQgb3V0cHV0IHR5cGUuXG4gKi9cbmV4cG9ydHMudHJhbnNmb3JtVG8gPSBmdW5jdGlvbihvdXRwdXRUeXBlLCBpbnB1dCkge1xuICAgIGlmICghaW5wdXQpIHtcbiAgICAgICAgLy8gdW5kZWZpbmVkLCBudWxsLCBldGNcbiAgICAgICAgLy8gYW4gZW1wdHkgc3RyaW5nIHdvbid0IGhhcm0uXG4gICAgICAgIGlucHV0ID0gXCJcIjtcbiAgICB9XG4gICAgaWYgKCFvdXRwdXRUeXBlKSB7XG4gICAgICAgIHJldHVybiBpbnB1dDtcbiAgICB9XG4gICAgZXhwb3J0cy5jaGVja1N1cHBvcnQob3V0cHV0VHlwZSk7XG4gICAgdmFyIGlucHV0VHlwZSA9IGV4cG9ydHMuZ2V0VHlwZU9mKGlucHV0KTtcbiAgICB2YXIgcmVzdWx0ID0gdHJhbnNmb3JtW2lucHV0VHlwZV1bb3V0cHV0VHlwZV0oaW5wdXQpO1xuICAgIHJldHVybiByZXN1bHQ7XG59O1xuXG4vKipcbiAqIFJlc29sdmUgYWxsIHJlbGF0aXZlIHBhdGggY29tcG9uZW50cywgXCIuXCIgYW5kIFwiLi5cIiwgaW4gYSBwYXRoLiBJZiB0aGVzZSByZWxhdGl2ZSBjb21wb25lbnRzXG4gKiB0cmF2ZXJzZSBhYm92ZSB0aGUgcm9vdCB0aGVuIHRoZSByZXN1bHRpbmcgcGF0aCB3aWxsIG9ubHkgY29udGFpbiB0aGUgZmluYWwgcGF0aCBjb21wb25lbnQuXG4gKlxuICogQWxsIGVtcHR5IGNvbXBvbmVudHMsIGUuZy4gXCIvL1wiLCBhcmUgcmVtb3ZlZC5cbiAqIEBwYXJhbSB7c3RyaW5nfSBwYXRoIEEgcGF0aCB3aXRoIC8gb3IgXFwgc2VwYXJhdG9yc1xuICogQHJldHVybnMge3N0cmluZ30gVGhlIHBhdGggd2l0aCBhbGwgcmVsYXRpdmUgcGF0aCBjb21wb25lbnRzIHJlc29sdmVkLlxuICovXG5leHBvcnRzLnJlc29sdmUgPSBmdW5jdGlvbihwYXRoKSB7XG4gICAgdmFyIHBhcnRzID0gcGF0aC5zcGxpdChcIi9cIik7XG4gICAgdmFyIHJlc3VsdCA9IFtdO1xuICAgIGZvciAodmFyIGluZGV4ID0gMDsgaW5kZXggPCBwYXJ0cy5sZW5ndGg7IGluZGV4KyspIHtcbiAgICAgICAgdmFyIHBhcnQgPSBwYXJ0c1tpbmRleF07XG4gICAgICAgIC8vIEFsbG93IHRoZSBmaXJzdCBhbmQgbGFzdCBjb21wb25lbnQgdG8gYmUgZW1wdHkgZm9yIHRyYWlsaW5nIHNsYXNoZXMuXG4gICAgICAgIGlmIChwYXJ0ID09PSBcIi5cIiB8fCAocGFydCA9PT0gXCJcIiAmJiBpbmRleCAhPT0gMCAmJiBpbmRleCAhPT0gcGFydHMubGVuZ3RoIC0gMSkpIHtcbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9IGVsc2UgaWYgKHBhcnQgPT09IFwiLi5cIikge1xuICAgICAgICAgICAgcmVzdWx0LnBvcCgpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgcmVzdWx0LnB1c2gocGFydCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHJlc3VsdC5qb2luKFwiL1wiKTtcbn07XG5cbi8qKlxuICogUmV0dXJuIHRoZSB0eXBlIG9mIHRoZSBpbnB1dC5cbiAqIFRoZSB0eXBlIHdpbGwgYmUgaW4gYSBmb3JtYXQgdmFsaWQgZm9yIEpTWmlwLnV0aWxzLnRyYW5zZm9ybVRvIDogc3RyaW5nLCBhcnJheSwgdWludDhhcnJheSwgYXJyYXlidWZmZXIuXG4gKiBAcGFyYW0ge09iamVjdH0gaW5wdXQgdGhlIGlucHV0IHRvIGlkZW50aWZ5LlxuICogQHJldHVybiB7U3RyaW5nfSB0aGUgKGxvd2VyY2FzZSkgdHlwZSBvZiB0aGUgaW5wdXQuXG4gKi9cbmV4cG9ydHMuZ2V0VHlwZU9mID0gZnVuY3Rpb24oaW5wdXQpIHtcbiAgICBpZiAodHlwZW9mIGlucHV0ID09PSBcInN0cmluZ1wiKSB7XG4gICAgICAgIHJldHVybiBcInN0cmluZ1wiO1xuICAgIH1cbiAgICBpZiAoT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy5jYWxsKGlucHV0KSA9PT0gXCJbb2JqZWN0IEFycmF5XVwiKSB7XG4gICAgICAgIHJldHVybiBcImFycmF5XCI7XG4gICAgfVxuICAgIGlmIChzdXBwb3J0Lm5vZGVidWZmZXIgJiYgbm9kZWpzVXRpbHMuaXNCdWZmZXIoaW5wdXQpKSB7XG4gICAgICAgIHJldHVybiBcIm5vZGVidWZmZXJcIjtcbiAgICB9XG4gICAgaWYgKHN1cHBvcnQudWludDhhcnJheSAmJiBpbnB1dCBpbnN0YW5jZW9mIFVpbnQ4QXJyYXkpIHtcbiAgICAgICAgcmV0dXJuIFwidWludDhhcnJheVwiO1xuICAgIH1cbiAgICBpZiAoc3VwcG9ydC5hcnJheWJ1ZmZlciAmJiBpbnB1dCBpbnN0YW5jZW9mIEFycmF5QnVmZmVyKSB7XG4gICAgICAgIHJldHVybiBcImFycmF5YnVmZmVyXCI7XG4gICAgfVxufTtcblxuLyoqXG4gKiBUaHJvdyBhbiBleGNlcHRpb24gaWYgdGhlIHR5cGUgaXMgbm90IHN1cHBvcnRlZC5cbiAqIEBwYXJhbSB7U3RyaW5nfSB0eXBlIHRoZSB0eXBlIHRvIGNoZWNrLlxuICogQHRocm93cyB7RXJyb3J9IGFuIEVycm9yIGlmIHRoZSBicm93c2VyIGRvZXNuJ3Qgc3VwcG9ydCB0aGUgcmVxdWVzdGVkIHR5cGUuXG4gKi9cbmV4cG9ydHMuY2hlY2tTdXBwb3J0ID0gZnVuY3Rpb24odHlwZSkge1xuICAgIHZhciBzdXBwb3J0ZWQgPSBzdXBwb3J0W3R5cGUudG9Mb3dlckNhc2UoKV07XG4gICAgaWYgKCFzdXBwb3J0ZWQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKHR5cGUgKyBcIiBpcyBub3Qgc3VwcG9ydGVkIGJ5IHRoaXMgcGxhdGZvcm1cIik7XG4gICAgfVxufTtcblxuZXhwb3J0cy5NQVhfVkFMVUVfMTZCSVRTID0gNjU1MzU7XG5leHBvcnRzLk1BWF9WQUxVRV8zMkJJVFMgPSAtMTsgLy8gd2VsbCwgXCJcXHhGRlxceEZGXFx4RkZcXHhGRlxceEZGXFx4RkZcXHhGRlxceEZGXCIgaXMgcGFyc2VkIGFzIC0xXG5cbi8qKlxuICogUHJldHRpZnkgYSBzdHJpbmcgcmVhZCBhcyBiaW5hcnkuXG4gKiBAcGFyYW0ge3N0cmluZ30gc3RyIHRoZSBzdHJpbmcgdG8gcHJldHRpZnkuXG4gKiBAcmV0dXJuIHtzdHJpbmd9IGEgcHJldHR5IHN0cmluZy5cbiAqL1xuZXhwb3J0cy5wcmV0dHkgPSBmdW5jdGlvbihzdHIpIHtcbiAgICB2YXIgcmVzID0gXCJcIixcbiAgICAgICAgY29kZSwgaTtcbiAgICBmb3IgKGkgPSAwOyBpIDwgKHN0ciB8fCBcIlwiKS5sZW5ndGg7IGkrKykge1xuICAgICAgICBjb2RlID0gc3RyLmNoYXJDb2RlQXQoaSk7XG4gICAgICAgIHJlcyArPSBcIlxcXFx4XCIgKyAoY29kZSA8IDE2ID8gXCIwXCIgOiBcIlwiKSArIGNvZGUudG9TdHJpbmcoMTYpLnRvVXBwZXJDYXNlKCk7XG4gICAgfVxuICAgIHJldHVybiByZXM7XG59O1xuXG4vKipcbiAqIERlZmVyIHRoZSBjYWxsIG9mIGEgZnVuY3Rpb24uXG4gKiBAcGFyYW0ge0Z1bmN0aW9ufSBjYWxsYmFjayB0aGUgZnVuY3Rpb24gdG8gY2FsbCBhc3luY2hyb25vdXNseS5cbiAqIEBwYXJhbSB7QXJyYXl9IGFyZ3MgdGhlIGFyZ3VtZW50cyB0byBnaXZlIHRvIHRoZSBjYWxsYmFjay5cbiAqL1xuZXhwb3J0cy5kZWxheSA9IGZ1bmN0aW9uKGNhbGxiYWNrLCBhcmdzLCBzZWxmKSB7XG4gICAgc2V0SW1tZWRpYXRlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgY2FsbGJhY2suYXBwbHkoc2VsZiB8fCBudWxsLCBhcmdzIHx8IFtdKTtcbiAgICB9KTtcbn07XG5cbi8qKlxuICogRXh0ZW5kcyBhIHByb3RvdHlwZSB3aXRoIGFuIG90aGVyLCB3aXRob3V0IGNhbGxpbmcgYSBjb25zdHJ1Y3RvciB3aXRoXG4gKiBzaWRlIGVmZmVjdHMuIEluc3BpcmVkIGJ5IG5vZGVqcycgYHV0aWxzLmluaGVyaXRzYFxuICogQHBhcmFtIHtGdW5jdGlvbn0gY3RvciB0aGUgY29uc3RydWN0b3IgdG8gYXVnbWVudFxuICogQHBhcmFtIHtGdW5jdGlvbn0gc3VwZXJDdG9yIHRoZSBwYXJlbnQgY29uc3RydWN0b3IgdG8gdXNlXG4gKi9cbmV4cG9ydHMuaW5oZXJpdHMgPSBmdW5jdGlvbiAoY3Rvciwgc3VwZXJDdG9yKSB7XG4gICAgdmFyIE9iaiA9IGZ1bmN0aW9uKCkge307XG4gICAgT2JqLnByb3RvdHlwZSA9IHN1cGVyQ3Rvci5wcm90b3R5cGU7XG4gICAgY3Rvci5wcm90b3R5cGUgPSBuZXcgT2JqKCk7XG59O1xuXG4vKipcbiAqIE1lcmdlIHRoZSBvYmplY3RzIHBhc3NlZCBhcyBwYXJhbWV0ZXJzIGludG8gYSBuZXcgb25lLlxuICogQHByaXZhdGVcbiAqIEBwYXJhbSB7Li4uT2JqZWN0fSB2YXJfYXJncyBBbGwgb2JqZWN0cyB0byBtZXJnZS5cbiAqIEByZXR1cm4ge09iamVjdH0gYSBuZXcgb2JqZWN0IHdpdGggdGhlIGRhdGEgb2YgdGhlIG90aGVycy5cbiAqL1xuZXhwb3J0cy5leHRlbmQgPSBmdW5jdGlvbigpIHtcbiAgICB2YXIgcmVzdWx0ID0ge30sIGksIGF0dHI7XG4gICAgZm9yIChpID0gMDsgaSA8IGFyZ3VtZW50cy5sZW5ndGg7IGkrKykgeyAvLyBhcmd1bWVudHMgaXMgbm90IGVudW1lcmFibGUgaW4gc29tZSBicm93c2Vyc1xuICAgICAgICBmb3IgKGF0dHIgaW4gYXJndW1lbnRzW2ldKSB7XG4gICAgICAgICAgICBpZiAoT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGFyZ3VtZW50c1tpXSwgYXR0cikgJiYgdHlwZW9mIHJlc3VsdFthdHRyXSA9PT0gXCJ1bmRlZmluZWRcIikge1xuICAgICAgICAgICAgICAgIHJlc3VsdFthdHRyXSA9IGFyZ3VtZW50c1tpXVthdHRyXTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gcmVzdWx0O1xufTtcblxuLyoqXG4gKiBUcmFuc2Zvcm0gYXJiaXRyYXJ5IGNvbnRlbnQgaW50byBhIFByb21pc2UuXG4gKiBAcGFyYW0ge1N0cmluZ30gbmFtZSBhIG5hbWUgZm9yIHRoZSBjb250ZW50IGJlaW5nIHByb2Nlc3NlZC5cbiAqIEBwYXJhbSB7T2JqZWN0fSBpbnB1dERhdGEgdGhlIGNvbnRlbnQgdG8gcHJvY2Vzcy5cbiAqIEBwYXJhbSB7Qm9vbGVhbn0gaXNCaW5hcnkgdHJ1ZSBpZiB0aGUgY29udGVudCBpcyBub3QgYW4gdW5pY29kZSBzdHJpbmdcbiAqIEBwYXJhbSB7Qm9vbGVhbn0gaXNPcHRpbWl6ZWRCaW5hcnlTdHJpbmcgdHJ1ZSBpZiB0aGUgc3RyaW5nIGNvbnRlbnQgb25seSBoYXMgb25lIGJ5dGUgcGVyIGNoYXJhY3Rlci5cbiAqIEBwYXJhbSB7Qm9vbGVhbn0gaXNCYXNlNjQgdHJ1ZSBpZiB0aGUgc3RyaW5nIGNvbnRlbnQgaXMgZW5jb2RlZCB3aXRoIGJhc2U2NC5cbiAqIEByZXR1cm4ge1Byb21pc2V9IGEgcHJvbWlzZSBpbiBhIGZvcm1hdCB1c2FibGUgYnkgSlNaaXAuXG4gKi9cbmV4cG9ydHMucHJlcGFyZUNvbnRlbnQgPSBmdW5jdGlvbihuYW1lLCBpbnB1dERhdGEsIGlzQmluYXJ5LCBpc09wdGltaXplZEJpbmFyeVN0cmluZywgaXNCYXNlNjQpIHtcblxuICAgIC8vIGlmIGlucHV0RGF0YSBpcyBhbHJlYWR5IGEgcHJvbWlzZSwgdGhpcyBmbGF0dGVuIGl0LlxuICAgIHZhciBwcm9taXNlID0gZXh0ZXJuYWwuUHJvbWlzZS5yZXNvbHZlKGlucHV0RGF0YSkudGhlbihmdW5jdGlvbihkYXRhKSB7XG5cblxuICAgICAgICB2YXIgaXNCbG9iID0gc3VwcG9ydC5ibG9iICYmIChkYXRhIGluc3RhbmNlb2YgQmxvYiB8fCBbXCJbb2JqZWN0IEZpbGVdXCIsIFwiW29iamVjdCBCbG9iXVwiXS5pbmRleE9mKE9iamVjdC5wcm90b3R5cGUudG9TdHJpbmcuY2FsbChkYXRhKSkgIT09IC0xKTtcblxuICAgICAgICBpZiAoaXNCbG9iICYmIHR5cGVvZiBGaWxlUmVhZGVyICE9PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICAgICAgICByZXR1cm4gbmV3IGV4dGVybmFsLlByb21pc2UoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkge1xuICAgICAgICAgICAgICAgIHZhciByZWFkZXIgPSBuZXcgRmlsZVJlYWRlcigpO1xuXG4gICAgICAgICAgICAgICAgcmVhZGVyLm9ubG9hZCA9IGZ1bmN0aW9uKGUpIHtcbiAgICAgICAgICAgICAgICAgICAgcmVzb2x2ZShlLnRhcmdldC5yZXN1bHQpO1xuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgcmVhZGVyLm9uZXJyb3IgPSBmdW5jdGlvbihlKSB7XG4gICAgICAgICAgICAgICAgICAgIHJlamVjdChlLnRhcmdldC5lcnJvcik7XG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICByZWFkZXIucmVhZEFzQXJyYXlCdWZmZXIoZGF0YSk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHJldHVybiBkYXRhO1xuICAgICAgICB9XG4gICAgfSk7XG5cbiAgICByZXR1cm4gcHJvbWlzZS50aGVuKGZ1bmN0aW9uKGRhdGEpIHtcbiAgICAgICAgdmFyIGRhdGFUeXBlID0gZXhwb3J0cy5nZXRUeXBlT2YoZGF0YSk7XG5cbiAgICAgICAgaWYgKCFkYXRhVHlwZSkge1xuICAgICAgICAgICAgcmV0dXJuIGV4dGVybmFsLlByb21pc2UucmVqZWN0KFxuICAgICAgICAgICAgICAgIG5ldyBFcnJvcihcIkNhbid0IHJlYWQgdGhlIGRhdGEgb2YgJ1wiICsgbmFtZSArIFwiJy4gSXMgaXQgXCIgK1xuICAgICAgICAgICAgICAgICAgICAgICAgICBcImluIGEgc3VwcG9ydGVkIEphdmFTY3JpcHQgdHlwZSAoU3RyaW5nLCBCbG9iLCBBcnJheUJ1ZmZlciwgZXRjKSA/XCIpXG4gICAgICAgICAgICApO1xuICAgICAgICB9XG4gICAgICAgIC8vIHNwZWNpYWwgY2FzZSA6IGl0J3Mgd2F5IGVhc2llciB0byB3b3JrIHdpdGggVWludDhBcnJheSB0aGFuIHdpdGggQXJyYXlCdWZmZXJcbiAgICAgICAgaWYgKGRhdGFUeXBlID09PSBcImFycmF5YnVmZmVyXCIpIHtcbiAgICAgICAgICAgIGRhdGEgPSBleHBvcnRzLnRyYW5zZm9ybVRvKFwidWludDhhcnJheVwiLCBkYXRhKTtcbiAgICAgICAgfSBlbHNlIGlmIChkYXRhVHlwZSA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICAgICAgaWYgKGlzQmFzZTY0KSB7XG4gICAgICAgICAgICAgICAgZGF0YSA9IGJhc2U2NC5kZWNvZGUoZGF0YSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChpc0JpbmFyeSkge1xuICAgICAgICAgICAgICAgIC8vIG9wdGltaXplZEJpbmFyeVN0cmluZyA9PT0gdHJ1ZSBtZWFucyB0aGF0IHRoZSBmaWxlIGhhcyBhbHJlYWR5IGJlZW4gZmlsdGVyZWQgd2l0aCBhIDB4RkYgbWFza1xuICAgICAgICAgICAgICAgIGlmIChpc09wdGltaXplZEJpbmFyeVN0cmluZyAhPT0gdHJ1ZSkge1xuICAgICAgICAgICAgICAgICAgICAvLyB0aGlzIGlzIGEgc3RyaW5nLCBub3QgaW4gYSBiYXNlNjQgZm9ybWF0LlxuICAgICAgICAgICAgICAgICAgICAvLyBCZSBzdXJlIHRoYXQgdGhpcyBpcyBhIGNvcnJlY3QgXCJiaW5hcnkgc3RyaW5nXCJcbiAgICAgICAgICAgICAgICAgICAgZGF0YSA9IHN0cmluZzJiaW5hcnkoZGF0YSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBkYXRhO1xuICAgIH0pO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/zipEntries.js":
/*!**********************************************!*\
  !*** ./node_modules/jszip/lib/zipEntries.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar readerFor = __webpack_require__(/*! ./reader/readerFor */ \"(ssr)/./node_modules/jszip/lib/reader/readerFor.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar sig = __webpack_require__(/*! ./signature */ \"(ssr)/./node_modules/jszip/lib/signature.js\");\nvar ZipEntry = __webpack_require__(/*! ./zipEntry */ \"(ssr)/./node_modules/jszip/lib/zipEntry.js\");\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\n//  class ZipEntries {{{\n/**\n * All the entries in the zip file.\n * @constructor\n * @param {Object} loadOptions Options for loading the stream.\n */\nfunction ZipEntries(loadOptions) {\n    this.files = [];\n    this.loadOptions = loadOptions;\n}\nZipEntries.prototype = {\n    /**\n     * Check that the reader is on the specified signature.\n     * @param {string} expectedSignature the expected signature.\n     * @throws {Error} if it is an other signature.\n     */\n    checkSignature: function(expectedSignature) {\n        if (!this.reader.readAndCheckSignature(expectedSignature)) {\n            this.reader.index -= 4;\n            var signature = this.reader.readString(4);\n            throw new Error(\"Corrupted zip or bug: unexpected signature \" + \"(\" + utils.pretty(signature) + \", expected \" + utils.pretty(expectedSignature) + \")\");\n        }\n    },\n    /**\n     * Check if the given signature is at the given index.\n     * @param {number} askedIndex the index to check.\n     * @param {string} expectedSignature the signature to expect.\n     * @return {boolean} true if the signature is here, false otherwise.\n     */\n    isSignature: function(askedIndex, expectedSignature) {\n        var currentIndex = this.reader.index;\n        this.reader.setIndex(askedIndex);\n        var signature = this.reader.readString(4);\n        var result = signature === expectedSignature;\n        this.reader.setIndex(currentIndex);\n        return result;\n    },\n    /**\n     * Read the end of the central directory.\n     */\n    readBlockEndOfCentral: function() {\n        this.diskNumber = this.reader.readInt(2);\n        this.diskWithCentralDirStart = this.reader.readInt(2);\n        this.centralDirRecordsOnThisDisk = this.reader.readInt(2);\n        this.centralDirRecords = this.reader.readInt(2);\n        this.centralDirSize = this.reader.readInt(4);\n        this.centralDirOffset = this.reader.readInt(4);\n\n        this.zipCommentLength = this.reader.readInt(2);\n        // warning : the encoding depends of the system locale\n        // On a linux machine with LANG=en_US.utf8, this field is utf8 encoded.\n        // On a windows machine, this field is encoded with the localized windows code page.\n        var zipComment = this.reader.readData(this.zipCommentLength);\n        var decodeParamType = support.uint8array ? \"uint8array\" : \"array\";\n        // To get consistent behavior with the generation part, we will assume that\n        // this is utf8 encoded unless specified otherwise.\n        var decodeContent = utils.transformTo(decodeParamType, zipComment);\n        this.zipComment = this.loadOptions.decodeFileName(decodeContent);\n    },\n    /**\n     * Read the end of the Zip 64 central directory.\n     * Not merged with the method readEndOfCentral :\n     * The end of central can coexist with its Zip64 brother,\n     * I don't want to read the wrong number of bytes !\n     */\n    readBlockZip64EndOfCentral: function() {\n        this.zip64EndOfCentralSize = this.reader.readInt(8);\n        this.reader.skip(4);\n        // this.versionMadeBy = this.reader.readString(2);\n        // this.versionNeeded = this.reader.readInt(2);\n        this.diskNumber = this.reader.readInt(4);\n        this.diskWithCentralDirStart = this.reader.readInt(4);\n        this.centralDirRecordsOnThisDisk = this.reader.readInt(8);\n        this.centralDirRecords = this.reader.readInt(8);\n        this.centralDirSize = this.reader.readInt(8);\n        this.centralDirOffset = this.reader.readInt(8);\n\n        this.zip64ExtensibleData = {};\n        var extraDataSize = this.zip64EndOfCentralSize - 44,\n            index = 0,\n            extraFieldId,\n            extraFieldLength,\n            extraFieldValue;\n        while (index < extraDataSize) {\n            extraFieldId = this.reader.readInt(2);\n            extraFieldLength = this.reader.readInt(4);\n            extraFieldValue = this.reader.readData(extraFieldLength);\n            this.zip64ExtensibleData[extraFieldId] = {\n                id: extraFieldId,\n                length: extraFieldLength,\n                value: extraFieldValue\n            };\n        }\n    },\n    /**\n     * Read the end of the Zip 64 central directory locator.\n     */\n    readBlockZip64EndOfCentralLocator: function() {\n        this.diskWithZip64CentralDirStart = this.reader.readInt(4);\n        this.relativeOffsetEndOfZip64CentralDir = this.reader.readInt(8);\n        this.disksCount = this.reader.readInt(4);\n        if (this.disksCount > 1) {\n            throw new Error(\"Multi-volumes zip are not supported\");\n        }\n    },\n    /**\n     * Read the local files, based on the offset read in the central part.\n     */\n    readLocalFiles: function() {\n        var i, file;\n        for (i = 0; i < this.files.length; i++) {\n            file = this.files[i];\n            this.reader.setIndex(file.localHeaderOffset);\n            this.checkSignature(sig.LOCAL_FILE_HEADER);\n            file.readLocalPart(this.reader);\n            file.handleUTF8();\n            file.processAttributes();\n        }\n    },\n    /**\n     * Read the central directory.\n     */\n    readCentralDir: function() {\n        var file;\n\n        this.reader.setIndex(this.centralDirOffset);\n        while (this.reader.readAndCheckSignature(sig.CENTRAL_FILE_HEADER)) {\n            file = new ZipEntry({\n                zip64: this.zip64\n            }, this.loadOptions);\n            file.readCentralPart(this.reader);\n            this.files.push(file);\n        }\n\n        if (this.centralDirRecords !== this.files.length) {\n            if (this.centralDirRecords !== 0 && this.files.length === 0) {\n                // We expected some records but couldn't find ANY.\n                // This is really suspicious, as if something went wrong.\n                throw new Error(\"Corrupted zip or bug: expected \" + this.centralDirRecords + \" records in central dir, got \" + this.files.length);\n            } else {\n                // We found some records but not all.\n                // Something is wrong but we got something for the user: no error here.\n                // console.warn(\"expected\", this.centralDirRecords, \"records in central dir, got\", this.files.length);\n            }\n        }\n    },\n    /**\n     * Read the end of central directory.\n     */\n    readEndOfCentral: function() {\n        var offset = this.reader.lastIndexOfSignature(sig.CENTRAL_DIRECTORY_END);\n        if (offset < 0) {\n            // Check if the content is a truncated zip or complete garbage.\n            // A \"LOCAL_FILE_HEADER\" is not required at the beginning (auto\n            // extractible zip for example) but it can give a good hint.\n            // If an ajax request was used without responseType, we will also\n            // get unreadable data.\n            var isGarbage = !this.isSignature(0, sig.LOCAL_FILE_HEADER);\n\n            if (isGarbage) {\n                throw new Error(\"Can't find end of central directory : is this a zip file ? \" +\n                                \"If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html\");\n            } else {\n                throw new Error(\"Corrupted zip: can't find end of central directory\");\n            }\n\n        }\n        this.reader.setIndex(offset);\n        var endOfCentralDirOffset = offset;\n        this.checkSignature(sig.CENTRAL_DIRECTORY_END);\n        this.readBlockEndOfCentral();\n\n\n        /* extract from the zip spec :\n            4)  If one of the fields in the end of central directory\n                record is too small to hold required data, the field\n                should be set to -1 (0xFFFF or 0xFFFFFFFF) and the\n                ZIP64 format record should be created.\n            5)  The end of central directory record and the\n                Zip64 end of central directory locator record must\n                reside on the same disk when splitting or spanning\n                an archive.\n         */\n        if (this.diskNumber === utils.MAX_VALUE_16BITS || this.diskWithCentralDirStart === utils.MAX_VALUE_16BITS || this.centralDirRecordsOnThisDisk === utils.MAX_VALUE_16BITS || this.centralDirRecords === utils.MAX_VALUE_16BITS || this.centralDirSize === utils.MAX_VALUE_32BITS || this.centralDirOffset === utils.MAX_VALUE_32BITS) {\n            this.zip64 = true;\n\n            /*\n            Warning : the zip64 extension is supported, but ONLY if the 64bits integer read from\n            the zip file can fit into a 32bits integer. This cannot be solved : JavaScript represents\n            all numbers as 64-bit double precision IEEE 754 floating point numbers.\n            So, we have 53bits for integers and bitwise operations treat everything as 32bits.\n            see https://developer.mozilla.org/en-US/docs/JavaScript/Reference/Operators/Bitwise_Operators\n            and http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-262.pdf section 8.5\n            */\n\n            // should look for a zip64 EOCD locator\n            offset = this.reader.lastIndexOfSignature(sig.ZIP64_CENTRAL_DIRECTORY_LOCATOR);\n            if (offset < 0) {\n                throw new Error(\"Corrupted zip: can't find the ZIP64 end of central directory locator\");\n            }\n            this.reader.setIndex(offset);\n            this.checkSignature(sig.ZIP64_CENTRAL_DIRECTORY_LOCATOR);\n            this.readBlockZip64EndOfCentralLocator();\n\n            // now the zip64 EOCD record\n            if (!this.isSignature(this.relativeOffsetEndOfZip64CentralDir, sig.ZIP64_CENTRAL_DIRECTORY_END)) {\n                // console.warn(\"ZIP64 end of central directory not where expected.\");\n                this.relativeOffsetEndOfZip64CentralDir = this.reader.lastIndexOfSignature(sig.ZIP64_CENTRAL_DIRECTORY_END);\n                if (this.relativeOffsetEndOfZip64CentralDir < 0) {\n                    throw new Error(\"Corrupted zip: can't find the ZIP64 end of central directory\");\n                }\n            }\n            this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir);\n            this.checkSignature(sig.ZIP64_CENTRAL_DIRECTORY_END);\n            this.readBlockZip64EndOfCentral();\n        }\n\n        var expectedEndOfCentralDirOffset = this.centralDirOffset + this.centralDirSize;\n        if (this.zip64) {\n            expectedEndOfCentralDirOffset += 20; // end of central dir 64 locator\n            expectedEndOfCentralDirOffset += 12 /* should not include the leading 12 bytes */ + this.zip64EndOfCentralSize;\n        }\n\n        var extraBytes = endOfCentralDirOffset - expectedEndOfCentralDirOffset;\n\n        if (extraBytes > 0) {\n            // console.warn(extraBytes, \"extra bytes at beginning or within zipfile\");\n            if (this.isSignature(endOfCentralDirOffset, sig.CENTRAL_FILE_HEADER)) {\n                // The offsets seem wrong, but we have something at the specified offset.\n                // So… we keep it.\n            } else {\n                // the offset is wrong, update the \"zero\" of the reader\n                // this happens if data has been prepended (crx files for example)\n                this.reader.zero = extraBytes;\n            }\n        } else if (extraBytes < 0) {\n            throw new Error(\"Corrupted zip: missing \" + Math.abs(extraBytes) + \" bytes.\");\n        }\n    },\n    prepareReader: function(data) {\n        this.reader = readerFor(data);\n    },\n    /**\n     * Read a zip file and create ZipEntries.\n     * @param {String|ArrayBuffer|Uint8Array|Buffer} data the binary string representing a zip file.\n     */\n    load: function(data) {\n        this.prepareReader(data);\n        this.readEndOfCentral();\n        this.readCentralDir();\n        this.readLocalFiles();\n    }\n};\n// }}} end of ZipEntries\nmodule.exports = ZipEntries;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/zipEntries.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/zipEntry.js":
/*!********************************************!*\
  !*** ./node_modules/jszip/lib/zipEntry.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar readerFor = __webpack_require__(/*! ./reader/readerFor */ \"(ssr)/./node_modules/jszip/lib/reader/readerFor.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar CompressedObject = __webpack_require__(/*! ./compressedObject */ \"(ssr)/./node_modules/jszip/lib/compressedObject.js\");\nvar crc32fn = __webpack_require__(/*! ./crc32 */ \"(ssr)/./node_modules/jszip/lib/crc32.js\");\nvar utf8 = __webpack_require__(/*! ./utf8 */ \"(ssr)/./node_modules/jszip/lib/utf8.js\");\nvar compressions = __webpack_require__(/*! ./compressions */ \"(ssr)/./node_modules/jszip/lib/compressions.js\");\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\n\nvar MADE_BY_DOS = 0x00;\nvar MADE_BY_UNIX = 0x03;\n\n/**\n * Find a compression registered in JSZip.\n * @param {string} compressionMethod the method magic to find.\n * @return {Object|null} the JSZip compression object, null if none found.\n */\nvar findCompression = function(compressionMethod) {\n    for (var method in compressions) {\n        if (!Object.prototype.hasOwnProperty.call(compressions, method)) {\n            continue;\n        }\n        if (compressions[method].magic === compressionMethod) {\n            return compressions[method];\n        }\n    }\n    return null;\n};\n\n// class ZipEntry {{{\n/**\n * An entry in the zip file.\n * @constructor\n * @param {Object} options Options of the current file.\n * @param {Object} loadOptions Options for loading the stream.\n */\nfunction ZipEntry(options, loadOptions) {\n    this.options = options;\n    this.loadOptions = loadOptions;\n}\nZipEntry.prototype = {\n    /**\n     * say if the file is encrypted.\n     * @return {boolean} true if the file is encrypted, false otherwise.\n     */\n    isEncrypted: function() {\n        // bit 1 is set\n        return (this.bitFlag & 0x0001) === 0x0001;\n    },\n    /**\n     * say if the file has utf-8 filename/comment.\n     * @return {boolean} true if the filename/comment is in utf-8, false otherwise.\n     */\n    useUTF8: function() {\n        // bit 11 is set\n        return (this.bitFlag & 0x0800) === 0x0800;\n    },\n    /**\n     * Read the local part of a zip file and add the info in this object.\n     * @param {DataReader} reader the reader to use.\n     */\n    readLocalPart: function(reader) {\n        var compression, localExtraFieldsLength;\n\n        // we already know everything from the central dir !\n        // If the central dir data are false, we are doomed.\n        // On the bright side, the local part is scary  : zip64, data descriptors, both, etc.\n        // The less data we get here, the more reliable this should be.\n        // Let's skip the whole header and dash to the data !\n        reader.skip(22);\n        // in some zip created on windows, the filename stored in the central dir contains \\ instead of /.\n        // Strangely, the filename here is OK.\n        // I would love to treat these zip files as corrupted (see http://www.info-zip.org/FAQ.html#backslashes\n        // or APPNOTE#********, \"All slashes MUST be forward slashes '/'\") but there are a lot of bad zip generators...\n        // Search \"unzip mismatching \"local\" filename continuing with \"central\" filename version\" on\n        // the internet.\n        //\n        // I think I see the logic here : the central directory is used to display\n        // content and the local directory is used to extract the files. Mixing / and \\\n        // may be used to display \\ to windows users and use / when extracting the files.\n        // Unfortunately, this lead also to some issues : http://seclists.org/fulldisclosure/2009/Sep/394\n        this.fileNameLength = reader.readInt(2);\n        localExtraFieldsLength = reader.readInt(2); // can't be sure this will be the same as the central dir\n        // the fileName is stored as binary data, the handleUTF8 method will take care of the encoding.\n        this.fileName = reader.readData(this.fileNameLength);\n        reader.skip(localExtraFieldsLength);\n\n        if (this.compressedSize === -1 || this.uncompressedSize === -1) {\n            throw new Error(\"Bug or corrupted zip : didn't get enough information from the central directory \" + \"(compressedSize === -1 || uncompressedSize === -1)\");\n        }\n\n        compression = findCompression(this.compressionMethod);\n        if (compression === null) { // no compression found\n            throw new Error(\"Corrupted zip : compression \" + utils.pretty(this.compressionMethod) + \" unknown (inner file : \" + utils.transformTo(\"string\", this.fileName) + \")\");\n        }\n        this.decompressed = new CompressedObject(this.compressedSize, this.uncompressedSize, this.crc32, compression, reader.readData(this.compressedSize));\n    },\n\n    /**\n     * Read the central part of a zip file and add the info in this object.\n     * @param {DataReader} reader the reader to use.\n     */\n    readCentralPart: function(reader) {\n        this.versionMadeBy = reader.readInt(2);\n        reader.skip(2);\n        // this.versionNeeded = reader.readInt(2);\n        this.bitFlag = reader.readInt(2);\n        this.compressionMethod = reader.readString(2);\n        this.date = reader.readDate();\n        this.crc32 = reader.readInt(4);\n        this.compressedSize = reader.readInt(4);\n        this.uncompressedSize = reader.readInt(4);\n        var fileNameLength = reader.readInt(2);\n        this.extraFieldsLength = reader.readInt(2);\n        this.fileCommentLength = reader.readInt(2);\n        this.diskNumberStart = reader.readInt(2);\n        this.internalFileAttributes = reader.readInt(2);\n        this.externalFileAttributes = reader.readInt(4);\n        this.localHeaderOffset = reader.readInt(4);\n\n        if (this.isEncrypted()) {\n            throw new Error(\"Encrypted zip are not supported\");\n        }\n\n        // will be read in the local part, see the comments there\n        reader.skip(fileNameLength);\n        this.readExtraFields(reader);\n        this.parseZIP64ExtraField(reader);\n        this.fileComment = reader.readData(this.fileCommentLength);\n    },\n\n    /**\n     * Parse the external file attributes and get the unix/dos permissions.\n     */\n    processAttributes: function () {\n        this.unixPermissions = null;\n        this.dosPermissions = null;\n        var madeBy = this.versionMadeBy >> 8;\n\n        // Check if we have the DOS directory flag set.\n        // We look for it in the DOS and UNIX permissions\n        // but some unknown platform could set it as a compatibility flag.\n        this.dir = this.externalFileAttributes & 0x0010 ? true : false;\n\n        if(madeBy === MADE_BY_DOS) {\n            // first 6 bits (0 to 5)\n            this.dosPermissions = this.externalFileAttributes & 0x3F;\n        }\n\n        if(madeBy === MADE_BY_UNIX) {\n            this.unixPermissions = (this.externalFileAttributes >> 16) & 0xFFFF;\n            // the octal permissions are in (this.unixPermissions & 0x01FF).toString(8);\n        }\n\n        // fail safe : if the name ends with a / it probably means a folder\n        if (!this.dir && this.fileNameStr.slice(-1) === \"/\") {\n            this.dir = true;\n        }\n    },\n\n    /**\n     * Parse the ZIP64 extra field and merge the info in the current ZipEntry.\n     * @param {DataReader} reader the reader to use.\n     */\n    parseZIP64ExtraField: function() {\n        if (!this.extraFields[0x0001]) {\n            return;\n        }\n\n        // should be something, preparing the extra reader\n        var extraReader = readerFor(this.extraFields[0x0001].value);\n\n        // I really hope that these 64bits integer can fit in 32 bits integer, because js\n        // won't let us have more.\n        if (this.uncompressedSize === utils.MAX_VALUE_32BITS) {\n            this.uncompressedSize = extraReader.readInt(8);\n        }\n        if (this.compressedSize === utils.MAX_VALUE_32BITS) {\n            this.compressedSize = extraReader.readInt(8);\n        }\n        if (this.localHeaderOffset === utils.MAX_VALUE_32BITS) {\n            this.localHeaderOffset = extraReader.readInt(8);\n        }\n        if (this.diskNumberStart === utils.MAX_VALUE_32BITS) {\n            this.diskNumberStart = extraReader.readInt(4);\n        }\n    },\n    /**\n     * Read the central part of a zip file and add the info in this object.\n     * @param {DataReader} reader the reader to use.\n     */\n    readExtraFields: function(reader) {\n        var end = reader.index + this.extraFieldsLength,\n            extraFieldId,\n            extraFieldLength,\n            extraFieldValue;\n\n        if (!this.extraFields) {\n            this.extraFields = {};\n        }\n\n        while (reader.index + 4 < end) {\n            extraFieldId = reader.readInt(2);\n            extraFieldLength = reader.readInt(2);\n            extraFieldValue = reader.readData(extraFieldLength);\n\n            this.extraFields[extraFieldId] = {\n                id: extraFieldId,\n                length: extraFieldLength,\n                value: extraFieldValue\n            };\n        }\n\n        reader.setIndex(end);\n    },\n    /**\n     * Apply an UTF8 transformation if needed.\n     */\n    handleUTF8: function() {\n        var decodeParamType = support.uint8array ? \"uint8array\" : \"array\";\n        if (this.useUTF8()) {\n            this.fileNameStr = utf8.utf8decode(this.fileName);\n            this.fileCommentStr = utf8.utf8decode(this.fileComment);\n        } else {\n            var upath = this.findExtraFieldUnicodePath();\n            if (upath !== null) {\n                this.fileNameStr = upath;\n            } else {\n                // ASCII text or unsupported code page\n                var fileNameByteArray =  utils.transformTo(decodeParamType, this.fileName);\n                this.fileNameStr = this.loadOptions.decodeFileName(fileNameByteArray);\n            }\n\n            var ucomment = this.findExtraFieldUnicodeComment();\n            if (ucomment !== null) {\n                this.fileCommentStr = ucomment;\n            } else {\n                // ASCII text or unsupported code page\n                var commentByteArray =  utils.transformTo(decodeParamType, this.fileComment);\n                this.fileCommentStr = this.loadOptions.decodeFileName(commentByteArray);\n            }\n        }\n    },\n\n    /**\n     * Find the unicode path declared in the extra field, if any.\n     * @return {String} the unicode path, null otherwise.\n     */\n    findExtraFieldUnicodePath: function() {\n        var upathField = this.extraFields[0x7075];\n        if (upathField) {\n            var extraReader = readerFor(upathField.value);\n\n            // wrong version\n            if (extraReader.readInt(1) !== 1) {\n                return null;\n            }\n\n            // the crc of the filename changed, this field is out of date.\n            if (crc32fn(this.fileName) !== extraReader.readInt(4)) {\n                return null;\n            }\n\n            return utf8.utf8decode(extraReader.readData(upathField.length - 5));\n        }\n        return null;\n    },\n\n    /**\n     * Find the unicode comment declared in the extra field, if any.\n     * @return {String} the unicode comment, null otherwise.\n     */\n    findExtraFieldUnicodeComment: function() {\n        var ucommentField = this.extraFields[0x6375];\n        if (ucommentField) {\n            var extraReader = readerFor(ucommentField.value);\n\n            // wrong version\n            if (extraReader.readInt(1) !== 1) {\n                return null;\n            }\n\n            // the crc of the comment changed, this field is out of date.\n            if (crc32fn(this.fileComment) !== extraReader.readInt(4)) {\n                return null;\n            }\n\n            return utf8.utf8decode(extraReader.readData(ucommentField.length - 5));\n        }\n        return null;\n    }\n};\nmodule.exports = ZipEntry;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/zipEntry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/zipObject.js":
/*!*********************************************!*\
  !*** ./node_modules/jszip/lib/zipObject.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar StreamHelper = __webpack_require__(/*! ./stream/StreamHelper */ \"(ssr)/./node_modules/jszip/lib/stream/StreamHelper.js\");\nvar DataWorker = __webpack_require__(/*! ./stream/DataWorker */ \"(ssr)/./node_modules/jszip/lib/stream/DataWorker.js\");\nvar utf8 = __webpack_require__(/*! ./utf8 */ \"(ssr)/./node_modules/jszip/lib/utf8.js\");\nvar CompressedObject = __webpack_require__(/*! ./compressedObject */ \"(ssr)/./node_modules/jszip/lib/compressedObject.js\");\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\n\n/**\n * A simple object representing a file in the zip file.\n * @constructor\n * @param {string} name the name of the file\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the data\n * @param {Object} options the options of the file\n */\nvar ZipObject = function(name, data, options) {\n    this.name = name;\n    this.dir = options.dir;\n    this.date = options.date;\n    this.comment = options.comment;\n    this.unixPermissions = options.unixPermissions;\n    this.dosPermissions = options.dosPermissions;\n\n    this._data = data;\n    this._dataBinary = options.binary;\n    // keep only the compression\n    this.options = {\n        compression : options.compression,\n        compressionOptions : options.compressionOptions\n    };\n};\n\nZipObject.prototype = {\n    /**\n     * Create an internal stream for the content of this object.\n     * @param {String} type the type of each chunk.\n     * @return StreamHelper the stream.\n     */\n    internalStream: function (type) {\n        var result = null, outputType = \"string\";\n        try {\n            if (!type) {\n                throw new Error(\"No output type specified.\");\n            }\n            outputType = type.toLowerCase();\n            var askUnicodeString = outputType === \"string\" || outputType === \"text\";\n            if (outputType === \"binarystring\" || outputType === \"text\") {\n                outputType = \"string\";\n            }\n            result = this._decompressWorker();\n\n            var isUnicodeString = !this._dataBinary;\n\n            if (isUnicodeString && !askUnicodeString) {\n                result = result.pipe(new utf8.Utf8EncodeWorker());\n            }\n            if (!isUnicodeString && askUnicodeString) {\n                result = result.pipe(new utf8.Utf8DecodeWorker());\n            }\n        } catch (e) {\n            result = new GenericWorker(\"error\");\n            result.error(e);\n        }\n\n        return new StreamHelper(result, outputType, \"\");\n    },\n\n    /**\n     * Prepare the content in the asked type.\n     * @param {String} type the type of the result.\n     * @param {Function} onUpdate a function to call on each internal update.\n     * @return Promise the promise of the result.\n     */\n    async: function (type, onUpdate) {\n        return this.internalStream(type).accumulate(onUpdate);\n    },\n\n    /**\n     * Prepare the content as a nodejs stream.\n     * @param {String} type the type of each chunk.\n     * @param {Function} onUpdate a function to call on each internal update.\n     * @return Stream the stream.\n     */\n    nodeStream: function (type, onUpdate) {\n        return this.internalStream(type || \"nodebuffer\").toNodejsStream(onUpdate);\n    },\n\n    /**\n     * Return a worker for the compressed content.\n     * @private\n     * @param {Object} compression the compression object to use.\n     * @param {Object} compressionOptions the options to use when compressing.\n     * @return Worker the worker.\n     */\n    _compressWorker: function (compression, compressionOptions) {\n        if (\n            this._data instanceof CompressedObject &&\n            this._data.compression.magic === compression.magic\n        ) {\n            return this._data.getCompressedWorker();\n        } else {\n            var result = this._decompressWorker();\n            if(!this._dataBinary) {\n                result = result.pipe(new utf8.Utf8EncodeWorker());\n            }\n            return CompressedObject.createWorkerFrom(result, compression, compressionOptions);\n        }\n    },\n    /**\n     * Return a worker for the decompressed content.\n     * @private\n     * @return Worker the worker.\n     */\n    _decompressWorker : function () {\n        if (this._data instanceof CompressedObject) {\n            return this._data.getContentWorker();\n        } else if (this._data instanceof GenericWorker) {\n            return this._data;\n        } else {\n            return new DataWorker(this._data);\n        }\n    }\n};\n\nvar removedMethods = [\"asText\", \"asBinary\", \"asNodeBuffer\", \"asUint8Array\", \"asArrayBuffer\"];\nvar removedFn = function () {\n    throw new Error(\"This method has been removed in JSZip 3.0, please check the upgrade guide.\");\n};\n\nfor(var i = 0; i < removedMethods.length; i++) {\n    ZipObject.prototype[removedMethods[i]] = removedFn;\n}\nmodule.exports = ZipObject;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/zipObject.js\n");

/***/ })

};
;