"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/JournalApp.tsx":
/*!***************************************!*\
  !*** ./src/components/JournalApp.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JournalApp: () => (/* binding */ JournalApp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _TextEditor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TextEditor */ \"(app-pages-browser)/./src/components/TextEditor.tsx\");\n/* harmony import */ var _TagManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TagManager */ \"(app-pages-browser)/./src/components/TagManager.tsx\");\n/* harmony import */ var _FileUpload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FileUpload */ \"(app-pages-browser)/./src/components/FileUpload.tsx\");\n/* harmony import */ var _Settings__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Settings */ \"(app-pages-browser)/./src/components/Settings.tsx\");\n/* harmony import */ var _lib_localStorage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/localStorage */ \"(app-pages-browser)/./src/lib/localStorage.ts\");\n/* harmony import */ var _lib_testData__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/testData */ \"(app-pages-browser)/./src/lib/testData.ts\");\n/* __next_internal_client_entry_do_not_use__ JournalApp auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction JournalApp(param) {\n    let { className = '' } = param;\n    _s();\n    const [currentEntry, setCurrentEntry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [entries, setEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUpload, setShowUpload] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Start closed on mobile\n    const [zoom, setZoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const storage = (0,_lib_localStorage__WEBPACK_IMPORTED_MODULE_6__.getLocalStorage)();\n    // Update available tags from all entries\n    const updateAvailableTags = (entriesList)=>{\n        const allTags = new Set();\n        entriesList.forEach((entry)=>{\n            entry.tags.forEach((tag)=>allTags.add(tag));\n        });\n        storage.cacheTags([\n            ...allTags\n        ]);\n    };\n    // Detect mobile and load cached entries on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"JournalApp.useEffect\": ()=>{\n            const checkMobile = {\n                \"JournalApp.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                    setSidebarOpen(window.innerWidth >= 768); // Open sidebar on desktop by default\n                }\n            }[\"JournalApp.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            const cached = storage.getCachedEntries();\n            if (cached) {\n                setEntries(cached.entries);\n                updateAvailableTags(cached.entries);\n            }\n            return ({\n                \"JournalApp.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"JournalApp.useEffect\"];\n        }\n    }[\"JournalApp.useEffect\"], []);\n    // Filter entries based on search and tags\n    const filteredEntries = entries.filter((entry)=>{\n        const matchesSearch = !searchQuery || entry.title.toLowerCase().includes(searchQuery.toLowerCase()) || entry.content.toLowerCase().includes(searchQuery.toLowerCase()) || entry.tags.some((tag)=>tag.toLowerCase().includes(searchQuery.toLowerCase()));\n        const matchesTags = selectedTags.length === 0 || selectedTags.every((tag)=>entry.tags.includes(tag));\n        return matchesSearch && matchesTags;\n    });\n    const createNewEntry = ()=>{\n        console.log('createNewEntry clicked!');\n        const newEntry = {\n            id: \"entry_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n            title: '',\n            content: '',\n            tags: [],\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n            word_count: 0\n        };\n        setCurrentEntry(newEntry);\n        setEntries((prev)=>[\n                newEntry,\n                ...prev\n            ]);\n        storage.cacheEntry(newEntry);\n        // Close sidebar on mobile when creating new entry\n        if (isMobile) {\n            setSidebarOpen(false);\n        }\n    };\n    const saveEntry = (data)=>{\n        if (!currentEntry) return;\n        const updatedEntry = {\n            ...currentEntry,\n            title: data.title || 'Untitled Entry',\n            content: data.content,\n            tags: data.tags,\n            updated_at: new Date().toISOString(),\n            word_count: data.content.trim().split(/\\s+/).filter((word)=>word.length > 0).length\n        };\n        setCurrentEntry(updatedEntry);\n        const newEntries = entries.map((entry)=>entry.id === updatedEntry.id ? updatedEntry : entry);\n        setEntries(newEntries);\n        storage.cacheEntry(updatedEntry);\n        // Update available tags from all entries\n        updateAvailableTags(newEntries);\n    };\n    const selectEntry = (entry)=>{\n        setCurrentEntry(entry);\n        // Close sidebar on mobile when selecting entry\n        if (isMobile) {\n            setSidebarOpen(false);\n        }\n    };\n    const deleteEntry = (entryId)=>{\n        setEntries((prev)=>prev.filter((entry)=>entry.id !== entryId));\n        storage.removeCachedEntry(entryId);\n        if ((currentEntry === null || currentEntry === void 0 ? void 0 : currentEntry.id) === entryId) {\n            setCurrentEntry(null);\n        }\n    };\n    const handleSearch = (query, tags)=>{\n        setSearchQuery(query);\n        setSelectedTags(tags);\n    };\n    const handleFileImport = (file)=>{\n        const newEntry = {\n            id: crypto.randomUUID(),\n            title: file.title,\n            content: file.content,\n            tags: [],\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n            word_count: file.content.split(/\\s+/).filter((word)=>word.length > 0).length\n        };\n        const newEntries = [\n            newEntry,\n            ...entries\n        ];\n        setEntries(newEntries);\n        storage.cacheEntry(newEntry);\n        setCurrentEntry(newEntry);\n        setShowUpload(false);\n        updateAvailableTags(newEntries);\n    };\n    const adjustZoom = (delta)=>{\n        const newZoom = Math.max(50, Math.min(200, zoom + delta));\n        setZoom(newZoom);\n    };\n    const loadSampleEntries = ()=>{\n        const sampleEntries = (0,_lib_testData__WEBPACK_IMPORTED_MODULE_7__.loadSampleData)();\n        if (sampleEntries.length > 0) {\n            setEntries(sampleEntries);\n            updateAvailableTags(sampleEntries);\n        }\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', {\n            month: 'short',\n            day: 'numeric',\n            year: 'numeric'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-50 dark:bg-gray-900 \".concat(className),\n        children: [\n            isMobile && sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-40\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 194,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n        \".concat(sidebarOpen ? isMobile ? 'w-80' : 'w-80' : 'w-0', \"\\n        \").concat(isMobile ? 'fixed left-0 top-0 h-full z-50' : 'relative', \"\\n        transition-all duration-300 overflow-hidden\\n        bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700\\n        flex flex-col\\n      \"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 md:p-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3 md:mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-lg md:text-xl font-bold text-gray-900 dark:text-gray-100\",\n                                        children: \"AllJournal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 13\n                                    }, this),\n                                    !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>adjustZoom(-10),\n                                                className: \"p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300\",\n                                                title: \"Zoom Out\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                children: [\n                                                    zoom,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>adjustZoom(10),\n                                                className: \"p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300\",\n                                                title: \"Zoom In\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: createNewEntry,\n                                        className: \"flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm md:text-base\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: isMobile ? 'New' : 'New Entry'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowUpload(true),\n                                        className: \"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                        title: \"Upload Files\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowSettings(true),\n                                        className: \"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                        title: \"Settings\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 md:p-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TagManager__WEBPACK_IMPORTED_MODULE_3__.TagManager, {\n                            selectedTags: selectedTags,\n                            onTagsChange: setSelectedTags,\n                            onSearch: handleSearch\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 md:p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                        children: [\n                                            \"Entries (\",\n                                            filteredEntries.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        filteredEntries.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                onClick: ()=>selectEntry(entry),\n                                                className: \"\\n                    p-3 rounded-lg cursor-pointer transition-colors\\n                    \".concat((currentEntry === null || currentEntry === void 0 ? void 0 : currentEntry.id) === entry.id ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' : 'hover:bg-gray-50 dark:hover:bg-gray-700', \"\\n                  \"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 dark:text-gray-100 truncate text-sm md:text-base\",\n                                                        children: entry.title || 'Untitled Entry'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs md:text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2\",\n                                                        children: entry.content || 'No content'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mt-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: formatDate(entry.updated_at)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: [\n                                                                    entry.word_count,\n                                                                    \" words\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    entry.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mt-2\",\n                                                        children: [\n                                                            entry.tags.slice(0, isMobile ? 2 : 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs px-2 py-1 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded\",\n                                                                    children: [\n                                                                        \"#\",\n                                                                        tag\n                                                                    ]\n                                                                }, tag, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 25\n                                                                }, this)),\n                                                            entry.tags.length > (isMobile ? 2 : 3) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: [\n                                                                    \"+\",\n                                                                    entry.tags.length - (isMobile ? 2 : 3)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, entry.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this)),\n                                        filteredEntries.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-12 h-12 mx-auto text-gray-400 mb-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 dark:text-gray-400\",\n                                                    children: entries.length === 0 ? 'No entries yet' : 'No entries match your search'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 19\n                                                }, this),\n                                                entries.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: createNewEntry,\n                                                    className: \"mt-2 text-blue-600 dark:text-blue-400 hover:underline\",\n                                                    children: \"Create your first entry\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col \".concat(isMobile && sidebarOpen ? 'pointer-events-none' : ''),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-3 md:p-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                        className: \"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 13\n                                    }, this),\n                                    isMobile && currentEntry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-medium text-gray-900 dark:text-gray-100 truncate\",\n                                        children: currentEntry.title || 'Untitled Entry'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, this),\n                            currentEntry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 md:space-x-4\",\n                                children: [\n                                    !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400 hidden sm:block\",\n                                        children: [\n                                            \"Last saved: \",\n                                            formatDate(currentEntry.updated_at)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>deleteEntry(currentEntry.id),\n                                        className: \"text-sm text-red-600 dark:text-red-400 hover:underline\",\n                                        children: isMobile ? 'Delete' : 'Delete Entry'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-2 md:p-0\",\n                        style: {\n                            zoom: \"\".concat(zoom, \"%\")\n                        },\n                        children: currentEntry ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TextEditor__WEBPACK_IMPORTED_MODULE_2__.TextEditor, {\n                            entryId: currentEntry.id,\n                            initialTitle: currentEntry.title,\n                            initialContent: currentEntry.content,\n                            initialTags: currentEntry.tags,\n                            onSave: saveEntry,\n                            onAutoSave: saveEntry,\n                            className: \"h-full\"\n                        }, currentEntry.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-full p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center max-w-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-12 h-12 md:w-16 md:h-16 mx-auto text-gray-400 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg md:text-xl font-medium text-gray-900 dark:text-gray-100 mb-2\",\n                                        children: \"Welcome to AllJournal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm md:text-base text-gray-600 dark:text-gray-400 mb-6\",\n                                        children: isMobile ? \"Tap the menu to browse entries or create a new one to start writing.\" : \"Select an entry from the sidebar or create a new one to start writing.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: createNewEntry,\n                                                className: \"flex items-center justify-center space-x-2 w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Create New Entry\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 19\n                                            }, this),\n                                            isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSidebarOpen(true),\n                                                className: \"flex items-center justify-center space-x-2 w-full px-4 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Browse Entries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 21\n                                            }, this),\n                                            entries.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: loadSampleEntries,\n                                                className: \"flex items-center justify-center space-x-2 w-full px-4 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Load Sample Entries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 351,\n                columnNumber: 7\n            }, this),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Settings__WEBPACK_IMPORTED_MODULE_5__.Settings, {\n                isOpen: showSettings,\n                onClose: ()=>setShowSettings(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 450,\n                columnNumber: 9\n            }, this),\n            showUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-gray-100\",\n                                    children: \"Import Files\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowUpload(false),\n                                    className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 459,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FileUpload__WEBPACK_IMPORTED_MODULE_4__.FileUpload, {\n                                onFilesProcessed: ()=>{},\n                                onImport: handleFileImport\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                    lineNumber: 458,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 457,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n        lineNumber: 191,\n        columnNumber: 5\n    }, this);\n}\n_s(JournalApp, \"jQW7sy2PZD0LrknvYp9gYrVxNR4=\");\n_c = JournalApp;\nvar _c;\n$RefreshReg$(_c, \"JournalApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/JournalApp.tsx\n"));

/***/ })

});