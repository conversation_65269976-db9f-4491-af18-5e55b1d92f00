[{"name":"hot-reloader","duration":82,"timestamp":126726149604,"id":3,"tags":{"version":"15.3.5"},"startTime":1751853294361,"traceId":"22e70f52cccb8652"},{"name":"setup-dev-bundler","duration":461359,"timestamp":126726009179,"id":2,"parentId":1,"tags":{},"startTime":1751853294221,"traceId":"22e70f52cccb8652"},{"name":"run-instrumentation-hook","duration":15,"timestamp":126726498024,"id":4,"parentId":1,"tags":{},"startTime":1751853294710,"traceId":"22e70f52cccb8652"},{"name":"start-dev-server","duration":813496,"timestamp":126725695681,"id":1,"tags":{"cpus":"28","platform":"win32","memory.freeMem":"20128591872","memory.totalMem":"34190999552","memory.heapSizeLimit":"17145266176","memory.rss":"196812800","memory.heapTotal":"101851136","memory.heapUsed":"68589176"},"startTime":1751853293907,"traceId":"22e70f52cccb8652"},{"name":"compile-path","duration":1284905,"timestamp":126752566378,"id":7,"tags":{"trigger":"/"},"startTime":1751853320778,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":1285653,"timestamp":126752565928,"id":6,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853320778,"traceId":"22e70f52cccb8652"}]
[{"name":"ensure-page","duration":14054,"timestamp":126753853527,"id":8,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853322065,"traceId":"22e70f52cccb8652"},{"name":"handle-request","duration":1547674,"timestamp":126752563281,"id":5,"tags":{"url":"/"},"startTime":1751853320775,"traceId":"22e70f52cccb8652"},{"name":"memory-usage","duration":4,"timestamp":126754110999,"id":9,"parentId":5,"tags":{"url":"/","memory.rss":"703156224","memory.heapUsed":"93095488","memory.heapTotal":"131252224"},"startTime":1751853322323,"traceId":"22e70f52cccb8652"},{"name":"compile-path","duration":204241,"timestamp":126754267269,"id":12,"tags":{"trigger":"/favicon.ico"},"startTime":1751853322479,"traceId":"22e70f52cccb8652"}]
[{"name":"ensure-page","duration":14324,"timestamp":126754472865,"id":13,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751853322685,"traceId":"22e70f52cccb8652"},{"name":"handle-request","duration":464682,"timestamp":126754265949,"id":10,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico"},"startTime":1751853322478,"traceId":"22e70f52cccb8652"},{"name":"memory-usage","duration":3,"timestamp":126754730663,"id":14,"parentId":10,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico","memory.rss":"731631616","memory.heapUsed":"107609312","memory.heapTotal":"136253440"},"startTime":1751853322942,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":14815,"timestamp":126763961273,"id":16,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853332173,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":13815,"timestamp":126763977341,"id":17,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853332189,"traceId":"22e70f52cccb8652"},{"name":"handle-request","duration":80768,"timestamp":126763959822,"id":15,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1751853332172,"traceId":"22e70f52cccb8652"},{"name":"memory-usage","duration":2,"timestamp":126764040626,"id":18,"parentId":15,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"712859648","memory.heapUsed":"100035936","memory.heapTotal":"122396672"},"startTime":1751853332252,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":13578,"timestamp":126764044338,"id":20,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853332256,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":14864,"timestamp":126764058952,"id":21,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853332271,"traceId":"22e70f52cccb8652"},{"name":"handle-request","duration":38215,"timestamp":126764043615,"id":19,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1751853332255,"traceId":"22e70f52cccb8652"},{"name":"memory-usage","duration":2,"timestamp":126764081852,"id":22,"parentId":19,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"715968512","memory.heapUsed":"104538944","memory.heapTotal":"122396672"},"startTime":1751853332294,"traceId":"22e70f52cccb8652"},{"name":"client-hmr-latency","duration":38000,"timestamp":126763927929,"id":23,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1751853332342,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":14164,"timestamp":126777670930,"id":25,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853345883,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":12008,"timestamp":126777686051,"id":26,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853345898,"traceId":"22e70f52cccb8652"},{"name":"handle-request","duration":67976,"timestamp":126777670335,"id":24,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1751853345882,"traceId":"22e70f52cccb8652"},{"name":"memory-usage","duration":2,"timestamp":126777738334,"id":27,"parentId":24,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"735473664","memory.heapUsed":"120387184","memory.heapTotal":"135987200"},"startTime":1751853345950,"traceId":"22e70f52cccb8652"},{"name":"client-hmr-latency","duration":35000,"timestamp":126777652792,"id":28,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1751853345972,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":13976,"timestamp":126777765679,"id":30,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853345977,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":13438,"timestamp":126777780565,"id":31,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853345992,"traceId":"22e70f52cccb8652"},{"name":"handle-request","duration":37585,"timestamp":126777764635,"id":29,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1751853345976,"traceId":"22e70f52cccb8652"},{"name":"memory-usage","duration":2,"timestamp":126777802249,"id":32,"parentId":29,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"723066880","memory.heapUsed":"108392672","memory.heapTotal":"144719872"},"startTime":1751853346014,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":14714,"timestamp":126786735759,"id":34,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853354947,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":12320,"timestamp":126786751703,"id":35,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853354963,"traceId":"22e70f52cccb8652"},{"name":"handle-request","duration":75666,"timestamp":126786735024,"id":33,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1751853354947,"traceId":"22e70f52cccb8652"},{"name":"memory-usage","duration":1,"timestamp":126786810719,"id":36,"parentId":33,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"719577088","memory.heapUsed":"109183792","memory.heapTotal":"131391488"},"startTime":1751853355022,"traceId":"22e70f52cccb8652"},{"name":"client-hmr-latency","duration":47000,"timestamp":126786704931,"id":39,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1751853355044,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":14017,"timestamp":126786819732,"id":38,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853355031,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":12665,"timestamp":126786834945,"id":40,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853355047,"traceId":"22e70f52cccb8652"},{"name":"handle-request","duration":36962,"timestamp":126786818908,"id":37,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1751853355031,"traceId":"22e70f52cccb8652"},{"name":"memory-usage","duration":1,"timestamp":126786855895,"id":41,"parentId":37,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"723091456","memory.heapUsed":"114015896","memory.heapTotal":"131391488"},"startTime":1751853355068,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":22882,"timestamp":126831122804,"id":43,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853399334,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":14709,"timestamp":126831146696,"id":44,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853399358,"traceId":"22e70f52cccb8652"},{"name":"client-hmr-latency","duration":31000,"timestamp":126831081506,"id":45,"parentId":3,"tags":{"updatedModules":["[project]/src/lib/localStorage.ts"],"page":"/","isPageHidden":true},"startTime":1751853399503,"traceId":"22e70f52cccb8652"},{"name":"handle-request","duration":188380,"timestamp":126831121827,"id":42,"tags":{"url":"/"},"startTime":1751853399334,"traceId":"22e70f52cccb8652"},{"name":"memory-usage","duration":2,"timestamp":126831310242,"id":46,"parentId":42,"tags":{"url":"/","memory.rss":"749633536","memory.heapUsed":"132677648","memory.heapTotal":"154865664"},"startTime":1751853399522,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":14078,"timestamp":126831491156,"id":48,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751853399703,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":12496,"timestamp":126831506552,"id":49,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751853399718,"traceId":"22e70f52cccb8652"},{"name":"handle-request","duration":269530,"timestamp":126831490123,"id":47,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico"},"startTime":1751853399702,"traceId":"22e70f52cccb8652"},{"name":"memory-usage","duration":2,"timestamp":126831759692,"id":50,"parentId":47,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico","memory.rss":"750039040","memory.heapUsed":"124884392","memory.heapTotal":"151408640"},"startTime":1751853399971,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":24324,"timestamp":126843044445,"id":52,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853411256,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":13590,"timestamp":126843070509,"id":53,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853411282,"traceId":"22e70f52cccb8652"},{"name":"client-hmr-latency","duration":45000,"timestamp":126842986618,"id":54,"parentId":3,"tags":{"updatedModules":["[project]/src/lib/localStorage.ts"],"page":"/","isPageHidden":true},"startTime":1751853411348,"traceId":"22e70f52cccb8652"},{"name":"handle-request","duration":142156,"timestamp":126843043234,"id":51,"tags":{"url":"/"},"startTime":1751853411255,"traceId":"22e70f52cccb8652"},{"name":"memory-usage","duration":2,"timestamp":126843185430,"id":55,"parentId":51,"tags":{"url":"/","memory.rss":"771174400","memory.heapUsed":"156928568","memory.heapTotal":"183152640"},"startTime":1751853411397,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":13946,"timestamp":126843384372,"id":57,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751853411596,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":14133,"timestamp":126843399896,"id":58,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751853411612,"traceId":"22e70f52cccb8652"},{"name":"handle-request","duration":238228,"timestamp":126843383635,"id":56,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico"},"startTime":1751853411595,"traceId":"22e70f52cccb8652"},{"name":"memory-usage","duration":3,"timestamp":126843621909,"id":59,"parentId":56,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico","memory.rss":"782733312","memory.heapUsed":"149239728","memory.heapTotal":"185630720"},"startTime":1751853411834,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":22141,"timestamp":126856641428,"id":61,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853424854,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":13266,"timestamp":126856664626,"id":62,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853424877,"traceId":"22e70f52cccb8652"},{"name":"client-hmr-latency","duration":16000,"timestamp":126856613146,"id":63,"parentId":3,"tags":{"updatedModules":["[project]/src/lib/localStorage.ts"],"page":"/","isPageHidden":true},"startTime":1751853424975,"traceId":"22e70f52cccb8652"},{"name":"handle-request","duration":128335,"timestamp":126856640774,"id":60,"tags":{"url":"/"},"startTime":1751853424853,"traceId":"22e70f52cccb8652"},{"name":"memory-usage","duration":1,"timestamp":126856769131,"id":64,"parentId":60,"tags":{"url":"/","memory.rss":"803119104","memory.heapUsed":"172032928","memory.heapTotal":"198549504"},"startTime":1751853424981,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":13350,"timestamp":126856981452,"id":66,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751853425194,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":13499,"timestamp":126856995850,"id":67,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751853425208,"traceId":"22e70f52cccb8652"},{"name":"handle-request","duration":239862,"timestamp":126856980675,"id":65,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico"},"startTime":1751853425193,"traceId":"22e70f52cccb8652"},{"name":"memory-usage","duration":4,"timestamp":126857220574,"id":68,"parentId":65,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico","memory.rss":"801165312","memory.heapUsed":"151387432","memory.heapTotal":"198750208"},"startTime":1751853425433,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":24997,"timestamp":126869640527,"id":70,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853437853,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":14428,"timestamp":126869667011,"id":71,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853437879,"traceId":"22e70f52cccb8652"},{"name":"client-hmr-latency","duration":15000,"timestamp":126869610430,"id":72,"parentId":3,"tags":{"updatedModules":["[project]/src/lib/localStorage.ts"],"page":"/","isPageHidden":true},"startTime":1751853437986,"traceId":"22e70f52cccb8652"},{"name":"handle-request","duration":141382,"timestamp":126869639707,"id":69,"tags":{"url":"/"},"startTime":1751853437852,"traceId":"22e70f52cccb8652"},{"name":"memory-usage","duration":2,"timestamp":126869781113,"id":73,"parentId":69,"tags":{"url":"/","memory.rss":"790216704","memory.heapUsed":"178449120","memory.heapTotal":"206286848"},"startTime":1751853437993,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":14490,"timestamp":126869998075,"id":75,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751853438210,"traceId":"22e70f52cccb8652"},{"name":"ensure-page","duration":13079,"timestamp":126870014213,"id":76,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751853438226,"traceId":"22e70f52cccb8652"},{"name":"handle-request","duration":240163,"timestamp":126869996934,"id":74,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico"},"startTime":1751853438209,"traceId":"22e70f52cccb8652"},{"name":"memory-usage","duration":1,"timestamp":126870237117,"id":77,"parentId":74,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico","memory.rss":"803409920","memory.heapUsed":"174024800","memory.heapTotal":"204906496"},"startTime":1751853438449,"traceId":"22e70f52cccb8652"},{"name":"compile-path","duration":24259,"timestamp":126894535430,"id":80,"tags":{"trigger":"/"},"startTime":1751853462748,"traceId":"22e70f52cccb8652"}]
[{"name":"ensure-page","duration":14052,"timestamp":126894561590,"id":81,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853462774,"traceId":"22e70f52cccb8652"},{"name":"handle-request","duration":82190,"timestamp":126894534493,"id":78,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1751853462747,"traceId":"22e70f52cccb8652"},{"name":"memory-usage","duration":1,"timestamp":126894616704,"id":82,"parentId":78,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"826658816","memory.heapUsed":"192675096","memory.heapTotal":"207196160"},"startTime":1751853462829,"traceId":"22e70f52cccb8652"},{"name":"client-hmr-latency","duration":13000,"timestamp":126894512910,"id":83,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1751853462833,"traceId":"22e70f52cccb8652"},{"name":"compile-path","duration":23813,"timestamp":126906082383,"id":86,"tags":{"trigger":"/"},"startTime":1751853474295,"traceId":"22e70f52cccb8652"}]
[{"name":"hot-reloader","duration":81,"timestamp":127024637276,"id":3,"tags":{"version":"15.3.5"},"startTime":1751853592849,"traceId":"d7639ae2cdd6be28"},{"name":"setup-dev-bundler","duration":453391,"timestamp":127024500752,"id":2,"parentId":1,"tags":{},"startTime":1751853592713,"traceId":"d7639ae2cdd6be28"},{"name":"run-instrumentation-hook","duration":14,"timestamp":127024982259,"id":4,"parentId":1,"tags":{},"startTime":1751853593194,"traceId":"d7639ae2cdd6be28"},{"name":"start-dev-server","duration":809944,"timestamp":127024183400,"id":1,"tags":{"cpus":"28","platform":"win32","memory.freeMem":"19534761984","memory.totalMem":"34190999552","memory.heapSizeLimit":"17145266176","memory.rss":"195739648","memory.heapTotal":"101851136","memory.heapUsed":"68734168"},"startTime":1751853592396,"traceId":"d7639ae2cdd6be28"},{"name":"compile-path","duration":1222581,"timestamp":127033740487,"id":7,"tags":{"trigger":"/"},"startTime":1751853601953,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":1223371,"timestamp":127033740045,"id":6,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853601952,"traceId":"d7639ae2cdd6be28"}]
[{"name":"compile-path","duration":452355,"timestamp":127034965926,"id":9,"tags":{"trigger":"/_error"},"startTime":1751853603178,"traceId":"d7639ae2cdd6be28"}]
[{"name":"handle-request","duration":1864860,"timestamp":127033736303,"id":5,"tags":{"url":"/"},"startTime":1751853601948,"traceId":"d7639ae2cdd6be28"},{"name":"memory-usage","duration":5,"timestamp":127035601268,"id":10,"parentId":5,"tags":{"url":"/","memory.rss":"748978176","memory.heapUsed":"73306664","memory.heapTotal":"83017728"},"startTime":1751853603813,"traceId":"d7639ae2cdd6be28"},{"name":"navigation-to-hydration","duration":1977000,"timestamp":127033722639,"id":14,"parentId":3,"tags":{"pathname":"/","query":""},"startTime":1751853603913,"traceId":"d7639ae2cdd6be28"},{"name":"compile-path","duration":199871,"timestamp":127035684928,"id":13,"tags":{"trigger":"/favicon.ico"},"startTime":1751853603897,"traceId":"d7639ae2cdd6be28"}]
[{"name":"ensure-page","duration":13817,"timestamp":127035886855,"id":15,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751853604099,"traceId":"d7639ae2cdd6be28"},{"name":"handle-request","duration":218453,"timestamp":127035683570,"id":11,"tags":{"url":"/favicon.ico"},"startTime":1751853603896,"traceId":"d7639ae2cdd6be28"},{"name":"memory-usage","duration":1,"timestamp":127035902055,"id":16,"parentId":11,"tags":{"url":"/favicon.ico","memory.rss":"776691712","memory.heapUsed":"72989776","memory.heapTotal":"92192768"},"startTime":1751853604114,"traceId":"d7639ae2cdd6be28"},{"name":"compile-path","duration":17547,"timestamp":127060590398,"id":19,"tags":{"trigger":"/"},"startTime":1751853628803,"traceId":"d7639ae2cdd6be28"}]
[{"name":"ensure-page","duration":14122,"timestamp":127060609237,"id":20,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853628821,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":283000,"timestamp":127060292121,"id":21,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1751853628953,"traceId":"d7639ae2cdd6be28"},{"name":"handle-request","duration":256608,"timestamp":127060589446,"id":17,"tags":{"url":"/"},"startTime":1751853628802,"traceId":"d7639ae2cdd6be28"},{"name":"memory-usage","duration":3,"timestamp":127060846094,"id":22,"parentId":17,"tags":{"url":"/","memory.rss":"876863488","memory.heapUsed":"95594560","memory.heapTotal":"136073216"},"startTime":1751853629058,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":19101,"timestamp":127061122736,"id":24,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751853629335,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":13707,"timestamp":127061143539,"id":25,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751853629356,"traceId":"d7639ae2cdd6be28"},{"name":"handle-request","duration":288841,"timestamp":127061121542,"id":23,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico"},"startTime":1751853629334,"traceId":"d7639ae2cdd6be28"},{"name":"memory-usage","duration":2,"timestamp":127061410408,"id":26,"parentId":23,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico","memory.rss":"877617152","memory.heapUsed":"109265584","memory.heapTotal":"140025856"},"startTime":1751853629623,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":79000,"timestamp":127086431749,"id":27,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1751853654748,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":344000,"timestamp":127096149155,"id":28,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1751853664716,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":17113,"timestamp":127108232542,"id":30,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853676440,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":13760,"timestamp":127108250892,"id":31,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853676459,"traceId":"d7639ae2cdd6be28"},{"name":"handle-request","duration":61977,"timestamp":127108231550,"id":29,"tags":{"url":"/"},"startTime":1751853676439,"traceId":"d7639ae2cdd6be28"},{"name":"memory-usage","duration":3,"timestamp":127108293563,"id":32,"parentId":29,"tags":{"url":"/","memory.rss":"854069248","memory.heapUsed":"101257152","memory.heapTotal":"105095168"},"startTime":1751853676501,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":657,"timestamp":127113838874,"id":33,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1751853682047,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":127,"timestamp":127113839567,"id":34,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1751853682047,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":110,"timestamp":127113839997,"id":35,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1751853682048,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":80,"timestamp":127113840119,"id":36,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1751853682048,"traceId":"d7639ae2cdd6be28"},{"name":"compile-path","duration":301257,"timestamp":127113841921,"id":39,"tags":{"trigger":"/_not-found/page"},"startTime":1751853682050,"traceId":"d7639ae2cdd6be28"}]
[{"name":"ensure-page","duration":13914,"timestamp":127114143738,"id":40,"parentId":3,"tags":{"inputPage":"/_not-found/page"},"startTime":1751853682352,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":64000,"timestamp":127160578258,"id":41,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1751853728888,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":62000,"timestamp":127160578768,"id":42,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":false},"startTime":1751853728893,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":14686,"timestamp":127183829419,"id":44,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853752042,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":13916,"timestamp":127183845094,"id":45,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751853752057,"traceId":"d7639ae2cdd6be28"},{"name":"handle-request","duration":57170,"timestamp":127183828751,"id":43,"tags":{"url":"/"},"startTime":1751853752041,"traceId":"d7639ae2cdd6be28"},{"name":"memory-usage","duration":2,"timestamp":127183885949,"id":46,"parentId":43,"tags":{"url":"/","memory.rss":"877682688","memory.heapUsed":"104472728","memory.heapTotal":"107454464"},"startTime":1751853752098,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":14967,"timestamp":127184037093,"id":48,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751853752249,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":13435,"timestamp":127184052995,"id":49,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751853752265,"traceId":"d7639ae2cdd6be28"},{"name":"handle-request","duration":252768,"timestamp":127184035985,"id":47,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico"},"startTime":1751853752248,"traceId":"d7639ae2cdd6be28"},{"name":"memory-usage","duration":13,"timestamp":127184288881,"id":50,"parentId":47,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico","memory.rss":"876388352","memory.heapUsed":"100963088","memory.heapTotal":"108765184"},"startTime":1751853752501,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":51000,"timestamp":127255275872,"id":51,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1751853823561,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":47000,"timestamp":127255276948,"id":52,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1751853823561,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":65000,"timestamp":127321563333,"id":53,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":false},"startTime":1751853889868,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":67000,"timestamp":127321563466,"id":54,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751853889884,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":164,"timestamp":127327491327,"id":55,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1751853895703,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":70,"timestamp":127327491508,"id":56,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1751853895703,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":193,"timestamp":127327491776,"id":57,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1751853895703,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":85,"timestamp":127327491984,"id":58,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1751853895703,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":17395,"timestamp":127327492959,"id":60,"parentId":3,"tags":{"inputPage":"/_not-found/page"},"startTime":1751853895704,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":14333,"timestamp":127327510576,"id":61,"parentId":3,"tags":{"inputPage":"/_not-found/page"},"startTime":1751853895722,"traceId":"d7639ae2cdd6be28"},{"name":"handle-request","duration":51660,"timestamp":127327492327,"id":59,"tags":{"url":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1751853895704,"traceId":"d7639ae2cdd6be28"},{"name":"memory-usage","duration":2,"timestamp":127327544045,"id":62,"parentId":59,"tags":{"url":"/.well-known/appspecific/com.chrome.devtools.json","memory.rss":"881778688","memory.heapUsed":"102251440","memory.heapTotal":"109027328"},"startTime":1751853895756,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":64000,"timestamp":127335237644,"id":63,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":false},"startTime":1751853903542,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":64000,"timestamp":127335237941,"id":64,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751853903542,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":64000,"timestamp":127349798705,"id":65,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751853918103,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":65000,"timestamp":127349798770,"id":66,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":false},"startTime":1751853918103,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":63000,"timestamp":127363050050,"id":67,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":false},"startTime":1751853931354,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":65000,"timestamp":127363050854,"id":68,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751853931356,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":59000,"timestamp":127378399572,"id":69,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751853946699,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":59000,"timestamp":127378399603,"id":70,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751853946699,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":65000,"timestamp":127402652487,"id":71,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751853970956,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":65000,"timestamp":127402652525,"id":72,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751853970956,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":61000,"timestamp":127422025112,"id":73,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751853990325,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":62000,"timestamp":127422025179,"id":74,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751853990325,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":65000,"timestamp":127443244248,"id":75,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854011548,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":65000,"timestamp":127443244303,"id":76,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854011548,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":67000,"timestamp":127467301605,"id":77,"parentId":3,"tags":{"updatedModules":["[project]/src/components/TextEditor.tsx"],"page":"/","isPageHidden":true},"startTime":1751854035606,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":67000,"timestamp":127467301638,"id":78,"parentId":3,"tags":{"updatedModules":["[project]/src/components/TextEditor.tsx"],"page":"/","isPageHidden":true},"startTime":1751854035606,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":96000,"timestamp":127491991613,"id":79,"parentId":3,"tags":{"updatedModules":["[project]/src/components/TextEditor.tsx"],"page":"/","isPageHidden":true},"startTime":1751854060326,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":96000,"timestamp":127491990646,"id":80,"parentId":3,"tags":{"updatedModules":["[project]/src/components/TextEditor.tsx"],"page":"/","isPageHidden":true},"startTime":1751854060327,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":67000,"timestamp":127518052074,"id":81,"parentId":3,"tags":{"updatedModules":["[project]/src/components/TextEditor.tsx"],"page":"/","isPageHidden":true},"startTime":1751854086358,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":67000,"timestamp":127518052164,"id":82,"parentId":3,"tags":{"updatedModules":["[project]/src/components/TextEditor.tsx"],"page":"/","isPageHidden":true},"startTime":1751854086358,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":81000,"timestamp":127539345295,"id":83,"parentId":3,"tags":{"updatedModules":["[project]/src/components/TextEditor.tsx"],"page":"/","isPageHidden":true},"startTime":1751854107650,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":82000,"timestamp":127539345345,"id":84,"parentId":3,"tags":{"updatedModules":["[project]/src/components/TextEditor.tsx"],"page":"/","isPageHidden":true},"startTime":1751854107650,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":129000,"timestamp":127566241405,"id":85,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854134608,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":129000,"timestamp":127566241466,"id":86,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854134608,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":65000,"timestamp":127588989611,"id":87,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854157289,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":66000,"timestamp":127588989649,"id":88,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854157289,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":95000,"timestamp":127603007720,"id":89,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854171342,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":97000,"timestamp":127603007769,"id":90,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854171342,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":68000,"timestamp":127646281072,"id":91,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854214588,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":68000,"timestamp":127646281147,"id":92,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854214588,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":79000,"timestamp":127673475508,"id":93,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1751854241795,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":79000,"timestamp":127673475564,"id":94,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1751854241795,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":72000,"timestamp":127994832427,"id":95,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854563135,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":72000,"timestamp":127994832184,"id":96,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854563137,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":72000,"timestamp":128036096435,"id":97,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854604397,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":72000,"timestamp":128036097362,"id":98,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854604401,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":62000,"timestamp":128053655040,"id":99,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854621959,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":62000,"timestamp":128053655089,"id":100,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854621959,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":65000,"timestamp":128071552507,"id":101,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854639849,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":65000,"timestamp":128071552330,"id":102,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854639856,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":66000,"timestamp":128097901607,"id":103,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854666206,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":66000,"timestamp":128097900894,"id":104,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854666207,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":53000,"timestamp":128112696140,"id":105,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854680990,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":53000,"timestamp":128112696467,"id":106,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854680990,"traceId":"d7639ae2cdd6be28"},{"name":"compile-path","duration":29222,"timestamp":128146769136,"id":110,"tags":{"trigger":"/"},"startTime":1751854714980,"traceId":"d7639ae2cdd6be28"}]
[{"name":"ensure-page","duration":20662,"timestamp":128146780677,"id":111,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751854714992,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":19094,"timestamp":128146799571,"id":112,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751854715011,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":141000,"timestamp":128146681085,"id":114,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854715080,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":147000,"timestamp":128146680654,"id":115,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854715081,"traceId":"d7639ae2cdd6be28"},{"name":"handle-request","duration":103033,"timestamp":128146768101,"id":107,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1751854714979,"traceId":"d7639ae2cdd6be28"},{"name":"memory-usage","duration":2,"timestamp":128146871165,"id":116,"parentId":107,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"918290432","memory.heapUsed":"109917792","memory.heapTotal":"137871360"},"startTime":1751854715082,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":70023,"timestamp":128146802246,"id":113,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751854715014,"traceId":"d7639ae2cdd6be28"},{"name":"handle-request","duration":111541,"timestamp":128146768672,"id":108,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1751854714980,"traceId":"d7639ae2cdd6be28"},{"name":"memory-usage","duration":3,"timestamp":128146880253,"id":117,"parentId":108,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"918822912","memory.heapUsed":"112265656","memory.heapTotal":"137871360"},"startTime":1751854715092,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":69000,"timestamp":128205067035,"id":118,"parentId":3,"tags":{"updatedModules":["[project]/src/components/TextEditor.tsx"],"page":"/","isPageHidden":true},"startTime":1751854773359,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":69000,"timestamp":128205067110,"id":119,"parentId":3,"tags":{"updatedModules":["[project]/src/components/TextEditor.tsx"],"page":"/","isPageHidden":true},"startTime":1751854773361,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":53000,"timestamp":128224363028,"id":120,"parentId":3,"tags":{"updatedModules":["[project]/src/components/TextEditor.tsx"],"page":"/","isPageHidden":true},"startTime":1751854792655,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":53000,"timestamp":128224362726,"id":121,"parentId":3,"tags":{"updatedModules":["[project]/src/components/TextEditor.tsx"],"page":"/","isPageHidden":true},"startTime":1751854792657,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":66000,"timestamp":128243679970,"id":122,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854811982,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":66000,"timestamp":128243679677,"id":123,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854811984,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":26203,"timestamp":128256768508,"id":125,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751854824980,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":13975,"timestamp":128256796015,"id":126,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751854825007,"traceId":"d7639ae2cdd6be28"},{"name":"handle-request","duration":201662,"timestamp":128256767547,"id":124,"tags":{"url":"/"},"startTime":1751854824979,"traceId":"d7639ae2cdd6be28"},{"name":"memory-usage","duration":2,"timestamp":128256969246,"id":127,"parentId":124,"tags":{"url":"/","memory.rss":"938995712","memory.heapUsed":"131676160","memory.heapTotal":"164032512"},"startTime":1751854825181,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":71000,"timestamp":128270376554,"id":128,"parentId":3,"tags":{"updatedModules":["[project]/src/components/TextEditor.tsx"],"page":"/","isPageHidden":true},"startTime":1751854838669,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":68000,"timestamp":128270376618,"id":129,"parentId":3,"tags":{"updatedModules":["[project]/src/components/TextEditor.tsx"],"page":"/","isPageHidden":false},"startTime":1751854838669,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":71000,"timestamp":128270376599,"id":130,"parentId":3,"tags":{"updatedModules":["[project]/src/components/TextEditor.tsx"],"page":"/","isPageHidden":true},"startTime":1751854838673,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":68000,"timestamp":128290172295,"id":131,"parentId":3,"tags":{"updatedModules":["[project]/src/components/TextEditor.tsx"],"page":"/","isPageHidden":false},"startTime":1751854858438,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":70000,"timestamp":128290172385,"id":132,"parentId":3,"tags":{"updatedModules":["[project]/src/components/TextEditor.tsx"],"page":"/","isPageHidden":true},"startTime":1751854858438,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":55000,"timestamp":128310134481,"id":133,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":false},"startTime":1751854878404,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":57000,"timestamp":128310134600,"id":134,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854878404,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":246,"timestamp":128325174804,"id":135,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1751854893372,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":97,"timestamp":128325175078,"id":136,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1751854893373,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":119,"timestamp":128325175487,"id":137,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1751854893373,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":71,"timestamp":128325175619,"id":138,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1751854893373,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":18856,"timestamp":128325176198,"id":140,"parentId":3,"tags":{"inputPage":"/_not-found/page"},"startTime":1751854893374,"traceId":"d7639ae2cdd6be28"},{"name":"ensure-page","duration":15329,"timestamp":128325195287,"id":141,"parentId":3,"tags":{"inputPage":"/_not-found/page"},"startTime":1751854893393,"traceId":"d7639ae2cdd6be28"},{"name":"handle-request","duration":53378,"timestamp":128325175774,"id":139,"tags":{"url":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1751854893373,"traceId":"d7639ae2cdd6be28"},{"name":"memory-usage","duration":3,"timestamp":128325229175,"id":142,"parentId":139,"tags":{"url":"/.well-known/appspecific/com.chrome.devtools.json","memory.rss":"953376768","memory.heapUsed":"131564504","memory.heapTotal":"135720960"},"startTime":1751854893427,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":71000,"timestamp":128345123176,"id":143,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751854913392,"traceId":"d7639ae2cdd6be28"},{"name":"client-hmr-latency","duration":69000,"timestamp":128345123839,"id":144,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":false},"startTime":1751854913392,"traceId":"d7639ae2cdd6be28"},{"name":"compile-path","duration":39407,"timestamp":128362010442,"id":147,"tags":{"trigger":"/"},"startTime":1751854930222,"traceId":"d7639ae2cdd6be28"}]
[{"name":"hot-reloader","duration":87,"timestamp":128686232030,"id":3,"tags":{"version":"15.3.5"},"startTime":1751855254444,"traceId":"6e3a319291aa975b"},{"name":"setup-dev-bundler","duration":459048,"timestamp":128686086979,"id":2,"parentId":1,"tags":{},"startTime":1751855254299,"traceId":"6e3a319291aa975b"},{"name":"run-instrumentation-hook","duration":14,"timestamp":128686573228,"id":4,"parentId":1,"tags":{},"startTime":1751855254785,"traceId":"6e3a319291aa975b"},{"name":"start-dev-server","duration":820029,"timestamp":128685764913,"id":1,"tags":{"cpus":"28","platform":"win32","memory.freeMem":"19116470272","memory.totalMem":"34190999552","memory.heapSizeLimit":"17145266176","memory.rss":"196354048","memory.heapTotal":"101588992","memory.heapUsed":"68711632"},"startTime":1751855253977,"traceId":"6e3a319291aa975b"},{"name":"compile-path","duration":1255433,"timestamp":128700210665,"id":7,"tags":{"trigger":"/"},"startTime":1751855268423,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":1256197,"timestamp":128700210237,"id":6,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855268422,"traceId":"6e3a319291aa975b"}]
[{"name":"ensure-page","duration":14493,"timestamp":128701469008,"id":8,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855269681,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":1516585,"timestamp":128700206516,"id":5,"tags":{"url":"/"},"startTime":1751855268419,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":4,"timestamp":128701723149,"id":9,"parentId":5,"tags":{"url":"/","memory.rss":"675655680","memory.heapUsed":"92298376","memory.heapTotal":"129970176"},"startTime":1751855269935,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":16622,"timestamp":128707926066,"id":11,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855276138,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":15219,"timestamp":128707943925,"id":12,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855276156,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":62351,"timestamp":128707925236,"id":10,"tags":{"url":"/"},"startTime":1751855276137,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":2,"timestamp":128707987615,"id":13,"parentId":10,"tags":{"url":"/","memory.rss":"676921344","memory.heapUsed":"100662728","memory.heapTotal":"130232320"},"startTime":1751855276200,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":14958,"timestamp":128713275639,"id":15,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855281488,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":14477,"timestamp":128713292032,"id":16,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855281504,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":58672,"timestamp":128713274899,"id":14,"tags":{"url":"/"},"startTime":1751855281487,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":3,"timestamp":128713333603,"id":17,"parentId":14,"tags":{"url":"/","memory.rss":"659660800","memory.heapUsed":"89031648","memory.heapTotal":"96215040"},"startTime":1751855281546,"traceId":"6e3a319291aa975b"},{"name":"compile-path","duration":197724,"timestamp":128713520758,"id":20,"tags":{"trigger":"/favicon.ico"},"startTime":1751855281733,"traceId":"6e3a319291aa975b"}]
[{"name":"ensure-page","duration":13453,"timestamp":128713720023,"id":21,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751855281932,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":461103,"timestamp":128713519649,"id":18,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico"},"startTime":1751855281732,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":3,"timestamp":128713980779,"id":22,"parentId":18,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico","memory.rss":"701317120","memory.heapUsed":"97799952","memory.heapTotal":"106459136"},"startTime":1751855282193,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":15853,"timestamp":128715337405,"id":24,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855283549,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":16011,"timestamp":128715354336,"id":25,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855283566,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":63716,"timestamp":128715336710,"id":23,"tags":{"url":"/"},"startTime":1751855283549,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":4,"timestamp":128715400468,"id":26,"parentId":23,"tags":{"url":"/","memory.rss":"703946752","memory.heapUsed":"98058072","memory.heapTotal":"115568640"},"startTime":1751855283612,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":15478,"timestamp":128715612400,"id":28,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751855283824,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":12902,"timestamp":128715629136,"id":29,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751855283841,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":246126,"timestamp":128715611572,"id":27,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico"},"startTime":1751855283824,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":3,"timestamp":128715857730,"id":30,"parentId":27,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico","memory.rss":"707981312","memory.heapUsed":"97458384","memory.heapTotal":"115568640"},"startTime":1751855284070,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":15455,"timestamp":128725709435,"id":32,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855293921,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":14467,"timestamp":128725725893,"id":33,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855293938,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":61257,"timestamp":128725708825,"id":31,"tags":{"url":"/"},"startTime":1751855293921,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":4,"timestamp":128725770111,"id":34,"parentId":31,"tags":{"url":"/","memory.rss":"712802304","memory.heapUsed":"97041528","memory.heapTotal":"102199296"},"startTime":1751855293982,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":29411,"timestamp":128851909668,"id":36,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855420121,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":15050,"timestamp":128851940117,"id":37,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855420151,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":194691,"timestamp":128851908766,"id":35,"tags":{"url":"/"},"startTime":1751855420120,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":4,"timestamp":128852103531,"id":38,"parentId":35,"tags":{"url":"/","memory.rss":"736997376","memory.heapUsed":"119538720","memory.heapTotal":"151683072"},"startTime":1751855420315,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":15847,"timestamp":128852336010,"id":40,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751855420547,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":13826,"timestamp":128852353139,"id":41,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751855420565,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":259226,"timestamp":128852334895,"id":39,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico"},"startTime":1751855420546,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":6,"timestamp":128852594168,"id":42,"parentId":39,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico","memory.rss":"749780992","memory.heapUsed":"125731528","memory.heapTotal":"151945216"},"startTime":1751855420806,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":15348,"timestamp":129030608387,"id":44,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855598820,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":13046,"timestamp":129030625450,"id":45,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855598837,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":61444,"timestamp":129030607396,"id":43,"tags":{"url":"/"},"startTime":1751855598819,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":3,"timestamp":129030668885,"id":46,"parentId":43,"tags":{"url":"/","memory.rss":"719929344","memory.heapUsed":"108979728","memory.heapTotal":"114102272"},"startTime":1751855598880,"traceId":"6e3a319291aa975b"},{"name":"client-hmr-latency","duration":3000,"timestamp":129106220771,"id":47,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751855674551,"traceId":"6e3a319291aa975b"},{"name":"client-hmr-latency","duration":9000,"timestamp":129106354886,"id":48,"parentId":3,"tags":{"updatedModules":["[project]/src/components/TextEditor.tsx"],"page":"/","isPageHidden":true},"startTime":1751855674593,"traceId":"6e3a319291aa975b"},{"name":"client-hmr-latency","duration":8000,"timestamp":129106393383,"id":49,"parentId":3,"tags":{"updatedModules":["[project]/src/components/TextEditor.tsx"],"page":"/","isPageHidden":true},"startTime":1751855674614,"traceId":"6e3a319291aa975b"},{"name":"client-hmr-latency","duration":5000,"timestamp":129106475247,"id":50,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751855674714,"traceId":"6e3a319291aa975b"},{"name":"client-hmr-latency","duration":7000,"timestamp":129106511395,"id":51,"parentId":3,"tags":{"updatedModules":["[project]/src/components/TextEditor.tsx"],"page":"/","isPageHidden":true},"startTime":1751855674731,"traceId":"6e3a319291aa975b"},{"name":"client-hmr-latency","duration":63000,"timestamp":129112776669,"id":52,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751855681080,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":25060,"timestamp":129125931244,"id":54,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855694143,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":13356,"timestamp":129125957408,"id":55,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855694169,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":146264,"timestamp":129125929518,"id":53,"tags":{"url":"/"},"startTime":1751855694141,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":3,"timestamp":129126075816,"id":56,"parentId":53,"tags":{"url":"/","memory.rss":"757796864","memory.heapUsed":"132473560","memory.heapTotal":"164630528"},"startTime":1751855694287,"traceId":"6e3a319291aa975b"},{"name":"client-hmr-latency","duration":96000,"timestamp":129142361630,"id":57,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751855710698,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":28259,"timestamp":129157661381,"id":59,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855725874,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":16152,"timestamp":129157690608,"id":60,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855725903,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":160643,"timestamp":129157660106,"id":58,"tags":{"url":"/"},"startTime":1751855725872,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":2,"timestamp":129157820784,"id":61,"parentId":58,"tags":{"url":"/","memory.rss":"787304448","memory.heapUsed":"146390008","memory.heapTotal":"180088832"},"startTime":1751855726033,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":16088,"timestamp":129188304336,"id":63,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855756516,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":13516,"timestamp":129188321674,"id":64,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855756534,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":66705,"timestamp":129188303167,"id":62,"tags":{"url":"/"},"startTime":1751855756515,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":2,"timestamp":129188369902,"id":65,"parentId":62,"tags":{"url":"/","memory.rss":"760119296","memory.heapUsed":"132726272","memory.heapTotal":"140378112"},"startTime":1751855756582,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":28261,"timestamp":129308463794,"id":67,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855876676,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":13735,"timestamp":129308493122,"id":68,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855876705,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":155805,"timestamp":129308463168,"id":66,"tags":{"url":"/"},"startTime":1751855876675,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":3,"timestamp":129308619007,"id":69,"parentId":66,"tags":{"url":"/","memory.rss":"795541504","memory.heapUsed":"156680192","memory.heapTotal":"187908096"},"startTime":1751855876831,"traceId":"6e3a319291aa975b"},{"name":"compile-path","duration":21591,"timestamp":129339421644,"id":72,"tags":{"trigger":"/"},"startTime":1751855907633,"traceId":"6e3a319291aa975b"}]
[{"name":"ensure-page","duration":14318,"timestamp":129339444945,"id":73,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855907657,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":258491,"timestamp":129339420465,"id":70,"tags":{"url":"/"},"startTime":1751855907632,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":3,"timestamp":129339679003,"id":74,"parentId":70,"tags":{"url":"/","memory.rss":"852881408","memory.heapUsed":"195162200","memory.heapTotal":"229318656"},"startTime":1751855907891,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":15006,"timestamp":129339901517,"id":76,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751855908113,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":15508,"timestamp":129339917654,"id":77,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751855908129,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":258085,"timestamp":129339900788,"id":75,"tags":{"url":"/favicon.ico"},"startTime":1751855908113,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":2,"timestamp":129340158905,"id":78,"parentId":75,"tags":{"url":"/favicon.ico","memory.rss":"852541440","memory.heapUsed":"184828768","memory.heapTotal":"227852288"},"startTime":1751855908371,"traceId":"6e3a319291aa975b"},{"name":"compile-path","duration":22365,"timestamp":129378636988,"id":81,"tags":{"trigger":"/"},"startTime":1751855946848,"traceId":"6e3a319291aa975b"}]
[{"name":"ensure-page","duration":14396,"timestamp":129378661236,"id":82,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855946873,"traceId":"6e3a319291aa975b"},{"name":"client-hmr-latency","duration":49000,"timestamp":129378561038,"id":83,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":false},"startTime":1751855946954,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":112237,"timestamp":129378635369,"id":79,"tags":{"url":"/"},"startTime":1751855946847,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":2,"timestamp":129378747628,"id":84,"parentId":79,"tags":{"url":"/","memory.rss":"820097024","memory.heapUsed":"177056568","memory.heapTotal":"213286912"},"startTime":1751855946959,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":15082,"timestamp":129378883385,"id":86,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751855947095,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":14250,"timestamp":129378899669,"id":87,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751855947111,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":245507,"timestamp":129378882641,"id":85,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico"},"startTime":1751855947094,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":2,"timestamp":129379128171,"id":88,"parentId":85,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico","memory.rss":"829558784","memory.heapUsed":"179654208","memory.heapTotal":"213286912"},"startTime":1751855947340,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":15538,"timestamp":129393031059,"id":90,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855961242,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":14452,"timestamp":129393047695,"id":91,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855961259,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":52832,"timestamp":129393030325,"id":89,"tags":{"url":"/"},"startTime":1751855961242,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":2,"timestamp":129393083181,"id":92,"parentId":89,"tags":{"url":"/","memory.rss":"837713920","memory.heapUsed":"175741800","memory.heapTotal":"183926784"},"startTime":1751855961295,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":15605,"timestamp":129404853200,"id":94,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855973065,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":13781,"timestamp":129404869940,"id":95,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855973081,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":52232,"timestamp":129404852446,"id":93,"tags":{"url":"/"},"startTime":1751855973064,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":1,"timestamp":129404904698,"id":96,"parentId":93,"tags":{"url":"/","memory.rss":"838701056","memory.heapUsed":"177957616","memory.heapTotal":"185499648"},"startTime":1751855973116,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":15318,"timestamp":129404987784,"id":98,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751855973199,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":15410,"timestamp":129405004180,"id":99,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751855973216,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":243680,"timestamp":129404987096,"id":97,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico"},"startTime":1751855973199,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":5,"timestamp":129405230820,"id":100,"parentId":97,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico","memory.rss":"838623232","memory.heapUsed":"179626664","memory.heapTotal":"190742528"},"startTime":1751855973442,"traceId":"6e3a319291aa975b"},{"name":"compile-path","duration":30050,"timestamp":129407600086,"id":104,"tags":{"trigger":"/"},"startTime":1751855975812,"traceId":"6e3a319291aa975b"}]
[{"name":"ensure-page","duration":21591,"timestamp":129407610735,"id":105,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855975822,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":21043,"timestamp":129407631835,"id":106,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855975843,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":98833,"timestamp":129407598385,"id":101,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1751855975810,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":2,"timestamp":129407697238,"id":108,"parentId":101,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"928813056","memory.heapUsed":"189863112","memory.heapTotal":"228933632"},"startTime":1751855975909,"traceId":"6e3a319291aa975b"},{"name":"ensure-page","duration":64216,"timestamp":129407633048,"id":107,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751855975844,"traceId":"6e3a319291aa975b"},{"name":"handle-request","duration":104463,"timestamp":129407598801,"id":102,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1751855975810,"traceId":"6e3a319291aa975b"},{"name":"memory-usage","duration":1,"timestamp":129407703281,"id":109,"parentId":102,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"928821248","memory.heapUsed":"192983496","memory.heapTotal":"228933632"},"startTime":1751855975915,"traceId":"6e3a319291aa975b"},{"name":"client-hmr-latency","duration":264000,"timestamp":129407392700,"id":110,"parentId":3,"tags":{"updatedModules":["[project]/src/components/TagManager.tsx","[project]/src/components/FileUpload.tsx","[project]/src/components/Settings.tsx","[project]/src/lib/testData.ts","[project]/src/components/JournalApp.tsx","[project]/src/app/page.tsx","[project]/node_modules/next/dist/build/polyfills/object-assign.js","[project]/node_modules/next/dist/compiled/buffer/index.js","[project]/node_modules/next/dist/compiled/events/events.js","[project]/node_modules/next/dist/compiled/util/util.js","[project]/node_modules/next/dist/compiled/stream-browserify/index.js","[project]/node_modules/next/dist/compiled/setimmediate/setImmediate.js","[project]/node_modules/underscore/modules/_setup.js","[project]/node_modules/underscore/modules/restArguments.js","[project]/node_modules/underscore/modules/isObject.js","[project]/node_modules/underscore/modules/isNull.js","[project]/node_modules/underscore/modules/isUndefined.js","[project]/node_modules/underscore/modules/isBoolean.js","[project]/node_modules/underscore/modules/isElement.js","[project]/node_modules/underscore/modules/_tagTester.js","[project]/node_modules/underscore/modules/isString.js","[project]/node_modules/underscore/modules/isNumber.js","[project]/node_modules/underscore/modules/isDate.js","[project]/node_modules/underscore/modules/isRegExp.js","[project]/node_modules/underscore/modules/isError.js","[project]/node_modules/underscore/modules/isSymbol.js","[project]/node_modules/underscore/modules/isArrayBuffer.js","[project]/node_modules/underscore/modules/isFunction.js","[project]/node_modules/underscore/modules/_hasObjectTag.js","[project]/node_modules/underscore/modules/_stringTagBug.js","[project]/node_modules/underscore/modules/isDataView.js","[project]/node_modules/underscore/modules/isArray.js","[project]/node_modules/underscore/modules/_has.js","[project]/node_modules/underscore/modules/isArguments.js","[project]/node_modules/underscore/modules/isFinite.js","[project]/node_modules/underscore/modules/isNaN.js","[project]/node_modules/underscore/modules/constant.js","[project]/node_modules/underscore/modules/_createSizePropertyCheck.js","[project]/node_modules/underscore/modules/_shallowProperty.js","[project]/node_modules/underscore/modules/_getByteLength.js","[project]/node_modules/underscore/modules/_isBufferLike.js","[project]/node_modules/underscore/modules/isTypedArray.js","[project]/node_modules/underscore/modules/_getLength.js","[project]/node_modules/underscore/modules/_collectNonEnumProps.js","[project]/node_modules/underscore/modules/keys.js","[project]/node_modules/underscore/modules/isEmpty.js","[project]/node_modules/underscore/modules/isMatch.js","[project]/node_modules/underscore/modules/underscore.js","[project]/node_modules/underscore/modules/_toBufferView.js","[project]/node_modules/underscore/modules/isEqual.js","[project]/node_modules/underscore/modules/allKeys.js","[project]/node_modules/underscore/modules/_methodFingerprint.js","[project]/node_modules/underscore/modules/isMap.js","[project]/node_modules/underscore/modules/isWeakMap.js","[project]/node_modules/underscore/modules/isSet.js","[project]/node_modules/underscore/modules/isWeakSet.js","[project]/node_modules/underscore/modules/values.js","[project]/node_modules/underscore/modules/pairs.js","[project]/node_modules/underscore/modules/invert.js","[project]/node_modules/underscore/modules/functions.js","[project]/node_modules/underscore/modules/_createAssigner.js","[project]/node_modules/underscore/modules/extend.js","[project]/node_modules/underscore/modules/extendOwn.js","[project]/node_modules/underscore/modules/defaults.js","[project]/node_modules/underscore/modules/_baseCreate.js","[project]/node_modules/underscore/modules/create.js","[project]/node_modules/underscore/modules/clone.js","[project]/node_modules/underscore/modules/tap.js","[project]/node_modules/underscore/modules/toPath.js","[project]/node_modules/underscore/modules/_toPath.js","[project]/node_modules/underscore/modules/_deepGet.js","[project]/node_modules/underscore/modules/get.js","[project]/node_modules/underscore/modules/has.js","[project]/node_modules/underscore/modules/identity.js","[project]/node_modules/underscore/modules/matcher.js","[project]/node_modules/underscore/modules/property.js","[project]/node_modules/underscore/modules/_optimizeCb.js","[project]/node_modules/underscore/modules/_baseIteratee.js","[project]/node_modules/underscore/modules/iteratee.js","[project]/node_modules/underscore/modules/_cb.js","[project]/node_modules/underscore/modules/mapObject.js","[project]/node_modules/underscore/modules/noop.js","[project]/node_modules/underscore/modules/propertyOf.js","[project]/node_modules/underscore/modules/times.js","[project]/node_modules/underscore/modules/random.js","[project]/node_modules/underscore/modules/now.js","[project]/node_modules/underscore/modules/_createEscaper.js","[project]/node_modules/underscore/modules/_escapeMap.js","[project]/node_modules/underscore/modules/escape.js","[project]/node_modules/underscore/modules/_unescapeMap.js","[project]/node_modules/underscore/modules/unescape.js","[project]/node_modules/underscore/modules/templateSettings.js","[project]/node_modules/underscore/modules/template.js","[project]/node_modules/underscore/modules/result.js","[project]/node_modules/underscore/modules/uniqueId.js","[project]/node_modules/underscore/modules/chain.js","[project]/node_modules/underscore/modules/_executeBound.js","[project]/node_modules/underscore/modules/partial.js","[project]/node_modules/underscore/modules/bind.js","[project]/node_modules/underscore/modules/_isArrayLike.js","[project]/node_modules/underscore/modules/_flatten.js","[project]/node_modules/underscore/modules/bindAll.js","[project]/node_modules/underscore/modules/memoize.js","[project]/node_modules/underscore/modules/delay.js","[project]/node_modules/underscore/modules/defer.js","[project]/node_modules/underscore/modules/throttle.js","[project]/node_modules/underscore/modules/debounce.js","[project]/node_modules/underscore/modules/wrap.js","[project]/node_modules/underscore/modules/negate.js","[project]/node_modules/underscore/modules/compose.js","[project]/node_modules/underscore/modules/after.js","[project]/node_modules/underscore/modules/before.js","[project]/node_modules/underscore/modules/once.js","[project]/node_modules/underscore/modules/findKey.js","[project]/node_modules/underscore/modules/_createPredicateIndexFinder.js","[project]/node_modules/underscore/modules/findIndex.js","[project]/node_modules/underscore/modules/findLastIndex.js","[project]/node_modules/underscore/modules/sortedIndex.js","[project]/node_modules/underscore/modules/_createIndexFinder.js","[project]/node_modules/underscore/modules/indexOf.js","[project]/node_modules/underscore/modules/lastIndexOf.js","[project]/node_modules/underscore/modules/find.js","[project]/node_modules/underscore/modules/findWhere.js","[project]/node_modules/underscore/modules/each.js","[project]/node_modules/underscore/modules/map.js","[project]/node_modules/underscore/modules/_createReduce.js","[project]/node_modules/underscore/modules/reduce.js","[project]/node_modules/underscore/modules/reduceRight.js","[project]/node_modules/underscore/modules/filter.js","[project]/node_modules/underscore/modules/reject.js","[project]/node_modules/underscore/modules/every.js","[project]/node_modules/underscore/modules/some.js","[project]/node_modules/underscore/modules/contains.js","[project]/node_modules/underscore/modules/invoke.js","[project]/node_modules/underscore/modules/pluck.js","[project]/node_modules/underscore/modules/where.js","[project]/node_modules/underscore/modules/max.js","[project]/node_modules/underscore/modules/min.js","[project]/node_modules/underscore/modules/toArray.js","[project]/node_modules/underscore/modules/sample.js","[project]/node_modules/underscore/modules/shuffle.js","[project]/node_modules/underscore/modules/sortBy.js","[project]/node_modules/underscore/modules/_group.js","[project]/node_modules/underscore/modules/groupBy.js","[project]/node_modules/underscore/modules/indexBy.js","[project]/node_modules/underscore/modules/countBy.js","[project]/node_modules/underscore/modules/partition.js","[project]/node_modules/underscore/modules/size.js","[project]/node_modules/underscore/modules/_keyInObj.js","[project]/node_modules/underscore/modules/pick.js","[project]/node_modules/underscore/modules/omit.js","[project]/node_modules/underscore/modules/initial.js","[project]/node_modules/underscore/modules/first.js","[project]/node_modules/underscore/modules/rest.js","[project]/node_modules/underscore/modules/last.js","[project]/node_modules/underscore/modules/compact.js","[project]/node_modules/underscore/modules/flatten.js","[project]/node_modules/underscore/modules/difference.js","[project]/node_modules/underscore/modules/without.js","[project]/node_modules/underscore/modules/uniq.js","[project]/node_modules/underscore/modules/union.js","[project]/node_modules/underscore/modules/intersection.js","[project]/node_modules/underscore/modules/unzip.js","[project]/node_modules/underscore/modules/zip.js","[project]/node_modules/underscore/modules/object.js","[project]/node_modules/underscore/modules/range.js","[project]/node_modules/underscore/modules/chunk.js","[project]/node_modules/underscore/modules/_chainResult.js","[project]/node_modules/underscore/modules/mixin.js","[project]/node_modules/underscore/modules/underscore-array-methods.js","[project]/node_modules/underscore/modules/index.js","[project]/node_modules/underscore/modules/index-default.js","[project]/node_modules/underscore/modules/index-all.js","[project]/node_modules/bluebird/js/release/es5.js","[project]/node_modules/bluebird/js/release/util.js","[project]/node_modules/bluebird/js/release/schedule.js","[project]/node_modules/bluebird/js/release/queue.js","[project]/node_modules/bluebird/js/release/async.js","[project]/node_modules/bluebird/js/release/errors.js","[project]/node_modules/bluebird/js/release/thenables.js","[project]/node_modules/bluebird/js/release/promise_array.js","[project]/node_modules/bluebird/js/release/context.js","[project]/node_modules/bluebird/js/release/debuggability.js","[project]/node_modules/bluebird/js/release/finally.js","[project]/node_modules/bluebird/js/release/catch_filter.js","[project]/node_modules/bluebird/js/release/nodeback.js","[project]/node_modules/bluebird/js/release/method.js","[project]/node_modules/bluebird/js/release/bind.js","[project]/node_modules/bluebird/js/release/cancel.js","[project]/node_modules/bluebird/js/release/direct_resolve.js","[project]/node_modules/bluebird/js/release/synchronous_inspection.js","[project]/node_modules/bluebird/js/release/join.js","[project]/node_modules/bluebird/js/release/map.js","[project]/node_modules/bluebird/js/release/call_get.js","[project]/node_modules/bluebird/js/release/using.js","[project]/node_modules/bluebird/js/release/timers.js","[project]/node_modules/bluebird/js/release/generators.js","[project]/node_modules/bluebird/js/release/nodeify.js","[project]/node_modules/bluebird/js/release/promisify.js","[project]/node_modules/bluebird/js/release/props.js","[project]/node_modules/bluebird/js/release/race.js","[project]/node_modules/bluebird/js/release/reduce.js","[project]/node_modules/bluebird/js/release/settle.js","[project]/node_modules/bluebird/js/release/some.js","[project]/node_modules/bluebird/js/release/filter.js","[project]/node_modules/bluebird/js/release/each.js","[project]/node_modules/bluebird/js/release/any.js","[project]/node_modules/bluebird/js/release/promise.js","[project]/node_modules/mammoth/lib/promises.js","[project]/node_modules/mammoth/lib/documents.js","[project]/node_modules/mammoth/lib/results.js","[project]/node_modules/mammoth/lib/zipfile.js","[project]/node_modules/mammoth/lib/xml/nodes.js","[project]/node_modules/mammoth/lib/xml/xmldom.js","[project]/node_modules/mammoth/lib/xml/reader.js","[project]/node_modules/mammoth/lib/xml/writer.js","[project]/node_modules/mammoth/lib/xml/index.js","[project]/node_modules/mammoth/lib/docx/office-xml-reader.js","[project]/node_modules/mammoth/lib/docx/uris.js","[project]/node_modules/mammoth/lib/docx/body-reader.js","[project]/node_modules/mammoth/lib/docx/document-xml-reader.js","[project]/node_modules/mammoth/lib/docx/relationships-reader.js","[project]/node_modules/mammoth/lib/docx/content-types-reader.js","[project]/node_modules/mammoth/lib/docx/numbering-xml.js","[project]/node_modules/mammoth/lib/docx/styles-reader.js","[project]/node_modules/mammoth/lib/docx/notes-reader.js","[project]/node_modules/mammoth/lib/docx/comments-reader.js","[project]/node_modules/mammoth/browser/docx/files.js","[project]/node_modules/mammoth/lib/docx/docx-reader.js","[project]/node_modules/mammoth/lib/docx/style-map.js","[project]/node_modules/mammoth/lib/html/ast.js","[project]/node_modules/mammoth/lib/html/simplify.js","[project]/node_modules/mammoth/lib/html/index.js","[project]/node_modules/mammoth/lib/styles/html-paths.js","[project]/node_modules/mammoth/lib/images.js","[project]/node_modules/mammoth/lib/writers/html-writer.js","[project]/node_modules/mammoth/lib/writers/markdown-writer.js","[project]/node_modules/mammoth/lib/writers/index.js","[project]/node_modules/mammoth/lib/document-to-html.js","[project]/node_modules/mammoth/lib/raw-text.js","[project]/node_modules/mammoth/lib/styles/document-matchers.js","[project]/node_modules/mammoth/lib/styles/parser/tokeniser.js","[project]/node_modules/mammoth/lib/style-reader.js","[project]/node_modules/mammoth/lib/options-reader.js","[project]/node_modules/mammoth/browser/unzip.js","[project]/node_modules/mammoth/lib/transforms.js","[project]/node_modules/mammoth/lib/underline.js","[project]/node_modules/mammoth/lib/index.js","[project]/node_modules/jszip/lib/readable-stream-browser.js","[project]/node_modules/jszip/lib/support.js","[project]/node_modules/jszip/lib/base64.js","[project]/node_modules/jszip/lib/nodejsUtils.js","[project]/node_modules/jszip/lib/external.js","[project]/node_modules/jszip/lib/utils.js","[project]/node_modules/jszip/lib/stream/GenericWorker.js","[project]/node_modules/jszip/lib/utf8.js","[project]/node_modules/jszip/lib/stream/ConvertWorker.js","[project]/node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js","[project]/node_modules/jszip/lib/stream/StreamHelper.js","[project]/node_modules/jszip/lib/defaults.js","[project]/node_modules/jszip/lib/stream/DataWorker.js","[project]/node_modules/jszip/lib/crc32.js","[project]/node_modules/jszip/lib/stream/Crc32Probe.js","[project]/node_modules/jszip/lib/stream/DataLengthProbe.js","[project]/node_modules/jszip/lib/compressedObject.js","[project]/node_modules/jszip/lib/zipObject.js","[project]/node_modules/jszip/lib/flate.js","[project]/node_modules/jszip/lib/compressions.js","[project]/node_modules/jszip/lib/signature.js","[project]/node_modules/jszip/lib/generate/ZipFileWorker.js","[project]/node_modules/jszip/lib/generate/index.js","[project]/node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js","[project]/node_modules/jszip/lib/object.js","[project]/node_modules/jszip/lib/reader/DataReader.js","[project]/node_modules/jszip/lib/reader/ArrayReader.js","[project]/node_modules/jszip/lib/reader/StringReader.js","[project]/node_modules/jszip/lib/reader/Uint8ArrayReader.js","[project]/node_modules/jszip/lib/reader/NodeBufferReader.js","[project]/node_modules/jszip/lib/reader/readerFor.js","[project]/node_modules/jszip/lib/zipEntry.js","[project]/node_modules/jszip/lib/zipEntries.js","[project]/node_modules/jszip/lib/load.js","[project]/node_modules/jszip/lib/index.js","[project]/node_modules/pako/lib/utils/common.js","[project]/node_modules/pako/lib/zlib/trees.js","[project]/node_modules/pako/lib/zlib/adler32.js","[project]/node_modules/pako/lib/zlib/crc32.js","[project]/node_modules/pako/lib/zlib/messages.js","[project]/node_modules/pako/lib/zlib/deflate.js","[project]/node_modules/pako/lib/utils/strings.js","[project]/node_modules/pako/lib/zlib/zstream.js","[project]/node_modules/pako/lib/deflate.js","[project]/node_modules/pako/lib/zlib/inffast.js","[project]/node_modules/pako/lib/zlib/inftrees.js","[project]/node_modules/pako/lib/zlib/inflate.js","[project]/node_modules/pako/lib/zlib/constants.js","[project]/node_modules/pako/lib/zlib/gzheader.js","[project]/node_modules/pako/lib/inflate.js","[project]/node_modules/pako/index.js","[project]/node_modules/@xmldom/xmldom/lib/conventions.js","[project]/node_modules/@xmldom/xmldom/lib/dom.js","[project]/node_modules/@xmldom/xmldom/lib/entities.js","[project]/node_modules/@xmldom/xmldom/lib/sax.js","[project]/node_modules/@xmldom/xmldom/lib/dom-parser.js","[project]/node_modules/@xmldom/xmldom/lib/index.js","[project]/node_modules/dingbat-to-unicode/dist/dingbats.js","[project]/node_modules/dingbat-to-unicode/dist/index.js","[project]/node_modules/lucide-react/dist/esm/shared/src/utils.js","[project]/node_modules/lucide-react/dist/esm/defaultAttributes.js","[project]/node_modules/lucide-react/dist/esm/Icon.js","[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js","[project]/node_modules/lucide-react/dist/esm/icons/circle-plus.js","[project]/node_modules/lucide-react/dist/esm/icons/settings.js","[project]/node_modules/lucide-react/dist/esm/icons/upload.js","[project]/node_modules/lucide-react/dist/esm/icons/book-open.js","[project]/node_modules/lucide-react/dist/esm/icons/menu.js","[project]/node_modules/lucide-react/dist/esm/icons/x.js","[project]/node_modules/lucide-react/dist/esm/icons/zoom-in.js","[project]/node_modules/lucide-react/dist/esm/icons/zoom-out.js","[project]/node_modules/lucide-react/dist/esm/icons/search.js","[project]/node_modules/lucide-react/dist/esm/icons/tag.js","[project]/node_modules/lucide-react/dist/esm/icons/plus.js","[project]/node_modules/lucide-react/dist/esm/icons/file.js","[project]/node_modules/lucide-react/dist/esm/icons/file-text.js","[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js","[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js","[project]/node_modules/lucide-react/dist/esm/icons/sun.js","[project]/node_modules/lucide-react/dist/esm/icons/moon.js","[project]/node_modules/lucide-react/dist/esm/icons/monitor.js","[project]/node_modules/lucide-react/dist/esm/icons/type.js","[project]/node_modules/lucide-react/dist/esm/icons/save.js","[project]/node_modules/lucide-react/dist/esm/icons/rotate-ccw.js","[project]/node_modules/lucide-react/dist/esm/icons/palette.js","[project]/node_modules/lucide-react/dist/esm/icons/zap.js","[project]/node_modules/lucide-react/dist/esm/icons/square-check-big.js","[project]/node_modules/tslib/tslib.es6.mjs","[project]/node_modules/file-selector/dist/es2015/file.js","[project]/node_modules/file-selector/dist/es2015/file-selector.js","[project]/node_modules/file-selector/dist/es2015/index.js","[project]/node_modules/attr-accept/dist/es/index.js","[project]/node_modules/react-dropzone/dist/es/utils/index.js","[project]/node_modules/react-dropzone/dist/es/index.js","[project]/node_modules/react-is/cjs/react-is.development.js","[project]/node_modules/react-is/index.js","[project]/node_modules/prop-types/lib/ReactPropTypesSecret.js","[project]/node_modules/prop-types/lib/has.js","[project]/node_modules/prop-types/checkPropTypes.js","[project]/node_modules/prop-types/factoryWithTypeCheckers.js","[project]/node_modules/prop-types/index.js","[project]/node_modules/base64-js/index.js","[project]/node_modules/ieee754/index.js","[project]/node_modules/buffer/index.js","[project]/node_modules/immediate/lib/browser.js","[project]/node_modules/lie/lib/browser.js","[project]/node_modules/xmlbuilder/lib/Utility.js","[project]/node_modules/xmlbuilder/lib/XMLAttribute.js","[project]/node_modules/xmlbuilder/lib/XMLElement.js","[project]/node_modules/xmlbuilder/lib/XMLCData.js","[project]/node_modules/xmlbuilder/lib/XMLComment.js","[project]/node_modules/xmlbuilder/lib/XMLDeclaration.js","[project]/node_modules/xmlbuilder/lib/XMLDTDAttList.js","[project]/node_modules/xmlbuilder/lib/XMLDTDEntity.js","[project]/node_modules/xmlbuilder/lib/XMLDTDElement.js","[project]/node_modules/xmlbuilder/lib/XMLDTDNotation.js","[project]/node_modules/xmlbuilder/lib/XMLDocType.js","[project]/node_modules/xmlbuilder/lib/XMLRaw.js","[project]/node_modules/xmlbuilder/lib/XMLText.js","[project]/node_modules/xmlbuilder/lib/XMLProcessingInstruction.js","[project]/node_modules/xmlbuilder/lib/XMLDummy.js","[project]/node_modules/xmlbuilder/lib/XMLNode.js","[project]/node_modules/xmlbuilder/lib/XMLStringifier.js","[project]/node_modules/xmlbuilder/lib/XMLWriterBase.js","[project]/node_modules/xmlbuilder/lib/XMLStringWriter.js","[project]/node_modules/xmlbuilder/lib/XMLDocument.js","[project]/node_modules/xmlbuilder/lib/XMLDocumentCB.js","[project]/node_modules/xmlbuilder/lib/XMLStreamWriter.js","[project]/node_modules/xmlbuilder/lib/index.js","[project]/node_modules/lop/lib/TokenIterator.js","[project]/node_modules/lop/lib/parser.js","[project]/node_modules/lop/lib/parsing-results.js","[project]/node_modules/lop/lib/errors.js","[project]/node_modules/lop/lib/lazy-iterators.js","[project]/node_modules/lop/lib/rules.js","[project]/node_modules/lop/lib/StringSource.js","[project]/node_modules/lop/lib/Token.js","[project]/node_modules/lop/lib/bottom-up.js","[project]/node_modules/lop/lib/regex-tokeniser.js","[project]/node_modules/lop/index.js","[project]/node_modules/option/index.js"],"page":"/","isPageHidden":false},"startTime":1751855975961,"traceId":"6e3a319291aa975b"},{"name":"client-hmr-latency","duration":295000,"timestamp":129407392397,"id":111,"parentId":3,"tags":{"updatedModules":["[project]/src/components/TagManager.tsx","[project]/src/components/FileUpload.tsx","[project]/src/components/Settings.tsx","[project]/src/lib/testData.ts","[project]/src/components/JournalApp.tsx","[project]/src/app/page.tsx","[project]/node_modules/next/dist/build/polyfills/object-assign.js","[project]/node_modules/next/dist/compiled/buffer/index.js","[project]/node_modules/next/dist/compiled/events/events.js","[project]/node_modules/next/dist/compiled/util/util.js","[project]/node_modules/next/dist/compiled/stream-browserify/index.js","[project]/node_modules/next/dist/compiled/setimmediate/setImmediate.js","[project]/node_modules/underscore/modules/_setup.js","[project]/node_modules/underscore/modules/restArguments.js","[project]/node_modules/underscore/modules/isObject.js","[project]/node_modules/underscore/modules/isNull.js","[project]/node_modules/underscore/modules/isUndefined.js","[project]/node_modules/underscore/modules/isBoolean.js","[project]/node_modules/underscore/modules/isElement.js","[project]/node_modules/underscore/modules/_tagTester.js","[project]/node_modules/underscore/modules/isString.js","[project]/node_modules/underscore/modules/isNumber.js","[project]/node_modules/underscore/modules/isDate.js","[project]/node_modules/underscore/modules/isRegExp.js","[project]/node_modules/underscore/modules/isError.js","[project]/node_modules/underscore/modules/isSymbol.js","[project]/node_modules/underscore/modules/isArrayBuffer.js","[project]/node_modules/underscore/modules/isFunction.js","[project]/node_modules/underscore/modules/_hasObjectTag.js","[project]/node_modules/underscore/modules/_stringTagBug.js","[project]/node_modules/underscore/modules/isDataView.js","[project]/node_modules/underscore/modules/isArray.js","[project]/node_modules/underscore/modules/_has.js","[project]/node_modules/underscore/modules/isArguments.js","[project]/node_modules/underscore/modules/isFinite.js","[project]/node_modules/underscore/modules/isNaN.js","[project]/node_modules/underscore/modules/constant.js","[project]/node_modules/underscore/modules/_createSizePropertyCheck.js","[project]/node_modules/underscore/modules/_shallowProperty.js","[project]/node_modules/underscore/modules/_getByteLength.js","[project]/node_modules/underscore/modules/_isBufferLike.js","[project]/node_modules/underscore/modules/isTypedArray.js","[project]/node_modules/underscore/modules/_getLength.js","[project]/node_modules/underscore/modules/_collectNonEnumProps.js","[project]/node_modules/underscore/modules/keys.js","[project]/node_modules/underscore/modules/isEmpty.js","[project]/node_modules/underscore/modules/isMatch.js","[project]/node_modules/underscore/modules/underscore.js","[project]/node_modules/underscore/modules/_toBufferView.js","[project]/node_modules/underscore/modules/isEqual.js","[project]/node_modules/underscore/modules/allKeys.js","[project]/node_modules/underscore/modules/_methodFingerprint.js","[project]/node_modules/underscore/modules/isMap.js","[project]/node_modules/underscore/modules/isWeakMap.js","[project]/node_modules/underscore/modules/isSet.js","[project]/node_modules/underscore/modules/isWeakSet.js","[project]/node_modules/underscore/modules/values.js","[project]/node_modules/underscore/modules/pairs.js","[project]/node_modules/underscore/modules/invert.js","[project]/node_modules/underscore/modules/functions.js","[project]/node_modules/underscore/modules/_createAssigner.js","[project]/node_modules/underscore/modules/extend.js","[project]/node_modules/underscore/modules/extendOwn.js","[project]/node_modules/underscore/modules/defaults.js","[project]/node_modules/underscore/modules/_baseCreate.js","[project]/node_modules/underscore/modules/create.js","[project]/node_modules/underscore/modules/clone.js","[project]/node_modules/underscore/modules/tap.js","[project]/node_modules/underscore/modules/toPath.js","[project]/node_modules/underscore/modules/_toPath.js","[project]/node_modules/underscore/modules/_deepGet.js","[project]/node_modules/underscore/modules/get.js","[project]/node_modules/underscore/modules/has.js","[project]/node_modules/underscore/modules/identity.js","[project]/node_modules/underscore/modules/matcher.js","[project]/node_modules/underscore/modules/property.js","[project]/node_modules/underscore/modules/_optimizeCb.js","[project]/node_modules/underscore/modules/_baseIteratee.js","[project]/node_modules/underscore/modules/iteratee.js","[project]/node_modules/underscore/modules/_cb.js","[project]/node_modules/underscore/modules/mapObject.js","[project]/node_modules/underscore/modules/noop.js","[project]/node_modules/underscore/modules/propertyOf.js","[project]/node_modules/underscore/modules/times.js","[project]/node_modules/underscore/modules/random.js","[project]/node_modules/underscore/modules/now.js","[project]/node_modules/underscore/modules/_createEscaper.js","[project]/node_modules/underscore/modules/_escapeMap.js","[project]/node_modules/underscore/modules/escape.js","[project]/node_modules/underscore/modules/_unescapeMap.js","[project]/node_modules/underscore/modules/unescape.js","[project]/node_modules/underscore/modules/templateSettings.js","[project]/node_modules/underscore/modules/template.js","[project]/node_modules/underscore/modules/result.js","[project]/node_modules/underscore/modules/uniqueId.js","[project]/node_modules/underscore/modules/chain.js","[project]/node_modules/underscore/modules/_executeBound.js","[project]/node_modules/underscore/modules/partial.js","[project]/node_modules/underscore/modules/bind.js","[project]/node_modules/underscore/modules/_isArrayLike.js","[project]/node_modules/underscore/modules/_flatten.js","[project]/node_modules/underscore/modules/bindAll.js","[project]/node_modules/underscore/modules/memoize.js","[project]/node_modules/underscore/modules/delay.js","[project]/node_modules/underscore/modules/defer.js","[project]/node_modules/underscore/modules/throttle.js","[project]/node_modules/underscore/modules/debounce.js","[project]/node_modules/underscore/modules/wrap.js","[project]/node_modules/underscore/modules/negate.js","[project]/node_modules/underscore/modules/compose.js","[project]/node_modules/underscore/modules/after.js","[project]/node_modules/underscore/modules/before.js","[project]/node_modules/underscore/modules/once.js","[project]/node_modules/underscore/modules/findKey.js","[project]/node_modules/underscore/modules/_createPredicateIndexFinder.js","[project]/node_modules/underscore/modules/findIndex.js","[project]/node_modules/underscore/modules/findLastIndex.js","[project]/node_modules/underscore/modules/sortedIndex.js","[project]/node_modules/underscore/modules/_createIndexFinder.js","[project]/node_modules/underscore/modules/indexOf.js","[project]/node_modules/underscore/modules/lastIndexOf.js","[project]/node_modules/underscore/modules/find.js","[project]/node_modules/underscore/modules/findWhere.js","[project]/node_modules/underscore/modules/each.js","[project]/node_modules/underscore/modules/map.js","[project]/node_modules/underscore/modules/_createReduce.js","[project]/node_modules/underscore/modules/reduce.js","[project]/node_modules/underscore/modules/reduceRight.js","[project]/node_modules/underscore/modules/filter.js","[project]/node_modules/underscore/modules/reject.js","[project]/node_modules/underscore/modules/every.js","[project]/node_modules/underscore/modules/some.js","[project]/node_modules/underscore/modules/contains.js","[project]/node_modules/underscore/modules/invoke.js","[project]/node_modules/underscore/modules/pluck.js","[project]/node_modules/underscore/modules/where.js","[project]/node_modules/underscore/modules/max.js","[project]/node_modules/underscore/modules/min.js","[project]/node_modules/underscore/modules/toArray.js","[project]/node_modules/underscore/modules/sample.js","[project]/node_modules/underscore/modules/shuffle.js","[project]/node_modules/underscore/modules/sortBy.js","[project]/node_modules/underscore/modules/_group.js","[project]/node_modules/underscore/modules/groupBy.js","[project]/node_modules/underscore/modules/indexBy.js","[project]/node_modules/underscore/modules/countBy.js","[project]/node_modules/underscore/modules/partition.js","[project]/node_modules/underscore/modules/size.js","[project]/node_modules/underscore/modules/_keyInObj.js","[project]/node_modules/underscore/modules/pick.js","[project]/node_modules/underscore/modules/omit.js","[project]/node_modules/underscore/modules/initial.js","[project]/node_modules/underscore/modules/first.js","[project]/node_modules/underscore/modules/rest.js","[project]/node_modules/underscore/modules/last.js","[project]/node_modules/underscore/modules/compact.js","[project]/node_modules/underscore/modules/flatten.js","[project]/node_modules/underscore/modules/difference.js","[project]/node_modules/underscore/modules/without.js","[project]/node_modules/underscore/modules/uniq.js","[project]/node_modules/underscore/modules/union.js","[project]/node_modules/underscore/modules/intersection.js","[project]/node_modules/underscore/modules/unzip.js","[project]/node_modules/underscore/modules/zip.js","[project]/node_modules/underscore/modules/object.js","[project]/node_modules/underscore/modules/range.js","[project]/node_modules/underscore/modules/chunk.js","[project]/node_modules/underscore/modules/_chainResult.js","[project]/node_modules/underscore/modules/mixin.js","[project]/node_modules/underscore/modules/underscore-array-methods.js","[project]/node_modules/underscore/modules/index.js","[project]/node_modules/underscore/modules/index-default.js","[project]/node_modules/underscore/modules/index-all.js","[project]/node_modules/bluebird/js/release/es5.js","[project]/node_modules/bluebird/js/release/util.js","[project]/node_modules/bluebird/js/release/schedule.js","[project]/node_modules/bluebird/js/release/queue.js","[project]/node_modules/bluebird/js/release/async.js","[project]/node_modules/bluebird/js/release/errors.js","[project]/node_modules/bluebird/js/release/thenables.js","[project]/node_modules/bluebird/js/release/promise_array.js","[project]/node_modules/bluebird/js/release/context.js","[project]/node_modules/bluebird/js/release/debuggability.js","[project]/node_modules/bluebird/js/release/finally.js","[project]/node_modules/bluebird/js/release/catch_filter.js","[project]/node_modules/bluebird/js/release/nodeback.js","[project]/node_modules/bluebird/js/release/method.js","[project]/node_modules/bluebird/js/release/bind.js","[project]/node_modules/bluebird/js/release/cancel.js","[project]/node_modules/bluebird/js/release/direct_resolve.js","[project]/node_modules/bluebird/js/release/synchronous_inspection.js","[project]/node_modules/bluebird/js/release/join.js","[project]/node_modules/bluebird/js/release/map.js","[project]/node_modules/bluebird/js/release/call_get.js","[project]/node_modules/bluebird/js/release/using.js","[project]/node_modules/bluebird/js/release/timers.js","[project]/node_modules/bluebird/js/release/generators.js","[project]/node_modules/bluebird/js/release/nodeify.js","[project]/node_modules/bluebird/js/release/promisify.js","[project]/node_modules/bluebird/js/release/props.js","[project]/node_modules/bluebird/js/release/race.js","[project]/node_modules/bluebird/js/release/reduce.js","[project]/node_modules/bluebird/js/release/settle.js","[project]/node_modules/bluebird/js/release/some.js","[project]/node_modules/bluebird/js/release/filter.js","[project]/node_modules/bluebird/js/release/each.js","[project]/node_modules/bluebird/js/release/any.js","[project]/node_modules/bluebird/js/release/promise.js","[project]/node_modules/mammoth/lib/promises.js","[project]/node_modules/mammoth/lib/documents.js","[project]/node_modules/mammoth/lib/results.js","[project]/node_modules/mammoth/lib/zipfile.js","[project]/node_modules/mammoth/lib/xml/nodes.js","[project]/node_modules/mammoth/lib/xml/xmldom.js","[project]/node_modules/mammoth/lib/xml/reader.js","[project]/node_modules/mammoth/lib/xml/writer.js","[project]/node_modules/mammoth/lib/xml/index.js","[project]/node_modules/mammoth/lib/docx/office-xml-reader.js","[project]/node_modules/mammoth/lib/docx/uris.js","[project]/node_modules/mammoth/lib/docx/body-reader.js","[project]/node_modules/mammoth/lib/docx/document-xml-reader.js","[project]/node_modules/mammoth/lib/docx/relationships-reader.js","[project]/node_modules/mammoth/lib/docx/content-types-reader.js","[project]/node_modules/mammoth/lib/docx/numbering-xml.js","[project]/node_modules/mammoth/lib/docx/styles-reader.js","[project]/node_modules/mammoth/lib/docx/notes-reader.js","[project]/node_modules/mammoth/lib/docx/comments-reader.js","[project]/node_modules/mammoth/browser/docx/files.js","[project]/node_modules/mammoth/lib/docx/docx-reader.js","[project]/node_modules/mammoth/lib/docx/style-map.js","[project]/node_modules/mammoth/lib/html/ast.js","[project]/node_modules/mammoth/lib/html/simplify.js","[project]/node_modules/mammoth/lib/html/index.js","[project]/node_modules/mammoth/lib/styles/html-paths.js","[project]/node_modules/mammoth/lib/images.js","[project]/node_modules/mammoth/lib/writers/html-writer.js","[project]/node_modules/mammoth/lib/writers/markdown-writer.js","[project]/node_modules/mammoth/lib/writers/index.js","[project]/node_modules/mammoth/lib/document-to-html.js","[project]/node_modules/mammoth/lib/raw-text.js","[project]/node_modules/mammoth/lib/styles/document-matchers.js","[project]/node_modules/mammoth/lib/styles/parser/tokeniser.js","[project]/node_modules/mammoth/lib/style-reader.js","[project]/node_modules/mammoth/lib/options-reader.js","[project]/node_modules/mammoth/browser/unzip.js","[project]/node_modules/mammoth/lib/transforms.js","[project]/node_modules/mammoth/lib/underline.js","[project]/node_modules/mammoth/lib/index.js","[project]/node_modules/jszip/lib/readable-stream-browser.js","[project]/node_modules/jszip/lib/support.js","[project]/node_modules/jszip/lib/base64.js","[project]/node_modules/jszip/lib/nodejsUtils.js","[project]/node_modules/jszip/lib/external.js","[project]/node_modules/jszip/lib/utils.js","[project]/node_modules/jszip/lib/stream/GenericWorker.js","[project]/node_modules/jszip/lib/utf8.js","[project]/node_modules/jszip/lib/stream/ConvertWorker.js","[project]/node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js","[project]/node_modules/jszip/lib/stream/StreamHelper.js","[project]/node_modules/jszip/lib/defaults.js","[project]/node_modules/jszip/lib/stream/DataWorker.js","[project]/node_modules/jszip/lib/crc32.js","[project]/node_modules/jszip/lib/stream/Crc32Probe.js","[project]/node_modules/jszip/lib/stream/DataLengthProbe.js","[project]/node_modules/jszip/lib/compressedObject.js","[project]/node_modules/jszip/lib/zipObject.js","[project]/node_modules/jszip/lib/flate.js","[project]/node_modules/jszip/lib/compressions.js","[project]/node_modules/jszip/lib/signature.js","[project]/node_modules/jszip/lib/generate/ZipFileWorker.js","[project]/node_modules/jszip/lib/generate/index.js","[project]/node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js","[project]/node_modules/jszip/lib/object.js","[project]/node_modules/jszip/lib/reader/DataReader.js","[project]/node_modules/jszip/lib/reader/ArrayReader.js","[project]/node_modules/jszip/lib/reader/StringReader.js","[project]/node_modules/jszip/lib/reader/Uint8ArrayReader.js","[project]/node_modules/jszip/lib/reader/NodeBufferReader.js","[project]/node_modules/jszip/lib/reader/readerFor.js","[project]/node_modules/jszip/lib/zipEntry.js","[project]/node_modules/jszip/lib/zipEntries.js","[project]/node_modules/jszip/lib/load.js","[project]/node_modules/jszip/lib/index.js","[project]/node_modules/pako/lib/utils/common.js","[project]/node_modules/pako/lib/zlib/trees.js","[project]/node_modules/pako/lib/zlib/adler32.js","[project]/node_modules/pako/lib/zlib/crc32.js","[project]/node_modules/pako/lib/zlib/messages.js","[project]/node_modules/pako/lib/zlib/deflate.js","[project]/node_modules/pako/lib/utils/strings.js","[project]/node_modules/pako/lib/zlib/zstream.js","[project]/node_modules/pako/lib/deflate.js","[project]/node_modules/pako/lib/zlib/inffast.js","[project]/node_modules/pako/lib/zlib/inftrees.js","[project]/node_modules/pako/lib/zlib/inflate.js","[project]/node_modules/pako/lib/zlib/constants.js","[project]/node_modules/pako/lib/zlib/gzheader.js","[project]/node_modules/pako/lib/inflate.js","[project]/node_modules/pako/index.js","[project]/node_modules/@xmldom/xmldom/lib/conventions.js","[project]/node_modules/@xmldom/xmldom/lib/dom.js","[project]/node_modules/@xmldom/xmldom/lib/entities.js","[project]/node_modules/@xmldom/xmldom/lib/sax.js","[project]/node_modules/@xmldom/xmldom/lib/dom-parser.js","[project]/node_modules/@xmldom/xmldom/lib/index.js","[project]/node_modules/dingbat-to-unicode/dist/dingbats.js","[project]/node_modules/dingbat-to-unicode/dist/index.js","[project]/node_modules/lucide-react/dist/esm/shared/src/utils.js","[project]/node_modules/lucide-react/dist/esm/defaultAttributes.js","[project]/node_modules/lucide-react/dist/esm/Icon.js","[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js","[project]/node_modules/lucide-react/dist/esm/icons/circle-plus.js","[project]/node_modules/lucide-react/dist/esm/icons/settings.js","[project]/node_modules/lucide-react/dist/esm/icons/upload.js","[project]/node_modules/lucide-react/dist/esm/icons/book-open.js","[project]/node_modules/lucide-react/dist/esm/icons/menu.js","[project]/node_modules/lucide-react/dist/esm/icons/x.js","[project]/node_modules/lucide-react/dist/esm/icons/zoom-in.js","[project]/node_modules/lucide-react/dist/esm/icons/zoom-out.js","[project]/node_modules/lucide-react/dist/esm/icons/search.js","[project]/node_modules/lucide-react/dist/esm/icons/tag.js","[project]/node_modules/lucide-react/dist/esm/icons/plus.js","[project]/node_modules/lucide-react/dist/esm/icons/file.js","[project]/node_modules/lucide-react/dist/esm/icons/file-text.js","[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js","[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js","[project]/node_modules/lucide-react/dist/esm/icons/sun.js","[project]/node_modules/lucide-react/dist/esm/icons/moon.js","[project]/node_modules/lucide-react/dist/esm/icons/monitor.js","[project]/node_modules/lucide-react/dist/esm/icons/type.js","[project]/node_modules/lucide-react/dist/esm/icons/save.js","[project]/node_modules/lucide-react/dist/esm/icons/rotate-ccw.js","[project]/node_modules/lucide-react/dist/esm/icons/palette.js","[project]/node_modules/lucide-react/dist/esm/icons/zap.js","[project]/node_modules/lucide-react/dist/esm/icons/square-check-big.js","[project]/node_modules/tslib/tslib.es6.mjs","[project]/node_modules/file-selector/dist/es2015/file.js","[project]/node_modules/file-selector/dist/es2015/file-selector.js","[project]/node_modules/file-selector/dist/es2015/index.js","[project]/node_modules/attr-accept/dist/es/index.js","[project]/node_modules/react-dropzone/dist/es/utils/index.js","[project]/node_modules/react-dropzone/dist/es/index.js","[project]/node_modules/react-is/cjs/react-is.development.js","[project]/node_modules/react-is/index.js","[project]/node_modules/prop-types/lib/ReactPropTypesSecret.js","[project]/node_modules/prop-types/lib/has.js","[project]/node_modules/prop-types/checkPropTypes.js","[project]/node_modules/prop-types/factoryWithTypeCheckers.js","[project]/node_modules/prop-types/index.js","[project]/node_modules/base64-js/index.js","[project]/node_modules/ieee754/index.js","[project]/node_modules/buffer/index.js","[project]/node_modules/immediate/lib/browser.js","[project]/node_modules/lie/lib/browser.js","[project]/node_modules/xmlbuilder/lib/Utility.js","[project]/node_modules/xmlbuilder/lib/XMLAttribute.js","[project]/node_modules/xmlbuilder/lib/XMLElement.js","[project]/node_modules/xmlbuilder/lib/XMLCData.js","[project]/node_modules/xmlbuilder/lib/XMLComment.js","[project]/node_modules/xmlbuilder/lib/XMLDeclaration.js","[project]/node_modules/xmlbuilder/lib/XMLDTDAttList.js","[project]/node_modules/xmlbuilder/lib/XMLDTDEntity.js","[project]/node_modules/xmlbuilder/lib/XMLDTDElement.js","[project]/node_modules/xmlbuilder/lib/XMLDTDNotation.js","[project]/node_modules/xmlbuilder/lib/XMLDocType.js","[project]/node_modules/xmlbuilder/lib/XMLRaw.js","[project]/node_modules/xmlbuilder/lib/XMLText.js","[project]/node_modules/xmlbuilder/lib/XMLProcessingInstruction.js","[project]/node_modules/xmlbuilder/lib/XMLDummy.js","[project]/node_modules/xmlbuilder/lib/XMLNode.js","[project]/node_modules/xmlbuilder/lib/XMLStringifier.js","[project]/node_modules/xmlbuilder/lib/XMLWriterBase.js","[project]/node_modules/xmlbuilder/lib/XMLStringWriter.js","[project]/node_modules/xmlbuilder/lib/XMLDocument.js","[project]/node_modules/xmlbuilder/lib/XMLDocumentCB.js","[project]/node_modules/xmlbuilder/lib/XMLStreamWriter.js","[project]/node_modules/xmlbuilder/lib/index.js","[project]/node_modules/lop/lib/TokenIterator.js","[project]/node_modules/lop/lib/parser.js","[project]/node_modules/lop/lib/parsing-results.js","[project]/node_modules/lop/lib/errors.js","[project]/node_modules/lop/lib/lazy-iterators.js","[project]/node_modules/lop/lib/rules.js","[project]/node_modules/lop/lib/StringSource.js","[project]/node_modules/lop/lib/Token.js","[project]/node_modules/lop/lib/bottom-up.js","[project]/node_modules/lop/lib/regex-tokeniser.js","[project]/node_modules/lop/index.js","[project]/node_modules/option/index.js"],"page":"/","isPageHidden":true},"startTime":1751855976102,"traceId":"6e3a319291aa975b"},{"name":"client-hmr-latency","duration":69000,"timestamp":129430158499,"id":112,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751855998464,"traceId":"6e3a319291aa975b"},{"name":"client-hmr-latency","duration":23000,"timestamp":129444212926,"id":113,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":false},"startTime":1751856012460,"traceId":"6e3a319291aa975b"},{"name":"client-hmr-latency","duration":65000,"timestamp":129453724327,"id":114,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751856022029,"traceId":"6e3a319291aa975b"},{"name":"client-hmr-latency","duration":15000,"timestamp":129465520022,"id":115,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751856033771,"traceId":"6e3a319291aa975b"},{"name":"client-hmr-latency","duration":64000,"timestamp":129474242047,"id":116,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751856042548,"traceId":"6e3a319291aa975b"},{"name":"client-hmr-latency","duration":64000,"timestamp":129474242184,"id":117,"parentId":3,"tags":{"updatedModules":["[project]/src/components/JournalApp.tsx"],"page":"/","isPageHidden":true},"startTime":1751856042548,"traceId":"6e3a319291aa975b"},{"name":"compile-path","duration":35960,"timestamp":129497440996,"id":121,"tags":{"trigger":"/"},"startTime":1751856065653,"traceId":"6e3a319291aa975b"}]
