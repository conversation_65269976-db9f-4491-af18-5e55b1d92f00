/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=false!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=false! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ThemeProvider.tsx */ \"(app-pages-browser)/./src/components/ThemeProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js ***!
  \*********************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{\"use strict\";var e={529:(e,r,t)=>{var n=t(191);var i=Object.create(null);var a=typeof document===\"undefined\";var o=Array.prototype.forEach;function debounce(e,r){var t=0;return function(){var n=this;var i=arguments;var a=function functionCall(){return e.apply(n,i)};clearTimeout(t);t=setTimeout(a,r)}}function noop(){}function getCurrentScriptUrl(e){var r=i[e];if(!r){if(document.currentScript){r=document.currentScript.src}else{var t=document.getElementsByTagName(\"script\");var a=t[t.length-1];if(a){r=a.src}}i[e]=r}return function(e){if(!r){return null}var t=r.split(/([^\\\\/]+)\\.js$/);var i=t&&t[1];if(!i){return[r.replace(\".js\",\".css\")]}if(!e){return[r.replace(\".js\",\".css\")]}return e.split(\",\").map((function(e){var t=new RegExp(\"\".concat(i,\"\\\\.js$\"),\"g\");return n(r.replace(t,\"\".concat(e.replace(/{fileName}/g,i),\".css\")))}))}}function updateCss(e,r){if(!r){if(!e.href){return}r=e.href.split(\"?\")[0]}if(!isUrlRequest(r)){return}if(e.isLoaded===false){return}if(!r||!(r.indexOf(\".css\")>-1)){return}e.visited=true;var t=e.cloneNode();t.isLoaded=false;t.addEventListener(\"load\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.addEventListener(\"error\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.href=\"\".concat(r,\"?\").concat(Date.now());if(e.nextSibling){e.parentNode.insertBefore(t,e.nextSibling)}else{e.parentNode.appendChild(t)}}function getReloadUrl(e,r){var t;e=n(e,{stripWWW:false});r.some((function(n){if(e.indexOf(r)>-1){t=n}}));return t}function reloadStyle(e){if(!e){return false}var r=document.querySelectorAll(\"link\");var t=false;o.call(r,(function(r){if(!r.href){return}var n=getReloadUrl(r.href,e);if(!isUrlRequest(n)){return}if(r.visited===true){return}if(n){updateCss(r,n);t=true}}));return t}function reloadAll(){var e=document.querySelectorAll(\"link\");o.call(e,(function(e){if(e.visited===true){return}updateCss(e)}))}function isUrlRequest(e){if(!/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.test(e)){return false}return true}e.exports=function(e,r){if(a){console.log(\"no window.document found, will not HMR CSS\");return noop}var t=getCurrentScriptUrl(e);function update(){var e=t(r.filename);var n=reloadStyle(e);if(r.locals){console.log(\"[HMR] Detected local css modules. Reload all css\");reloadAll();return}if(n){console.log(\"[HMR] css reload %s\",e.join(\" \"))}else{console.log(\"[HMR] Reload all css\");reloadAll()}}return debounce(update,50)}},191:e=>{function normalizeUrl(e){return e.reduce((function(e,r){switch(r){case\"..\":e.pop();break;case\".\":break;default:e.push(r)}return e}),[]).join(\"/\")}e.exports=function(e){e=e.trim();if(/^data:/i.test(e)){return e}var r=e.indexOf(\"//\")!==-1?e.split(\"//\")[0]+\"//\":\"\";var t=e.replace(new RegExp(r,\"i\"),\"\").split(\"/\");var n=t[0].toLowerCase().replace(/\\.$/,\"\");t[0]=\"\";var i=normalizeUrl(t);return r+n+i}}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var i=r[t]={exports:{}};var a=true;try{e[t](i,i.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(529);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWE7QUFDYixLQUFxQztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWCwrQ0FBK0MsNkJBQTZCO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsZ0JBQWdCLGdEQUFnRDtBQUNoRSxnQkFBZ0IsYUFBYTtBQUM3QjtBQUNBO0FBQ0EsZ0NBQWdDLGtDQUFrQyxPQUFPO0FBQ3pFO0FBQ0EsZ0dBQWdHLFNBQVMsVUFBVSxzRkFBc0YsYUFBYSxVQUFVLFVBQVU7QUFDMU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsbUJBQU8sQ0FBQyxzR0FBMEI7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLGdCQUFnQjtBQUNwQixJQUFJLGNBQWM7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHh5aWVuXFxPbmVEcml2ZVxcRGVza3RvcFxcQWxsSm91cm5hbFxcam91cm5hbC1hcHBcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxjanNcXHJlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIFJlYWN0XG4gKiByZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanNcbiAqXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cblwidXNlIHN0cmljdFwiO1xuXCJwcm9kdWN0aW9uXCIgIT09IHByb2Nlc3MuZW52Lk5PREVfRU5WICYmXG4gIChmdW5jdGlvbiAoKSB7XG4gICAgZnVuY3Rpb24gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUpIHtcbiAgICAgIGlmIChudWxsID09IHR5cGUpIHJldHVybiBudWxsO1xuICAgICAgaWYgKFwiZnVuY3Rpb25cIiA9PT0gdHlwZW9mIHR5cGUpXG4gICAgICAgIHJldHVybiB0eXBlLiQkdHlwZW9mID09PSBSRUFDVF9DTElFTlRfUkVGRVJFTkNFXG4gICAgICAgICAgPyBudWxsXG4gICAgICAgICAgOiB0eXBlLmRpc3BsYXlOYW1lIHx8IHR5cGUubmFtZSB8fCBudWxsO1xuICAgICAgaWYgKFwic3RyaW5nXCIgPT09IHR5cGVvZiB0eXBlKSByZXR1cm4gdHlwZTtcbiAgICAgIHN3aXRjaCAodHlwZSkge1xuICAgICAgICBjYXNlIFJFQUNUX0ZSQUdNRU5UX1RZUEU6XG4gICAgICAgICAgcmV0dXJuIFwiRnJhZ21lbnRcIjtcbiAgICAgICAgY2FzZSBSRUFDVF9QUk9GSUxFUl9UWVBFOlxuICAgICAgICAgIHJldHVybiBcIlByb2ZpbGVyXCI7XG4gICAgICAgIGNhc2UgUkVBQ1RfU1RSSUNUX01PREVfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJTdHJpY3RNb2RlXCI7XG4gICAgICAgIGNhc2UgUkVBQ1RfU1VTUEVOU0VfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJTdXNwZW5zZVwiO1xuICAgICAgICBjYXNlIFJFQUNUX1NVU1BFTlNFX0xJU1RfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJTdXNwZW5zZUxpc3RcIjtcbiAgICAgICAgY2FzZSBSRUFDVF9BQ1RJVklUWV9UWVBFOlxuICAgICAgICAgIHJldHVybiBcIkFjdGl2aXR5XCI7XG4gICAgICB9XG4gICAgICBpZiAoXCJvYmplY3RcIiA9PT0gdHlwZW9mIHR5cGUpXG4gICAgICAgIHN3aXRjaCAoXG4gICAgICAgICAgKFwibnVtYmVyXCIgPT09IHR5cGVvZiB0eXBlLnRhZyAmJlxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICAgICAgXCJSZWNlaXZlZCBhbiB1bmV4cGVjdGVkIG9iamVjdCBpbiBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUoKS4gVGhpcyBpcyBsaWtlbHkgYSBidWcgaW4gUmVhY3QuIFBsZWFzZSBmaWxlIGFuIGlzc3VlLlwiXG4gICAgICAgICAgICApLFxuICAgICAgICAgIHR5cGUuJCR0eXBlb2YpXG4gICAgICAgICkge1xuICAgICAgICAgIGNhc2UgUkVBQ1RfUE9SVEFMX1RZUEU6XG4gICAgICAgICAgICByZXR1cm4gXCJQb3J0YWxcIjtcbiAgICAgICAgICBjYXNlIFJFQUNUX0NPTlRFWFRfVFlQRTpcbiAgICAgICAgICAgIHJldHVybiAodHlwZS5kaXNwbGF5TmFtZSB8fCBcIkNvbnRleHRcIikgKyBcIi5Qcm92aWRlclwiO1xuICAgICAgICAgIGNhc2UgUkVBQ1RfQ09OU1VNRVJfVFlQRTpcbiAgICAgICAgICAgIHJldHVybiAodHlwZS5fY29udGV4dC5kaXNwbGF5TmFtZSB8fCBcIkNvbnRleHRcIikgKyBcIi5Db25zdW1lclwiO1xuICAgICAgICAgIGNhc2UgUkVBQ1RfRk9SV0FSRF9SRUZfVFlQRTpcbiAgICAgICAgICAgIHZhciBpbm5lclR5cGUgPSB0eXBlLnJlbmRlcjtcbiAgICAgICAgICAgIHR5cGUgPSB0eXBlLmRpc3BsYXlOYW1lO1xuICAgICAgICAgICAgdHlwZSB8fFxuICAgICAgICAgICAgICAoKHR5cGUgPSBpbm5lclR5cGUuZGlzcGxheU5hbWUgfHwgaW5uZXJUeXBlLm5hbWUgfHwgXCJcIiksXG4gICAgICAgICAgICAgICh0eXBlID0gXCJcIiAhPT0gdHlwZSA/IFwiRm9yd2FyZFJlZihcIiArIHR5cGUgKyBcIilcIiA6IFwiRm9yd2FyZFJlZlwiKSk7XG4gICAgICAgICAgICByZXR1cm4gdHlwZTtcbiAgICAgICAgICBjYXNlIFJFQUNUX01FTU9fVFlQRTpcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgIChpbm5lclR5cGUgPSB0eXBlLmRpc3BsYXlOYW1lIHx8IG51bGwpLFxuICAgICAgICAgICAgICBudWxsICE9PSBpbm5lclR5cGVcbiAgICAgICAgICAgICAgICA/IGlubmVyVHlwZVxuICAgICAgICAgICAgICAgIDogZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUudHlwZSkgfHwgXCJNZW1vXCJcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgY2FzZSBSRUFDVF9MQVpZX1RZUEU6XG4gICAgICAgICAgICBpbm5lclR5cGUgPSB0eXBlLl9wYXlsb2FkO1xuICAgICAgICAgICAgdHlwZSA9IHR5cGUuX2luaXQ7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICByZXR1cm4gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUoaW5uZXJUeXBlKSk7XG4gICAgICAgICAgICB9IGNhdGNoICh4KSB7fVxuICAgICAgICB9XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gICAgZnVuY3Rpb24gdGVzdFN0cmluZ0NvZXJjaW9uKHZhbHVlKSB7XG4gICAgICByZXR1cm4gXCJcIiArIHZhbHVlO1xuICAgIH1cbiAgICBmdW5jdGlvbiBjaGVja0tleVN0cmluZ0NvZXJjaW9uKHZhbHVlKSB7XG4gICAgICB0cnkge1xuICAgICAgICB0ZXN0U3RyaW5nQ29lcmNpb24odmFsdWUpO1xuICAgICAgICB2YXIgSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0ID0gITE7XG4gICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCA9ICEwO1xuICAgICAgfVxuICAgICAgaWYgKEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCkge1xuICAgICAgICBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQgPSBjb25zb2xlO1xuICAgICAgICB2YXIgSlNDb21waWxlcl90ZW1wX2NvbnN0ID0gSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0LmVycm9yO1xuICAgICAgICB2YXIgSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0JGpzY29tcCQwID1cbiAgICAgICAgICAoXCJmdW5jdGlvblwiID09PSB0eXBlb2YgU3ltYm9sICYmXG4gICAgICAgICAgICBTeW1ib2wudG9TdHJpbmdUYWcgJiZcbiAgICAgICAgICAgIHZhbHVlW1N5bWJvbC50b1N0cmluZ1RhZ10pIHx8XG4gICAgICAgICAgdmFsdWUuY29uc3RydWN0b3IubmFtZSB8fFxuICAgICAgICAgIFwiT2JqZWN0XCI7XG4gICAgICAgIEpTQ29tcGlsZXJfdGVtcF9jb25zdC5jYWxsKFxuICAgICAgICAgIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCxcbiAgICAgICAgICBcIlRoZSBwcm92aWRlZCBrZXkgaXMgYW4gdW5zdXBwb3J0ZWQgdHlwZSAlcy4gVGhpcyB2YWx1ZSBtdXN0IGJlIGNvZXJjZWQgdG8gYSBzdHJpbmcgYmVmb3JlIHVzaW5nIGl0IGhlcmUuXCIsXG4gICAgICAgICAgSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0JGpzY29tcCQwXG4gICAgICAgICk7XG4gICAgICAgIHJldHVybiB0ZXN0U3RyaW5nQ29lcmNpb24odmFsdWUpO1xuICAgICAgfVxuICAgIH1cbiAgICBmdW5jdGlvbiBnZXRUYXNrTmFtZSh0eXBlKSB7XG4gICAgICBpZiAodHlwZSA9PT0gUkVBQ1RfRlJBR01FTlRfVFlQRSkgcmV0dXJuIFwiPD5cIjtcbiAgICAgIGlmIChcbiAgICAgICAgXCJvYmplY3RcIiA9PT0gdHlwZW9mIHR5cGUgJiZcbiAgICAgICAgbnVsbCAhPT0gdHlwZSAmJlxuICAgICAgICB0eXBlLiQkdHlwZW9mID09PSBSRUFDVF9MQVpZX1RZUEVcbiAgICAgIClcbiAgICAgICAgcmV0dXJuIFwiPC4uLj5cIjtcbiAgICAgIHRyeSB7XG4gICAgICAgIHZhciBuYW1lID0gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUpO1xuICAgICAgICByZXR1cm4gbmFtZSA/IFwiPFwiICsgbmFtZSArIFwiPlwiIDogXCI8Li4uPlwiO1xuICAgICAgfSBjYXRjaCAoeCkge1xuICAgICAgICByZXR1cm4gXCI8Li4uPlwiO1xuICAgICAgfVxuICAgIH1cbiAgICBmdW5jdGlvbiBnZXRPd25lcigpIHtcbiAgICAgIHZhciBkaXNwYXRjaGVyID0gUmVhY3RTaGFyZWRJbnRlcm5hbHMuQTtcbiAgICAgIHJldHVybiBudWxsID09PSBkaXNwYXRjaGVyID8gbnVsbCA6IGRpc3BhdGNoZXIuZ2V0T3duZXIoKTtcbiAgICB9XG4gICAgZnVuY3Rpb24gVW5rbm93bk93bmVyKCkge1xuICAgICAgcmV0dXJuIEVycm9yKFwicmVhY3Qtc3RhY2stdG9wLWZyYW1lXCIpO1xuICAgIH1cbiAgICBmdW5jdGlvbiBoYXNWYWxpZEtleShjb25maWcpIHtcbiAgICAgIGlmIChoYXNPd25Qcm9wZXJ0eS5jYWxsKGNvbmZpZywgXCJrZXlcIikpIHtcbiAgICAgICAgdmFyIGdldHRlciA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IoY29uZmlnLCBcImtleVwiKS5nZXQ7XG4gICAgICAgIGlmIChnZXR0ZXIgJiYgZ2V0dGVyLmlzUmVhY3RXYXJuaW5nKSByZXR1cm4gITE7XG4gICAgICB9XG4gICAgICByZXR1cm4gdm9pZCAwICE9PSBjb25maWcua2V5O1xuICAgIH1cbiAgICBmdW5jdGlvbiBkZWZpbmVLZXlQcm9wV2FybmluZ0dldHRlcihwcm9wcywgZGlzcGxheU5hbWUpIHtcbiAgICAgIGZ1bmN0aW9uIHdhcm5BYm91dEFjY2Vzc2luZ0tleSgpIHtcbiAgICAgICAgc3BlY2lhbFByb3BLZXlXYXJuaW5nU2hvd24gfHxcbiAgICAgICAgICAoKHNwZWNpYWxQcm9wS2V5V2FybmluZ1Nob3duID0gITApLFxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgICBcIiVzOiBga2V5YCBpcyBub3QgYSBwcm9wLiBUcnlpbmcgdG8gYWNjZXNzIGl0IHdpbGwgcmVzdWx0IGluIGB1bmRlZmluZWRgIGJlaW5nIHJldHVybmVkLiBJZiB5b3UgbmVlZCB0byBhY2Nlc3MgdGhlIHNhbWUgdmFsdWUgd2l0aGluIHRoZSBjaGlsZCBjb21wb25lbnQsIHlvdSBzaG91bGQgcGFzcyBpdCBhcyBhIGRpZmZlcmVudCBwcm9wLiAoaHR0cHM6Ly9yZWFjdC5kZXYvbGluay9zcGVjaWFsLXByb3BzKVwiLFxuICAgICAgICAgICAgZGlzcGxheU5hbWVcbiAgICAgICAgICApKTtcbiAgICAgIH1cbiAgICAgIHdhcm5BYm91dEFjY2Vzc2luZ0tleS5pc1JlYWN0V2FybmluZyA9ICEwO1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHByb3BzLCBcImtleVwiLCB7XG4gICAgICAgIGdldDogd2FybkFib3V0QWNjZXNzaW5nS2V5LFxuICAgICAgICBjb25maWd1cmFibGU6ICEwXG4gICAgICB9KTtcbiAgICB9XG4gICAgZnVuY3Rpb24gZWxlbWVudFJlZkdldHRlcldpdGhEZXByZWNhdGlvbldhcm5pbmcoKSB7XG4gICAgICB2YXIgY29tcG9uZW50TmFtZSA9IGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0aGlzLnR5cGUpO1xuICAgICAgZGlkV2FybkFib3V0RWxlbWVudFJlZltjb21wb25lbnROYW1lXSB8fFxuICAgICAgICAoKGRpZFdhcm5BYm91dEVsZW1lbnRSZWZbY29tcG9uZW50TmFtZV0gPSAhMCksXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgXCJBY2Nlc3NpbmcgZWxlbWVudC5yZWYgd2FzIHJlbW92ZWQgaW4gUmVhY3QgMTkuIHJlZiBpcyBub3cgYSByZWd1bGFyIHByb3AuIEl0IHdpbGwgYmUgcmVtb3ZlZCBmcm9tIHRoZSBKU1ggRWxlbWVudCB0eXBlIGluIGEgZnV0dXJlIHJlbGVhc2UuXCJcbiAgICAgICAgKSk7XG4gICAgICBjb21wb25lbnROYW1lID0gdGhpcy5wcm9wcy5yZWY7XG4gICAgICByZXR1cm4gdm9pZCAwICE9PSBjb21wb25lbnROYW1lID8gY29tcG9uZW50TmFtZSA6IG51bGw7XG4gICAgfVxuICAgIGZ1bmN0aW9uIFJlYWN0RWxlbWVudChcbiAgICAgIHR5cGUsXG4gICAgICBrZXksXG4gICAgICBzZWxmLFxuICAgICAgc291cmNlLFxuICAgICAgb3duZXIsXG4gICAgICBwcm9wcyxcbiAgICAgIGRlYnVnU3RhY2ssXG4gICAgICBkZWJ1Z1Rhc2tcbiAgICApIHtcbiAgICAgIHNlbGYgPSBwcm9wcy5yZWY7XG4gICAgICB0eXBlID0ge1xuICAgICAgICAkJHR5cGVvZjogUkVBQ1RfRUxFTUVOVF9UWVBFLFxuICAgICAgICB0eXBlOiB0eXBlLFxuICAgICAgICBrZXk6IGtleSxcbiAgICAgICAgcHJvcHM6IHByb3BzLFxuICAgICAgICBfb3duZXI6IG93bmVyXG4gICAgICB9O1xuICAgICAgbnVsbCAhPT0gKHZvaWQgMCAhPT0gc2VsZiA/IHNlbGYgOiBudWxsKVxuICAgICAgICA/IE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0eXBlLCBcInJlZlwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiAhMSxcbiAgICAgICAgICAgIGdldDogZWxlbWVudFJlZkdldHRlcldpdGhEZXByZWNhdGlvbldhcm5pbmdcbiAgICAgICAgICB9KVxuICAgICAgICA6IE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0eXBlLCBcInJlZlwiLCB7IGVudW1lcmFibGU6ICExLCB2YWx1ZTogbnVsbCB9KTtcbiAgICAgIHR5cGUuX3N0b3JlID0ge307XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodHlwZS5fc3RvcmUsIFwidmFsaWRhdGVkXCIsIHtcbiAgICAgICAgY29uZmlndXJhYmxlOiAhMSxcbiAgICAgICAgZW51bWVyYWJsZTogITEsXG4gICAgICAgIHdyaXRhYmxlOiAhMCxcbiAgICAgICAgdmFsdWU6IDBcbiAgICAgIH0pO1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUsIFwiX2RlYnVnSW5mb1wiLCB7XG4gICAgICAgIGNvbmZpZ3VyYWJsZTogITEsXG4gICAgICAgIGVudW1lcmFibGU6ICExLFxuICAgICAgICB3cml0YWJsZTogITAsXG4gICAgICAgIHZhbHVlOiBudWxsXG4gICAgICB9KTtcbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0eXBlLCBcIl9kZWJ1Z1N0YWNrXCIsIHtcbiAgICAgICAgY29uZmlndXJhYmxlOiAhMSxcbiAgICAgICAgZW51bWVyYWJsZTogITEsXG4gICAgICAgIHdyaXRhYmxlOiAhMCxcbiAgICAgICAgdmFsdWU6IGRlYnVnU3RhY2tcbiAgICAgIH0pO1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUsIFwiX2RlYnVnVGFza1wiLCB7XG4gICAgICAgIGNvbmZpZ3VyYWJsZTogITEsXG4gICAgICAgIGVudW1lcmFibGU6ICExLFxuICAgICAgICB3cml0YWJsZTogITAsXG4gICAgICAgIHZhbHVlOiBkZWJ1Z1Rhc2tcbiAgICAgIH0pO1xuICAgICAgT2JqZWN0LmZyZWV6ZSAmJiAoT2JqZWN0LmZyZWV6ZSh0eXBlLnByb3BzKSwgT2JqZWN0LmZyZWV6ZSh0eXBlKSk7XG4gICAgICByZXR1cm4gdHlwZTtcbiAgICB9XG4gICAgZnVuY3Rpb24ganN4REVWSW1wbChcbiAgICAgIHR5cGUsXG4gICAgICBjb25maWcsXG4gICAgICBtYXliZUtleSxcbiAgICAgIGlzU3RhdGljQ2hpbGRyZW4sXG4gICAgICBzb3VyY2UsXG4gICAgICBzZWxmLFxuICAgICAgZGVidWdTdGFjayxcbiAgICAgIGRlYnVnVGFza1xuICAgICkge1xuICAgICAgdmFyIGNoaWxkcmVuID0gY29uZmlnLmNoaWxkcmVuO1xuICAgICAgaWYgKHZvaWQgMCAhPT0gY2hpbGRyZW4pXG4gICAgICAgIGlmIChpc1N0YXRpY0NoaWxkcmVuKVxuICAgICAgICAgIGlmIChpc0FycmF5SW1wbChjaGlsZHJlbikpIHtcbiAgICAgICAgICAgIGZvciAoXG4gICAgICAgICAgICAgIGlzU3RhdGljQ2hpbGRyZW4gPSAwO1xuICAgICAgICAgICAgICBpc1N0YXRpY0NoaWxkcmVuIDwgY2hpbGRyZW4ubGVuZ3RoO1xuICAgICAgICAgICAgICBpc1N0YXRpY0NoaWxkcmVuKytcbiAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgdmFsaWRhdGVDaGlsZEtleXMoY2hpbGRyZW5baXNTdGF0aWNDaGlsZHJlbl0pO1xuICAgICAgICAgICAgT2JqZWN0LmZyZWV6ZSAmJiBPYmplY3QuZnJlZXplKGNoaWxkcmVuKTtcbiAgICAgICAgICB9IGVsc2VcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgICAgIFwiUmVhY3QuanN4OiBTdGF0aWMgY2hpbGRyZW4gc2hvdWxkIGFsd2F5cyBiZSBhbiBhcnJheS4gWW91IGFyZSBsaWtlbHkgZXhwbGljaXRseSBjYWxsaW5nIFJlYWN0LmpzeHMgb3IgUmVhY3QuanN4REVWLiBVc2UgdGhlIEJhYmVsIHRyYW5zZm9ybSBpbnN0ZWFkLlwiXG4gICAgICAgICAgICApO1xuICAgICAgICBlbHNlIHZhbGlkYXRlQ2hpbGRLZXlzKGNoaWxkcmVuKTtcbiAgICAgIGlmIChoYXNPd25Qcm9wZXJ0eS5jYWxsKGNvbmZpZywgXCJrZXlcIikpIHtcbiAgICAgICAgY2hpbGRyZW4gPSBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUodHlwZSk7XG4gICAgICAgIHZhciBrZXlzID0gT2JqZWN0LmtleXMoY29uZmlnKS5maWx0ZXIoZnVuY3Rpb24gKGspIHtcbiAgICAgICAgICByZXR1cm4gXCJrZXlcIiAhPT0gaztcbiAgICAgICAgfSk7XG4gICAgICAgIGlzU3RhdGljQ2hpbGRyZW4gPVxuICAgICAgICAgIDAgPCBrZXlzLmxlbmd0aFxuICAgICAgICAgICAgPyBcIntrZXk6IHNvbWVLZXksIFwiICsga2V5cy5qb2luKFwiOiAuLi4sIFwiKSArIFwiOiAuLi59XCJcbiAgICAgICAgICAgIDogXCJ7a2V5OiBzb21lS2V5fVwiO1xuICAgICAgICBkaWRXYXJuQWJvdXRLZXlTcHJlYWRbY2hpbGRyZW4gKyBpc1N0YXRpY0NoaWxkcmVuXSB8fFxuICAgICAgICAgICgoa2V5cyA9XG4gICAgICAgICAgICAwIDwga2V5cy5sZW5ndGggPyBcIntcIiArIGtleXMuam9pbihcIjogLi4uLCBcIikgKyBcIjogLi4ufVwiIDogXCJ7fVwiKSxcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgICAgJ0EgcHJvcHMgb2JqZWN0IGNvbnRhaW5pbmcgYSBcImtleVwiIHByb3AgaXMgYmVpbmcgc3ByZWFkIGludG8gSlNYOlxcbiAgbGV0IHByb3BzID0gJXM7XFxuICA8JXMgey4uLnByb3BzfSAvPlxcblJlYWN0IGtleXMgbXVzdCBiZSBwYXNzZWQgZGlyZWN0bHkgdG8gSlNYIHdpdGhvdXQgdXNpbmcgc3ByZWFkOlxcbiAgbGV0IHByb3BzID0gJXM7XFxuICA8JXMga2V5PXtzb21lS2V5fSB7Li4ucHJvcHN9IC8+JyxcbiAgICAgICAgICAgIGlzU3RhdGljQ2hpbGRyZW4sXG4gICAgICAgICAgICBjaGlsZHJlbixcbiAgICAgICAgICAgIGtleXMsXG4gICAgICAgICAgICBjaGlsZHJlblxuICAgICAgICAgICksXG4gICAgICAgICAgKGRpZFdhcm5BYm91dEtleVNwcmVhZFtjaGlsZHJlbiArIGlzU3RhdGljQ2hpbGRyZW5dID0gITApKTtcbiAgICAgIH1cbiAgICAgIGNoaWxkcmVuID0gbnVsbDtcbiAgICAgIHZvaWQgMCAhPT0gbWF5YmVLZXkgJiZcbiAgICAgICAgKGNoZWNrS2V5U3RyaW5nQ29lcmNpb24obWF5YmVLZXkpLCAoY2hpbGRyZW4gPSBcIlwiICsgbWF5YmVLZXkpKTtcbiAgICAgIGhhc1ZhbGlkS2V5KGNvbmZpZykgJiZcbiAgICAgICAgKGNoZWNrS2V5U3RyaW5nQ29lcmNpb24oY29uZmlnLmtleSksIChjaGlsZHJlbiA9IFwiXCIgKyBjb25maWcua2V5KSk7XG4gICAgICBpZiAoXCJrZXlcIiBpbiBjb25maWcpIHtcbiAgICAgICAgbWF5YmVLZXkgPSB7fTtcbiAgICAgICAgZm9yICh2YXIgcHJvcE5hbWUgaW4gY29uZmlnKVxuICAgICAgICAgIFwia2V5XCIgIT09IHByb3BOYW1lICYmIChtYXliZUtleVtwcm9wTmFtZV0gPSBjb25maWdbcHJvcE5hbWVdKTtcbiAgICAgIH0gZWxzZSBtYXliZUtleSA9IGNvbmZpZztcbiAgICAgIGNoaWxkcmVuICYmXG4gICAgICAgIGRlZmluZUtleVByb3BXYXJuaW5nR2V0dGVyKFxuICAgICAgICAgIG1heWJlS2V5LFxuICAgICAgICAgIFwiZnVuY3Rpb25cIiA9PT0gdHlwZW9mIHR5cGVcbiAgICAgICAgICAgID8gdHlwZS5kaXNwbGF5TmFtZSB8fCB0eXBlLm5hbWUgfHwgXCJVbmtub3duXCJcbiAgICAgICAgICAgIDogdHlwZVxuICAgICAgICApO1xuICAgICAgcmV0dXJuIFJlYWN0RWxlbWVudChcbiAgICAgICAgdHlwZSxcbiAgICAgICAgY2hpbGRyZW4sXG4gICAgICAgIHNlbGYsXG4gICAgICAgIHNvdXJjZSxcbiAgICAgICAgZ2V0T3duZXIoKSxcbiAgICAgICAgbWF5YmVLZXksXG4gICAgICAgIGRlYnVnU3RhY2ssXG4gICAgICAgIGRlYnVnVGFza1xuICAgICAgKTtcbiAgICB9XG4gICAgZnVuY3Rpb24gdmFsaWRhdGVDaGlsZEtleXMobm9kZSkge1xuICAgICAgXCJvYmplY3RcIiA9PT0gdHlwZW9mIG5vZGUgJiZcbiAgICAgICAgbnVsbCAhPT0gbm9kZSAmJlxuICAgICAgICBub2RlLiQkdHlwZW9mID09PSBSRUFDVF9FTEVNRU5UX1RZUEUgJiZcbiAgICAgICAgbm9kZS5fc3RvcmUgJiZcbiAgICAgICAgKG5vZGUuX3N0b3JlLnZhbGlkYXRlZCA9IDEpO1xuICAgIH1cbiAgICB2YXIgUmVhY3QgPSByZXF1aXJlKFwibmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0XCIpLFxuICAgICAgUkVBQ1RfRUxFTUVOVF9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LnRyYW5zaXRpb25hbC5lbGVtZW50XCIpLFxuICAgICAgUkVBQ1RfUE9SVEFMX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QucG9ydGFsXCIpLFxuICAgICAgUkVBQ1RfRlJBR01FTlRfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5mcmFnbWVudFwiKSxcbiAgICAgIFJFQUNUX1NUUklDVF9NT0RFX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3Quc3RyaWN0X21vZGVcIiksXG4gICAgICBSRUFDVF9QUk9GSUxFUl9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LnByb2ZpbGVyXCIpO1xuICAgIFN5bWJvbC5mb3IoXCJyZWFjdC5wcm92aWRlclwiKTtcbiAgICB2YXIgUkVBQ1RfQ09OU1VNRVJfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5jb25zdW1lclwiKSxcbiAgICAgIFJFQUNUX0NPTlRFWFRfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5jb250ZXh0XCIpLFxuICAgICAgUkVBQ1RfRk9SV0FSRF9SRUZfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5mb3J3YXJkX3JlZlwiKSxcbiAgICAgIFJFQUNUX1NVU1BFTlNFX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3Quc3VzcGVuc2VcIiksXG4gICAgICBSRUFDVF9TVVNQRU5TRV9MSVNUX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3Quc3VzcGVuc2VfbGlzdFwiKSxcbiAgICAgIFJFQUNUX01FTU9fVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5tZW1vXCIpLFxuICAgICAgUkVBQ1RfTEFaWV9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmxhenlcIiksXG4gICAgICBSRUFDVF9BQ1RJVklUWV9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmFjdGl2aXR5XCIpLFxuICAgICAgUkVBQ1RfQ0xJRU5UX1JFRkVSRU5DRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5jbGllbnQucmVmZXJlbmNlXCIpLFxuICAgICAgUmVhY3RTaGFyZWRJbnRlcm5hbHMgPVxuICAgICAgICBSZWFjdC5fX0NMSUVOVF9JTlRFUk5BTFNfRE9fTk9UX1VTRV9PUl9XQVJOX1VTRVJTX1RIRVlfQ0FOTk9UX1VQR1JBREUsXG4gICAgICBoYXNPd25Qcm9wZXJ0eSA9IE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHksXG4gICAgICBpc0FycmF5SW1wbCA9IEFycmF5LmlzQXJyYXksXG4gICAgICBjcmVhdGVUYXNrID0gY29uc29sZS5jcmVhdGVUYXNrXG4gICAgICAgID8gY29uc29sZS5jcmVhdGVUYXNrXG4gICAgICAgIDogZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgfTtcbiAgICBSZWFjdCA9IHtcbiAgICAgIFwicmVhY3Qtc3RhY2stYm90dG9tLWZyYW1lXCI6IGZ1bmN0aW9uIChjYWxsU3RhY2tGb3JFcnJvcikge1xuICAgICAgICByZXR1cm4gY2FsbFN0YWNrRm9yRXJyb3IoKTtcbiAgICAgIH1cbiAgICB9O1xuICAgIHZhciBzcGVjaWFsUHJvcEtleVdhcm5pbmdTaG93bjtcbiAgICB2YXIgZGlkV2FybkFib3V0RWxlbWVudFJlZiA9IHt9O1xuICAgIHZhciB1bmtub3duT3duZXJEZWJ1Z1N0YWNrID0gUmVhY3RbXCJyZWFjdC1zdGFjay1ib3R0b20tZnJhbWVcIl0uYmluZChcbiAgICAgIFJlYWN0LFxuICAgICAgVW5rbm93bk93bmVyXG4gICAgKSgpO1xuICAgIHZhciB1bmtub3duT3duZXJEZWJ1Z1Rhc2sgPSBjcmVhdGVUYXNrKGdldFRhc2tOYW1lKFVua25vd25Pd25lcikpO1xuICAgIHZhciBkaWRXYXJuQWJvdXRLZXlTcHJlYWQgPSB7fTtcbiAgICBleHBvcnRzLkZyYWdtZW50ID0gUkVBQ1RfRlJBR01FTlRfVFlQRTtcbiAgICBleHBvcnRzLmpzeERFViA9IGZ1bmN0aW9uIChcbiAgICAgIHR5cGUsXG4gICAgICBjb25maWcsXG4gICAgICBtYXliZUtleSxcbiAgICAgIGlzU3RhdGljQ2hpbGRyZW4sXG4gICAgICBzb3VyY2UsXG4gICAgICBzZWxmXG4gICAgKSB7XG4gICAgICB2YXIgdHJhY2tBY3R1YWxPd25lciA9XG4gICAgICAgIDFlNCA+IFJlYWN0U2hhcmVkSW50ZXJuYWxzLnJlY2VudGx5Q3JlYXRlZE93bmVyU3RhY2tzKys7XG4gICAgICByZXR1cm4ganN4REVWSW1wbChcbiAgICAgICAgdHlwZSxcbiAgICAgICAgY29uZmlnLFxuICAgICAgICBtYXliZUtleSxcbiAgICAgICAgaXNTdGF0aWNDaGlsZHJlbixcbiAgICAgICAgc291cmNlLFxuICAgICAgICBzZWxmLFxuICAgICAgICB0cmFja0FjdHVhbE93bmVyXG4gICAgICAgICAgPyBFcnJvcihcInJlYWN0LXN0YWNrLXRvcC1mcmFtZVwiKVxuICAgICAgICAgIDogdW5rbm93bk93bmVyRGVidWdTdGFjayxcbiAgICAgICAgdHJhY2tBY3R1YWxPd25lciA/IGNyZWF0ZVRhc2soZ2V0VGFza05hbWUodHlwZSkpIDogdW5rbm93bk93bmVyRGVidWdUYXNrXG4gICAgICApO1xuICAgIH07XG4gIH0pKCk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xceHlpZW5cXE9uZURyaXZlXFxEZXNrdG9wXFxBbGxKb3VybmFsXFxqb3VybmFsLWFwcFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGpzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":
/*!**************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \**************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'Inter', 'Inter Fallback'\",\"fontStyle\":\"normal\"},\"className\":\"__className_e8ce0c\"};\n    if(true) {\n      // 1751858047449\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwic3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIkludGVyXCIsXCJhcmd1bWVudHNcIjpbe1wic3Vic2V0c1wiOltcImxhdGluXCJdfV0sXCJ2YXJpYWJsZU5hbWVcIjpcImludGVyXCJ9IiwibWFwcGluZ3MiOiJBQUFBO0FBQ0Esa0JBQWtCLFNBQVMsOERBQThEO0FBQ3pGLE9BQU8sSUFBVTtBQUNqQjtBQUNBLHNCQUFzQixtQkFBTyxDQUFDLHdNQUE0SSxjQUFjLHNEQUFzRDtBQUM5TyxNQUFNLFVBQVU7QUFDaEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHh5aWVuXFxPbmVEcml2ZVxcRGVza3RvcFxcQWxsSm91cm5hbFxcam91cm5hbC1hcHBcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZm9udFxcZ29vZ2xlXFx0YXJnZXQuY3NzP3tcInBhdGhcIjpcInNyY1xcYXBwXFxsYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIkludGVyXCIsXCJhcmd1bWVudHNcIjpbe1wic3Vic2V0c1wiOltcImxhdGluXCJdfV0sXCJ2YXJpYWJsZU5hbWVcIjpcImludGVyXCJ9fGFwcC1wYWdlcy1icm93c2VyIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGV4dHJhY3RlZCBieSBtaW5pLWNzcy1leHRyYWN0LXBsdWdpblxubW9kdWxlLmV4cG9ydHMgPSB7XCJzdHlsZVwiOntcImZvbnRGYW1pbHlcIjpcIidJbnRlcicsICdJbnRlciBGYWxsYmFjaydcIixcImZvbnRTdHlsZVwiOlwibm9ybWFsXCJ9LFwiY2xhc3NOYW1lXCI6XCJfX2NsYXNzTmFtZV9lOGNlMGNcIn07XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgLy8gMTc1MTg1ODA0NzQ0OVxuICAgICAgdmFyIGNzc1JlbG9hZCA9IHJlcXVpcmUoXCJDOi9Vc2Vycy94eWllbi9PbmVEcml2ZS9EZXNrdG9wL0FsbEpvdXJuYWwvam91cm5hbC1hcHAvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9taW5pLWNzcy1leHRyYWN0LXBsdWdpbi9obXIvaG90TW9kdWxlUmVwbGFjZW1lbnQuanNcIikobW9kdWxlLmlkLCB7XCJwdWJsaWNQYXRoXCI6XCIvX25leHQvXCIsXCJlc01vZHVsZVwiOmZhbHNlLFwibG9jYWxzXCI6dHJ1ZX0pO1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGNzc1JlbG9hZCk7XG4gICAgICBcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c4222462190d\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHh5aWVuXFxPbmVEcml2ZVxcRGVza3RvcFxcQWxsSm91cm5hbFxcam91cm5hbC1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImM0MjIyNDYyMTkwZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_localStorage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/localStorage */ \"(app-pages-browser)/./src/lib/localStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ useTheme,ThemeProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useTheme() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (!context) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n}\n_s(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction ThemeProvider(param) {\n    let { children } = param;\n    _s1();\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('system');\n    const [resolvedTheme, setResolvedTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load theme from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const storage = (0,_lib_localStorage__WEBPACK_IMPORTED_MODULE_2__.getLocalStorage)();\n            const settings = storage.getSettings();\n            setThemeState(settings.theme);\n            setMounted(true);\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // Update resolved theme when theme changes or system preference changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const updateResolvedTheme = {\n                \"ThemeProvider.useEffect.updateResolvedTheme\": ()=>{\n                    if (theme === 'system') {\n                        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n                        setResolvedTheme(systemTheme);\n                    } else {\n                        setResolvedTheme(theme);\n                    }\n                }\n            }[\"ThemeProvider.useEffect.updateResolvedTheme\"];\n            updateResolvedTheme();\n            // Listen for system theme changes\n            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            const handleChange = {\n                \"ThemeProvider.useEffect.handleChange\": ()=>{\n                    if (theme === 'system') {\n                        updateResolvedTheme();\n                    }\n                }\n            }[\"ThemeProvider.useEffect.handleChange\"];\n            mediaQuery.addEventListener('change', handleChange);\n            return ({\n                \"ThemeProvider.useEffect\": ()=>mediaQuery.removeEventListener('change', handleChange)\n            })[\"ThemeProvider.useEffect\"];\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme\n    ]);\n    // Apply theme to document\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            const root = document.documentElement;\n            // Remove existing theme classes\n            root.classList.remove('light', 'dark');\n            // Add current theme class\n            root.classList.add(resolvedTheme);\n            // Update meta theme-color for mobile browsers\n            const metaThemeColor = document.querySelector('meta[name=\"theme-color\"]');\n            if (metaThemeColor) {\n                metaThemeColor.setAttribute('content', resolvedTheme === 'dark' ? '#1f2937' : '#ffffff');\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        resolvedTheme,\n        mounted\n    ]);\n    const setTheme = (newTheme)=>{\n        setThemeState(newTheme);\n        // Save to localStorage\n        const storage = (0,_lib_localStorage__WEBPACK_IMPORTED_MODULE_2__.getLocalStorage)();\n        storage.saveSettings({\n            theme: newTheme\n        });\n    };\n    // Prevent hydration mismatch by not rendering until mounted\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-white\",\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\ThemeProvider.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            setTheme,\n            resolvedTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n_s1(ThemeProvider, \"xxT7/+P3EnWgAlCNimYjvcDEMbg=\");\n_c = ThemeProvider;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1RoZW1lUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRThFO0FBQ3pCO0FBVXJELE1BQU1NLDZCQUFlTCxvREFBYUEsQ0FBK0JNO0FBRTFELFNBQVNDOztJQUNkLE1BQU1DLFVBQVVQLGlEQUFVQSxDQUFDSTtJQUMzQixJQUFJLENBQUNHLFNBQVM7UUFDWixNQUFNLElBQUlDLE1BQU07SUFDbEI7SUFDQSxPQUFPRDtBQUNUO0dBTmdCRDtBQVlULFNBQVNHLGNBQWMsS0FBZ0M7UUFBaEMsRUFBRUMsUUFBUSxFQUFzQixHQUFoQzs7SUFDNUIsTUFBTSxDQUFDQyxPQUFPQyxjQUFjLEdBQUdWLCtDQUFRQSxDQUFRO0lBQy9DLE1BQU0sQ0FBQ1csZUFBZUMsaUJBQWlCLEdBQUdaLCtDQUFRQSxDQUFtQjtJQUNyRSxNQUFNLENBQUNhLFNBQVNDLFdBQVcsR0FBR2QsK0NBQVFBLENBQUM7SUFFdkMsd0NBQXdDO0lBQ3hDRCxnREFBU0E7bUNBQUM7WUFDUixNQUFNZ0IsVUFBVWQsa0VBQWVBO1lBQy9CLE1BQU1lLFdBQVdELFFBQVFFLFdBQVc7WUFDcENQLGNBQWNNLFNBQVNQLEtBQUs7WUFDNUJLLFdBQVc7UUFDYjtrQ0FBRyxFQUFFO0lBRUwsd0VBQXdFO0lBQ3hFZixnREFBU0E7bUNBQUM7WUFDUixNQUFNbUI7K0RBQXNCO29CQUMxQixJQUFJVCxVQUFVLFVBQVU7d0JBQ3RCLE1BQU1VLGNBQWNDLE9BQU9DLFVBQVUsQ0FBQyxnQ0FBZ0NDLE9BQU8sR0FBRyxTQUFTO3dCQUN6RlYsaUJBQWlCTztvQkFDbkIsT0FBTzt3QkFDTFAsaUJBQWlCSDtvQkFDbkI7Z0JBQ0Y7O1lBRUFTO1lBRUEsa0NBQWtDO1lBQ2xDLE1BQU1LLGFBQWFILE9BQU9DLFVBQVUsQ0FBQztZQUNyQyxNQUFNRzt3REFBZTtvQkFDbkIsSUFBSWYsVUFBVSxVQUFVO3dCQUN0QlM7b0JBQ0Y7Z0JBQ0Y7O1lBRUFLLFdBQVdFLGdCQUFnQixDQUFDLFVBQVVEO1lBQ3RDOzJDQUFPLElBQU1ELFdBQVdHLG1CQUFtQixDQUFDLFVBQVVGOztRQUN4RDtrQ0FBRztRQUFDZjtLQUFNO0lBRVYsMEJBQTBCO0lBQzFCVixnREFBU0E7bUNBQUM7WUFDUixJQUFJLENBQUNjLFNBQVM7WUFFZCxNQUFNYyxPQUFPQyxTQUFTQyxlQUFlO1lBRXJDLGdDQUFnQztZQUNoQ0YsS0FBS0csU0FBUyxDQUFDQyxNQUFNLENBQUMsU0FBUztZQUUvQiwwQkFBMEI7WUFDMUJKLEtBQUtHLFNBQVMsQ0FBQ0UsR0FBRyxDQUFDckI7WUFFbkIsOENBQThDO1lBQzlDLE1BQU1zQixpQkFBaUJMLFNBQVNNLGFBQWEsQ0FBQztZQUM5QyxJQUFJRCxnQkFBZ0I7Z0JBQ2xCQSxlQUFlRSxZQUFZLENBQUMsV0FBV3hCLGtCQUFrQixTQUFTLFlBQVk7WUFDaEY7UUFDRjtrQ0FBRztRQUFDQTtRQUFlRTtLQUFRO0lBRTNCLE1BQU11QixXQUFXLENBQUNDO1FBQ2hCM0IsY0FBYzJCO1FBRWQsdUJBQXVCO1FBQ3ZCLE1BQU10QixVQUFVZCxrRUFBZUE7UUFDL0JjLFFBQVF1QixZQUFZLENBQUM7WUFBRTdCLE9BQU80QjtRQUFTO0lBQ3pDO0lBRUEsNERBQTREO0lBQzVELElBQUksQ0FBQ3hCLFNBQVM7UUFDWixxQkFDRSw4REFBQzBCO1lBQUlDLFdBQVU7c0JBQ1poQzs7Ozs7O0lBR1A7SUFFQSxxQkFDRSw4REFBQ04sYUFBYXVDLFFBQVE7UUFBQ0MsT0FBTztZQUFFakM7WUFBTzJCO1lBQVV6QjtRQUFjO2tCQUM1REg7Ozs7OztBQUdQO0lBL0VnQkQ7S0FBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xceHlpZW5cXE9uZURyaXZlXFxEZXNrdG9wXFxBbGxKb3VybmFsXFxqb3VybmFsLWFwcFxcc3JjXFxjb21wb25lbnRzXFxUaGVtZVByb3ZpZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgZ2V0TG9jYWxTdG9yYWdlIH0gZnJvbSAnQC9saWIvbG9jYWxTdG9yYWdlJztcblxudHlwZSBUaGVtZSA9ICdsaWdodCcgfCAnZGFyaycgfCAnc3lzdGVtJztcblxuaW50ZXJmYWNlIFRoZW1lQ29udGV4dFR5cGUge1xuICB0aGVtZTogVGhlbWU7XG4gIHNldFRoZW1lOiAodGhlbWU6IFRoZW1lKSA9PiB2b2lkO1xuICByZXNvbHZlZFRoZW1lOiAnbGlnaHQnIHwgJ2RhcmsnO1xufVxuXG5jb25zdCBUaGVtZUNvbnRleHQgPSBjcmVhdGVDb250ZXh0PFRoZW1lQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZCk7XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VUaGVtZSgpIHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoVGhlbWVDb250ZXh0KTtcbiAgaWYgKCFjb250ZXh0KSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VUaGVtZSBtdXN0IGJlIHVzZWQgd2l0aGluIGEgVGhlbWVQcm92aWRlcicpO1xuICB9XG4gIHJldHVybiBjb250ZXh0O1xufVxuXG5pbnRlcmZhY2UgVGhlbWVQcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFRoZW1lUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiBUaGVtZVByb3ZpZGVyUHJvcHMpIHtcbiAgY29uc3QgW3RoZW1lLCBzZXRUaGVtZVN0YXRlXSA9IHVzZVN0YXRlPFRoZW1lPignc3lzdGVtJyk7XG4gIGNvbnN0IFtyZXNvbHZlZFRoZW1lLCBzZXRSZXNvbHZlZFRoZW1lXSA9IHVzZVN0YXRlPCdsaWdodCcgfCAnZGFyayc+KCdsaWdodCcpO1xuICBjb25zdCBbbW91bnRlZCwgc2V0TW91bnRlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gTG9hZCB0aGVtZSBmcm9tIGxvY2FsU3RvcmFnZSBvbiBtb3VudFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHN0b3JhZ2UgPSBnZXRMb2NhbFN0b3JhZ2UoKTtcbiAgICBjb25zdCBzZXR0aW5ncyA9IHN0b3JhZ2UuZ2V0U2V0dGluZ3MoKTtcbiAgICBzZXRUaGVtZVN0YXRlKHNldHRpbmdzLnRoZW1lKTtcbiAgICBzZXRNb3VudGVkKHRydWUpO1xuICB9LCBbXSk7XG5cbiAgLy8gVXBkYXRlIHJlc29sdmVkIHRoZW1lIHdoZW4gdGhlbWUgY2hhbmdlcyBvciBzeXN0ZW0gcHJlZmVyZW5jZSBjaGFuZ2VzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgdXBkYXRlUmVzb2x2ZWRUaGVtZSA9ICgpID0+IHtcbiAgICAgIGlmICh0aGVtZSA9PT0gJ3N5c3RlbScpIHtcbiAgICAgICAgY29uc3Qgc3lzdGVtVGhlbWUgPSB3aW5kb3cubWF0Y2hNZWRpYSgnKHByZWZlcnMtY29sb3Itc2NoZW1lOiBkYXJrKScpLm1hdGNoZXMgPyAnZGFyaycgOiAnbGlnaHQnO1xuICAgICAgICBzZXRSZXNvbHZlZFRoZW1lKHN5c3RlbVRoZW1lKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldFJlc29sdmVkVGhlbWUodGhlbWUpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICB1cGRhdGVSZXNvbHZlZFRoZW1lKCk7XG5cbiAgICAvLyBMaXN0ZW4gZm9yIHN5c3RlbSB0aGVtZSBjaGFuZ2VzXG4gICAgY29uc3QgbWVkaWFRdWVyeSA9IHdpbmRvdy5tYXRjaE1lZGlhKCcocHJlZmVycy1jb2xvci1zY2hlbWU6IGRhcmspJyk7XG4gICAgY29uc3QgaGFuZGxlQ2hhbmdlID0gKCkgPT4ge1xuICAgICAgaWYgKHRoZW1lID09PSAnc3lzdGVtJykge1xuICAgICAgICB1cGRhdGVSZXNvbHZlZFRoZW1lKCk7XG4gICAgICB9XG4gICAgfTtcblxuICAgIG1lZGlhUXVlcnkuYWRkRXZlbnRMaXN0ZW5lcignY2hhbmdlJywgaGFuZGxlQ2hhbmdlKTtcbiAgICByZXR1cm4gKCkgPT4gbWVkaWFRdWVyeS5yZW1vdmVFdmVudExpc3RlbmVyKCdjaGFuZ2UnLCBoYW5kbGVDaGFuZ2UpO1xuICB9LCBbdGhlbWVdKTtcblxuICAvLyBBcHBseSB0aGVtZSB0byBkb2N1bWVudFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghbW91bnRlZCkgcmV0dXJuO1xuXG4gICAgY29uc3Qgcm9vdCA9IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudDtcbiAgICBcbiAgICAvLyBSZW1vdmUgZXhpc3RpbmcgdGhlbWUgY2xhc3Nlc1xuICAgIHJvb3QuY2xhc3NMaXN0LnJlbW92ZSgnbGlnaHQnLCAnZGFyaycpO1xuICAgIFxuICAgIC8vIEFkZCBjdXJyZW50IHRoZW1lIGNsYXNzXG4gICAgcm9vdC5jbGFzc0xpc3QuYWRkKHJlc29sdmVkVGhlbWUpO1xuICAgIFxuICAgIC8vIFVwZGF0ZSBtZXRhIHRoZW1lLWNvbG9yIGZvciBtb2JpbGUgYnJvd3NlcnNcbiAgICBjb25zdCBtZXRhVGhlbWVDb2xvciA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJ21ldGFbbmFtZT1cInRoZW1lLWNvbG9yXCJdJyk7XG4gICAgaWYgKG1ldGFUaGVtZUNvbG9yKSB7XG4gICAgICBtZXRhVGhlbWVDb2xvci5zZXRBdHRyaWJ1dGUoJ2NvbnRlbnQnLCByZXNvbHZlZFRoZW1lID09PSAnZGFyaycgPyAnIzFmMjkzNycgOiAnI2ZmZmZmZicpO1xuICAgIH1cbiAgfSwgW3Jlc29sdmVkVGhlbWUsIG1vdW50ZWRdKTtcblxuICBjb25zdCBzZXRUaGVtZSA9IChuZXdUaGVtZTogVGhlbWUpID0+IHtcbiAgICBzZXRUaGVtZVN0YXRlKG5ld1RoZW1lKTtcbiAgICBcbiAgICAvLyBTYXZlIHRvIGxvY2FsU3RvcmFnZVxuICAgIGNvbnN0IHN0b3JhZ2UgPSBnZXRMb2NhbFN0b3JhZ2UoKTtcbiAgICBzdG9yYWdlLnNhdmVTZXR0aW5ncyh7IHRoZW1lOiBuZXdUaGVtZSB9KTtcbiAgfTtcblxuICAvLyBQcmV2ZW50IGh5ZHJhdGlvbiBtaXNtYXRjaCBieSBub3QgcmVuZGVyaW5nIHVudGlsIG1vdW50ZWRcbiAgaWYgKCFtb3VudGVkKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLXdoaXRlXCI+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxUaGVtZUNvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3sgdGhlbWUsIHNldFRoZW1lLCByZXNvbHZlZFRoZW1lIH19PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvVGhlbWVDb250ZXh0LlByb3ZpZGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsImdldExvY2FsU3RvcmFnZSIsIlRoZW1lQ29udGV4dCIsInVuZGVmaW5lZCIsInVzZVRoZW1lIiwiY29udGV4dCIsIkVycm9yIiwiVGhlbWVQcm92aWRlciIsImNoaWxkcmVuIiwidGhlbWUiLCJzZXRUaGVtZVN0YXRlIiwicmVzb2x2ZWRUaGVtZSIsInNldFJlc29sdmVkVGhlbWUiLCJtb3VudGVkIiwic2V0TW91bnRlZCIsInN0b3JhZ2UiLCJzZXR0aW5ncyIsImdldFNldHRpbmdzIiwidXBkYXRlUmVzb2x2ZWRUaGVtZSIsInN5c3RlbVRoZW1lIiwid2luZG93IiwibWF0Y2hNZWRpYSIsIm1hdGNoZXMiLCJtZWRpYVF1ZXJ5IiwiaGFuZGxlQ2hhbmdlIiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJyb290IiwiZG9jdW1lbnQiLCJkb2N1bWVudEVsZW1lbnQiLCJjbGFzc0xpc3QiLCJyZW1vdmUiLCJhZGQiLCJtZXRhVGhlbWVDb2xvciIsInF1ZXJ5U2VsZWN0b3IiLCJzZXRBdHRyaWJ1dGUiLCJzZXRUaGVtZSIsIm5ld1RoZW1lIiwic2F2ZVNldHRpbmdzIiwiZGl2IiwiY2xhc3NOYW1lIiwiUHJvdmlkZXIiLCJ2YWx1ZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ThemeProvider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/localStorage.ts":
/*!*********************************!*\
  !*** ./src/lib/localStorage.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getLocalStorage: () => (/* binding */ getLocalStorage)\n/* harmony export */ });\nconst STORAGE_KEYS = {\n    ENTRIES: 'journal_entries',\n    DRAFTS: 'journal_drafts',\n    SETTINGS: 'journal_settings',\n    TAGS: 'journal_tags'\n};\nclass LocalStorageManager {\n    // Settings management\n    getSettings() {\n        if (false) {}\n        try {\n            const stored = localStorage.getItem(STORAGE_KEYS.SETTINGS);\n            if (stored) {\n                return {\n                    ...this.getDefaultSettings(),\n                    ...JSON.parse(stored)\n                };\n            }\n        } catch (error) {\n            console.error('Error loading settings from localStorage:', error);\n        }\n        return this.getDefaultSettings();\n    }\n    saveSettings(settings) {\n        if (false) {}\n        try {\n            const current = this.getSettings();\n            const updated = {\n                ...current,\n                ...settings\n            };\n            localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(updated));\n        } catch (error) {\n            console.error('Error saving settings to localStorage:', error);\n        }\n    }\n    getDefaultSettings() {\n        return {\n            theme: 'system',\n            fontSize: 16,\n            fontFamily: 'Inter, system-ui, sans-serif',\n            autoSave: true,\n            autoSaveInterval: 2000,\n            spellCheck: true\n        };\n    }\n    // Draft management (for unsaved entries)\n    saveDraft(draft) {\n        if (false) {}\n        try {\n            const drafts = this.getDrafts();\n            const existingIndex = drafts.findIndex((d)=>d.id === draft.id);\n            if (existingIndex >= 0) {\n                drafts[existingIndex] = draft;\n            } else {\n                drafts.push(draft);\n            }\n            localStorage.setItem(STORAGE_KEYS.DRAFTS, JSON.stringify(drafts));\n        } catch (error) {\n            console.error('Error saving draft to localStorage:', error);\n        }\n    }\n    getDrafts() {\n        if (false) {}\n        try {\n            const stored = localStorage.getItem(STORAGE_KEYS.DRAFTS);\n            return stored ? JSON.parse(stored) : [];\n        } catch (error) {\n            console.error('Error loading drafts from localStorage:', error);\n            return [];\n        }\n    }\n    getDraft(id) {\n        const drafts = this.getDrafts();\n        return drafts.find((d)=>d.id === id) || null;\n    }\n    deleteDraft(id) {\n        try {\n            const drafts = this.getDrafts().filter((d)=>d.id !== id);\n            localStorage.setItem(STORAGE_KEYS.DRAFTS, JSON.stringify(drafts));\n        } catch (error) {\n            console.error('Error deleting draft from localStorage:', error);\n        }\n    }\n    // Entry caching (for offline access)\n    cacheEntries(entries) {\n        try {\n            const cached = {\n                entries,\n                lastUpdated: new Date().toISOString()\n            };\n            localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(cached));\n        } catch (error) {\n            console.error('Error caching entries to localStorage:', error);\n        }\n    }\n    getCachedEntries() {\n        try {\n            const stored = localStorage.getItem(STORAGE_KEYS.ENTRIES);\n            return stored ? JSON.parse(stored) : null;\n        } catch (error) {\n            console.error('Error loading cached entries from localStorage:', error);\n            return null;\n        }\n    }\n    cacheEntry(entry) {\n        try {\n            const cached = this.getCachedEntries();\n            if (cached) {\n                const existingIndex = cached.entries.findIndex((e)=>e.id === entry.id);\n                if (existingIndex >= 0) {\n                    cached.entries[existingIndex] = entry;\n                } else {\n                    cached.entries.unshift(entry);\n                }\n                cached.lastUpdated = new Date().toISOString();\n                localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(cached));\n            } else {\n                this.cacheEntries([\n                    entry\n                ]);\n            }\n        } catch (error) {\n            console.error('Error caching entry to localStorage:', error);\n        }\n    }\n    removeCachedEntry(id) {\n        try {\n            const cached = this.getCachedEntries();\n            if (cached) {\n                cached.entries = cached.entries.filter((e)=>e.id !== id);\n                cached.lastUpdated = new Date().toISOString();\n                localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(cached));\n            }\n        } catch (error) {\n            console.error('Error removing cached entry from localStorage:', error);\n        }\n    }\n    // Tag caching\n    cacheTags(tags) {\n        try {\n            const cached = {\n                tags,\n                lastUpdated: new Date().toISOString()\n            };\n            localStorage.setItem(STORAGE_KEYS.TAGS, JSON.stringify(cached));\n        } catch (error) {\n            console.error('Error caching tags to localStorage:', error);\n        }\n    }\n    getCachedTags() {\n        try {\n            const stored = localStorage.getItem(STORAGE_KEYS.TAGS);\n            const cached = stored ? JSON.parse(stored) : null;\n            return cached ? cached.tags : [];\n        } catch (error) {\n            console.error('Error loading cached tags from localStorage:', error);\n            return [];\n        }\n    }\n    // Utility methods\n    clearAllData() {\n        try {\n            Object.values(STORAGE_KEYS).forEach((key)=>{\n                localStorage.removeItem(key);\n            });\n        } catch (error) {\n            console.error('Error clearing localStorage:', error);\n        }\n    }\n    getStorageUsage() {\n        try {\n            let used = 0;\n            for(let i = 0; i < localStorage.length; i++){\n                const key = localStorage.key(i);\n                if (key) {\n                    var _localStorage_getItem;\n                    used += ((_localStorage_getItem = localStorage.getItem(key)) === null || _localStorage_getItem === void 0 ? void 0 : _localStorage_getItem.length) || 0;\n                }\n            }\n            // Estimate available space (most browsers have ~5-10MB limit)\n            const available = 5 * 1024 * 1024 - used; // Assume 5MB limit\n            return {\n                used,\n                available\n            };\n        } catch (error) {\n            console.error('Error calculating storage usage:', error);\n            return {\n                used: 0,\n                available: 0\n            };\n        }\n    }\n    // Auto-save functionality\n    createAutoSaveTimer(callback, interval) {\n        const settings = this.getSettings();\n        const saveInterval = interval || settings.autoSaveInterval;\n        return setInterval(callback, saveInterval);\n    }\n    // Export/Import functionality\n    exportData() {\n        try {\n            const data = {\n                entries: this.getCachedEntries(),\n                drafts: this.getDrafts(),\n                settings: this.getSettings(),\n                tags: this.getCachedTags(),\n                exportDate: new Date().toISOString()\n            };\n            return JSON.stringify(data, null, 2);\n        } catch (error) {\n            console.error('Error exporting data:', error);\n            throw new Error('Failed to export data');\n        }\n    }\n    importData(jsonData) {\n        try {\n            const data = JSON.parse(jsonData);\n            if (data.entries) {\n                localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(data.entries));\n            }\n            if (data.drafts) {\n                localStorage.setItem(STORAGE_KEYS.DRAFTS, JSON.stringify(data.drafts));\n            }\n            if (data.settings) {\n                localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(data.settings));\n            }\n            if (data.tags) {\n                localStorage.setItem(STORAGE_KEYS.TAGS, JSON.stringify(data.tags));\n            }\n        } catch (error) {\n            console.error('Error importing data:', error);\n            throw new Error('Failed to import data');\n        }\n    }\n}\n// Singleton instance\nlet storageInstance = null;\nfunction getLocalStorage() {\n    if (!storageInstance) {\n        storageInstance = new LocalStorageManager();\n    }\n    return storageInstance;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LocalStorageManager);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/localStorage.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);