"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/JournalApp.tsx":
/*!***************************************!*\
  !*** ./src/components/JournalApp.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JournalApp: () => (/* binding */ JournalApp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _TextEditor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TextEditor */ \"(app-pages-browser)/./src/components/TextEditor.tsx\");\n/* harmony import */ var _TagManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TagManager */ \"(app-pages-browser)/./src/components/TagManager.tsx\");\n/* harmony import */ var _FileUpload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FileUpload */ \"(app-pages-browser)/./src/components/FileUpload.tsx\");\n/* harmony import */ var _Settings__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Settings */ \"(app-pages-browser)/./src/components/Settings.tsx\");\n/* harmony import */ var _lib_localStorage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/localStorage */ \"(app-pages-browser)/./src/lib/localStorage.ts\");\n/* harmony import */ var _lib_testData__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/testData */ \"(app-pages-browser)/./src/lib/testData.ts\");\n/* __next_internal_client_entry_do_not_use__ JournalApp auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction JournalApp(param) {\n    let { className = '' } = param;\n    _s();\n    const [currentEntry, setCurrentEntry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [entries, setEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUpload, setShowUpload] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Start closed on mobile\n    const [zoom, setZoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const storage = (0,_lib_localStorage__WEBPACK_IMPORTED_MODULE_6__.getLocalStorage)();\n    // Update available tags from all entries\n    const updateAvailableTags = (entriesList)=>{\n        const allTags = new Set();\n        entriesList.forEach((entry)=>{\n            entry.tags.forEach((tag)=>allTags.add(tag));\n        });\n        storage.cacheTags([\n            ...allTags\n        ]);\n    };\n    // Detect mobile and load cached entries on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"JournalApp.useEffect\": ()=>{\n            const checkMobile = {\n                \"JournalApp.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                    setSidebarOpen(window.innerWidth >= 768); // Open sidebar on desktop by default\n                }\n            }[\"JournalApp.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            const cached = storage.getCachedEntries();\n            if (cached) {\n                setEntries(cached.entries);\n                updateAvailableTags(cached.entries);\n            }\n            return ({\n                \"JournalApp.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"JournalApp.useEffect\"];\n        }\n    }[\"JournalApp.useEffect\"], []);\n    // Filter entries based on search and tags\n    const filteredEntries = entries.filter((entry)=>{\n        const matchesSearch = !searchQuery || entry.title.toLowerCase().includes(searchQuery.toLowerCase()) || entry.content.toLowerCase().includes(searchQuery.toLowerCase()) || entry.tags.some((tag)=>tag.toLowerCase().includes(searchQuery.toLowerCase()));\n        const matchesTags = selectedTags.length === 0 || selectedTags.every((tag)=>entry.tags.includes(tag));\n        return matchesSearch && matchesTags;\n    });\n    const createNewEntry = ()=>{\n        const newEntry = {\n            id: \"entry_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n            title: '',\n            content: '',\n            tags: [],\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n            word_count: 0\n        };\n        setCurrentEntry(newEntry);\n        setEntries((prev)=>[\n                newEntry,\n                ...prev\n            ]);\n        storage.cacheEntry(newEntry);\n        // Close sidebar on mobile when creating new entry\n        if (isMobile) {\n            setSidebarOpen(false);\n        }\n    };\n    const saveEntry = (data)=>{\n        if (!currentEntry) return;\n        const updatedEntry = {\n            ...currentEntry,\n            title: data.title || 'Untitled Entry',\n            content: data.content,\n            tags: data.tags,\n            updated_at: new Date().toISOString(),\n            word_count: data.content.trim().split(/\\s+/).filter((word)=>word.length > 0).length\n        };\n        setCurrentEntry(updatedEntry);\n        const newEntries = entries.map((entry)=>entry.id === updatedEntry.id ? updatedEntry : entry);\n        setEntries(newEntries);\n        storage.cacheEntry(updatedEntry);\n        // Update available tags from all entries\n        updateAvailableTags(newEntries);\n    };\n    const selectEntry = (entry)=>{\n        setCurrentEntry(entry);\n        // Close sidebar on mobile when selecting entry\n        if (isMobile) {\n            setSidebarOpen(false);\n        }\n    };\n    const deleteEntry = (entryId)=>{\n        setEntries((prev)=>prev.filter((entry)=>entry.id !== entryId));\n        storage.removeCachedEntry(entryId);\n        if ((currentEntry === null || currentEntry === void 0 ? void 0 : currentEntry.id) === entryId) {\n            setCurrentEntry(null);\n        }\n    };\n    const handleSearch = (query, tags)=>{\n        setSearchQuery(query);\n        setSelectedTags(tags);\n    };\n    const handleFileImport = (file)=>{\n        const newEntry = {\n            id: crypto.randomUUID(),\n            title: file.title,\n            content: file.content,\n            tags: [],\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n            word_count: file.content.split(/\\s+/).filter((word)=>word.length > 0).length\n        };\n        const newEntries = [\n            newEntry,\n            ...entries\n        ];\n        setEntries(newEntries);\n        storage.cacheEntry(newEntry);\n        setCurrentEntry(newEntry);\n        setShowUpload(false);\n        updateAvailableTags(newEntries);\n    };\n    const adjustZoom = (delta)=>{\n        const newZoom = Math.max(50, Math.min(200, zoom + delta));\n        setZoom(newZoom);\n    };\n    const loadSampleEntries = ()=>{\n        const sampleEntries = (0,_lib_testData__WEBPACK_IMPORTED_MODULE_7__.loadSampleData)();\n        if (sampleEntries.length > 0) {\n            setEntries(sampleEntries);\n            updateAvailableTags(sampleEntries);\n        }\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', {\n            month: 'short',\n            day: 'numeric',\n            year: 'numeric'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-50 dark:bg-gray-900 \".concat(className),\n        children: [\n            isMobile && sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-40\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 193,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n        \".concat(sidebarOpen ? isMobile ? 'w-80' : 'w-80' : 'w-0', \"\\n        \").concat(isMobile ? 'fixed left-0 top-0 h-full z-50' : 'relative', \"\\n        transition-all duration-300 overflow-hidden\\n        bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700\\n        flex flex-col\\n      \"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 md:p-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3 md:mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-lg md:text-xl font-bold text-gray-900 dark:text-gray-100\",\n                                        children: \"AllJournal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 13\n                                    }, this),\n                                    !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>adjustZoom(-10),\n                                                className: \"p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300\",\n                                                title: \"Zoom Out\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                children: [\n                                                    zoom,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>adjustZoom(10),\n                                                className: \"p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300\",\n                                                title: \"Zoom In\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: createNewEntry,\n                                        className: \"flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm md:text-base\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: isMobile ? 'New' : 'New Entry'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowUpload(true),\n                                        className: \"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                        title: \"Upload Files\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowSettings(true),\n                                        className: \"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                        title: \"Settings\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 md:p-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TagManager__WEBPACK_IMPORTED_MODULE_3__.TagManager, {\n                            selectedTags: selectedTags,\n                            onTagsChange: setSelectedTags,\n                            onSearch: handleSearch\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 md:p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                        children: [\n                                            \"Entries (\",\n                                            filteredEntries.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        filteredEntries.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                onClick: ()=>selectEntry(entry),\n                                                className: \"\\n                    p-3 rounded-lg cursor-pointer transition-colors\\n                    \".concat((currentEntry === null || currentEntry === void 0 ? void 0 : currentEntry.id) === entry.id ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' : 'hover:bg-gray-50 dark:hover:bg-gray-700', \"\\n                  \"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 dark:text-gray-100 truncate text-sm md:text-base\",\n                                                        children: entry.title || 'Untitled Entry'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs md:text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2\",\n                                                        children: entry.content || 'No content'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mt-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: formatDate(entry.updated_at)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: [\n                                                                    entry.word_count,\n                                                                    \" words\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    entry.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mt-2\",\n                                                        children: [\n                                                            entry.tags.slice(0, isMobile ? 2 : 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs px-2 py-1 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded\",\n                                                                    children: [\n                                                                        \"#\",\n                                                                        tag\n                                                                    ]\n                                                                }, tag, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                                    lineNumber: 311,\n                                                                    columnNumber: 25\n                                                                }, this)),\n                                                            entry.tags.length > (isMobile ? 2 : 3) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: [\n                                                                    \"+\",\n                                                                    entry.tags.length - (isMobile ? 2 : 3)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, entry.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this)),\n                                        filteredEntries.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-12 h-12 mx-auto text-gray-400 mb-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 dark:text-gray-400\",\n                                                    children: entries.length === 0 ? 'No entries yet' : 'No entries match your search'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 19\n                                                }, this),\n                                                entries.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: createNewEntry,\n                                                    className: \"mt-2 text-blue-600 dark:text-blue-400 hover:underline\",\n                                                    children: \"Create your first entry\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col \".concat(isMobile && sidebarOpen ? 'pointer-events-none' : ''),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-3 md:p-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            console.log('Menu button clicked!', sidebarOpen);\n                                            setSidebarOpen(!sidebarOpen);\n                                        },\n                                        className: \"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 13\n                                    }, this),\n                                    isMobile && currentEntry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-medium text-gray-900 dark:text-gray-100 truncate\",\n                                        children: currentEntry.title || 'Untitled Entry'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, this),\n                            currentEntry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 md:space-x-4\",\n                                children: [\n                                    !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400 hidden sm:block\",\n                                        children: [\n                                            \"Last saved: \",\n                                            formatDate(currentEntry.updated_at)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>deleteEntry(currentEntry.id),\n                                        className: \"text-sm text-red-600 dark:text-red-400 hover:underline\",\n                                        children: isMobile ? 'Delete' : 'Delete Entry'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-2 md:p-0\",\n                        style: {\n                            zoom: \"\".concat(zoom, \"%\")\n                        },\n                        children: currentEntry ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TextEditor__WEBPACK_IMPORTED_MODULE_2__.TextEditor, {\n                            entryId: currentEntry.id,\n                            initialTitle: currentEntry.title,\n                            initialContent: currentEntry.content,\n                            initialTags: currentEntry.tags,\n                            onSave: saveEntry,\n                            onAutoSave: saveEntry,\n                            className: \"h-full\"\n                        }, currentEntry.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-full p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center max-w-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-12 h-12 md:w-16 md:h-16 mx-auto text-gray-400 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg md:text-xl font-medium text-gray-900 dark:text-gray-100 mb-2\",\n                                        children: \"Welcome to AllJournal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm md:text-base text-gray-600 dark:text-gray-400 mb-6\",\n                                        children: isMobile ? \"Tap the menu to browse entries or create a new one to start writing.\" : \"Select an entry from the sidebar or create a new one to start writing.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: createNewEntry,\n                                                className: \"flex items-center justify-center space-x-2 w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Create New Entry\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 19\n                                            }, this),\n                                            isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSidebarOpen(true),\n                                                className: \"flex items-center justify-center space-x-2 w-full px-4 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Browse Entries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 21\n                                            }, this),\n                                            entries.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: loadSampleEntries,\n                                                className: \"flex items-center justify-center space-x-2 w-full px-4 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Load Sample Entries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 350,\n                columnNumber: 7\n            }, this),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Settings__WEBPACK_IMPORTED_MODULE_5__.Settings, {\n                isOpen: showSettings,\n                onClose: ()=>setShowSettings(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 452,\n                columnNumber: 9\n            }, this),\n            showUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-gray-100\",\n                                    children: \"Import Files\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowUpload(false),\n                                    className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FileUpload__WEBPACK_IMPORTED_MODULE_4__.FileUpload, {\n                                onFilesProcessed: ()=>{},\n                                onImport: handleFileImport\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 459,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, this);\n}\n_s(JournalApp, \"jQW7sy2PZD0LrknvYp9gYrVxNR4=\");\n_c = JournalApp;\nvar _c;\n$RefreshReg$(_c, \"JournalApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/JournalApp.tsx\n"));

/***/ })

});