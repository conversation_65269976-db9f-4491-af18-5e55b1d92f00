{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\n// import { JournalApp } from \"@/components/JournalApp\";\n\nexport default function Home() {\n  console.log('Home page rendering...');\n\n  return (\n    <main className=\"h-screen flex items-center justify-center\">\n      <div className=\"text-center\">\n        <h1 className=\"text-2xl font-bold mb-4\">AllJournal Debug Mode</h1>\n        <p className=\"text-gray-600 mb-4\">Testing basic React functionality</p>\n        <button\n          onClick={() => console.log('Button clicked!')}\n          className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n        >\n          Test Button\n        </button>\n      </div>\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAIe,SAAS;IACtB,QAAQ,GAAG,CAAC;IAEZ,qBACE,6LAAC;QAAK,WAAU;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,6LAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAClC,6LAAC;oBACC,SAAS,IAAM,QAAQ,GAAG,CAAC;oBAC3B,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT;KAjBwB", "debugId": null}}]}