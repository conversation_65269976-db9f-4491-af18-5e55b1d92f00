'use client';

import React, { useState } from 'react';
import { 
  PlusCircle, 
  Menu,
  BookOpen
} from 'lucide-react';

interface JournalAppProps {
  className?: string;
}

export function JournalApp({ className = '' }: JournalAppProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [currentEntry, setCurrentEntry] = useState<any>(null);

  const createNewEntry = () => {
    console.log('Creating new entry...');
    setCurrentEntry({ id: 'test', title: 'Test Entry', content: 'Test content' });
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'w-80' : 'w-0'} transition-all duration-300 overflow-hidden bg-white border-r border-gray-200 flex flex-col`}>
        <div className="p-4 border-b border-gray-200">
          <h1 className="text-xl font-bold text-gray-900">AllJournal</h1>
          <button
            onClick={createNewEntry}
            className="mt-4 w-full flex items-center justify-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <PlusCircle className="w-4 h-4" />
            <span>New Entry</span>
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Bar */}
        <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200">
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="p-2 text-gray-600 hover:text-gray-800 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <Menu className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 p-4">
          {currentEntry ? (
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-2xl font-bold mb-4">{currentEntry.title}</h2>
              <p className="text-gray-700">{currentEntry.content}</p>
            </div>
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <BookOpen className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <h2 className="text-xl font-medium text-gray-900 mb-2">Welcome to AllJournal</h2>
                <p className="text-gray-600 mb-6">Create a new entry to start writing.</p>
                <button
                  onClick={createNewEntry}
                  className="flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <PlusCircle className="w-4 h-4" />
                  <span>Create New Entry</span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
