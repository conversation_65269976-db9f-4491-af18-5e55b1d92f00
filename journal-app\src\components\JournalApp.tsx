'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  PlusCircle,
  Menu,
  BookOpen,
  Settings as SettingsIcon,
  Upload
} from 'lucide-react';
import { getLocalStorage } from '@/lib/localStorage';
import { JournalEntry } from '@/lib/database';

interface JournalAppProps {
  className?: string;
}

export function JournalApp({ className = '' }: JournalAppProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [currentEntry, setCurrentEntry] = useState<JournalEntry | null>(null);
  const [entries, setEntries] = useState<JournalEntry[]>([]);
  const [showSettings, setShowSettings] = useState(false);
  const [showUpload, setShowUpload] = useState(false);

  // Memoize storage to prevent recreation on every render
  const storage = useMemo(() => getLocalStorage(), []);

  // Load cached entries on mount
  useEffect(() => {
    const cached = storage.getCachedEntries();
    if (cached) {
      setEntries(cached.entries);
    }
  }, [storage]);

  const createNewEntry = useCallback(() => {
    const newEntry: JournalEntry = {
      id: `entry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      title: '',
      content: '',
      tags: [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      word_count: 0,
    };

    setCurrentEntry(newEntry);
    setEntries(prev => [newEntry, ...prev]);
    storage.cacheEntry(newEntry);
  }, [storage]);

  const selectEntry = useCallback((entry: JournalEntry) => {
    setCurrentEntry(entry);
  }, []);

  const saveEntry = useCallback((data: { title: string; content: string; tags: string[] }) => {
    if (!currentEntry) return;

    const updatedEntry: JournalEntry = {
      ...currentEntry,
      title: data.title || 'Untitled Entry',
      content: data.content,
      tags: data.tags,
      updated_at: new Date().toISOString(),
      word_count: data.content.trim().split(/\s+/).filter(word => word.length > 0).length,
    };

    setCurrentEntry(updatedEntry);
    const newEntries = entries.map(entry =>
      entry.id === updatedEntry.id ? updatedEntry : entry
    );
    setEntries(newEntries);
    storage.cacheEntry(updatedEntry);
  }, [currentEntry, entries, storage]);

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'w-80' : 'w-0'} transition-all duration-300 overflow-hidden bg-white border-r border-gray-200 flex flex-col`}>
        <div className="p-4 border-b border-gray-200">
          <h1 className="text-xl font-bold text-gray-900">AllJournal</h1>
          <div className="flex space-x-2 mt-4">
            <button
              onClick={createNewEntry}
              className="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <PlusCircle className="w-4 h-4" />
              <span>New Entry</span>
            </button>
            <button
              onClick={() => setShowUpload(true)}
              className="p-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              title="Upload Files"
            >
              <Upload className="w-4 h-4" />
            </button>
            <button
              onClick={() => setShowSettings(true)}
              className="p-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              title="Settings"
            >
              <SettingsIcon className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Entries List */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-4">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              Entries ({entries.length})
            </h3>
            <div className="space-y-2">
              {entries.map((entry) => (
                <div
                  key={entry.id}
                  onClick={() => selectEntry(entry)}
                  className={`
                    p-3 rounded-lg cursor-pointer transition-colors
                    ${currentEntry?.id === entry.id
                      ? 'bg-blue-50 border border-blue-200'
                      : 'hover:bg-gray-50'
                    }
                  `}
                >
                  <h4 className="font-medium text-gray-900 truncate">
                    {entry.title || 'Untitled Entry'}
                  </h4>
                  <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                    {entry.content || 'No content'}
                  </p>
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-xs text-gray-500">
                      {new Date(entry.updated_at).toLocaleDateString()}
                    </span>
                    <span className="text-xs text-gray-500">
                      {entry.word_count} words
                    </span>
                  </div>
                </div>
              ))}

              {entries.length === 0 && (
                <div className="text-center py-8">
                  <BookOpen className="w-12 h-12 mx-auto text-gray-400 mb-3" />
                  <p className="text-gray-500">No entries yet</p>
                  <button
                    onClick={createNewEntry}
                    className="mt-2 text-blue-600 hover:underline"
                  >
                    Create your first entry
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Bar */}
        <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200">
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="p-2 text-gray-600 hover:text-gray-800 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <Menu className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 p-4">
          {currentEntry ? (
            <SimpleEditor
              entry={currentEntry}
              onSave={saveEntry}
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <BookOpen className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <h2 className="text-xl font-medium text-gray-900 mb-2">Welcome to AllJournal</h2>
                <p className="text-gray-600 mb-6">Create a new entry to start writing.</p>
                <button
                  onClick={createNewEntry}
                  className="flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <PlusCircle className="w-4 h-4" />
                  <span>Create New Entry</span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Simple editor component to avoid complexity
interface SimpleEditorProps {
  entry: JournalEntry;
  onSave: (data: { title: string; content: string; tags: string[] }) => void;
}

function SimpleEditor({ entry, onSave }: SimpleEditorProps) {
  const [title, setTitle] = React.useState(entry.title);
  const [content, setContent] = React.useState(entry.content);

  // Auto-save with debounce
  React.useEffect(() => {
    const timer = setTimeout(() => {
      if (title !== entry.title || content !== entry.content) {
        onSave({ title, content, tags: entry.tags });
      }
    }, 1000);

    return () => clearTimeout(timer);
  }, [title, content, entry.title, entry.content, entry.tags, onSave]);

  return (
    <div className="bg-white rounded-lg shadow h-full flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <input
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Enter title..."
          className="w-full text-2xl font-bold bg-transparent border-none outline-none text-gray-900 placeholder-gray-500"
        />
      </div>
      <div className="flex-1 p-4">
        <textarea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          placeholder="Start writing your journal entry..."
          className="w-full h-full bg-transparent border-none outline-none resize-none text-gray-900 placeholder-gray-500 leading-relaxed"
        />
      </div>
      <div className="p-4 border-t border-gray-200 text-sm text-gray-500">
        <div className="flex justify-between">
          <span>Words: {content.trim().split(/\s+/).filter(word => word.length > 0).length}</span>
          <span>Characters: {content.length}</span>
        </div>
      </div>
    </div>
  );
}
