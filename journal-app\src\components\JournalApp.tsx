'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  PlusCircle, 
  Search, 
  Settings as SettingsIcon, 
  Upload, 
  BookOpen,
  Menu,
  X,
  ZoomIn,
  ZoomOut
} from 'lucide-react';
import { TextEditor } from './TextEditor';
import { TagManager } from './TagManager';
import { FileUpload } from './FileUpload';
import { Settings } from './Settings';
import { getLocalStorage } from '@/lib/localStorage';
import { JournalEntry } from '@/lib/database';
import { loadSampleData } from '@/lib/testData';

interface JournalAppProps {
  className?: string;
}

export function JournalApp({ className = '' }: JournalAppProps) {
  const [currentEntry, setCurrentEntry] = useState<JournalEntry | null>(null);
  const [entries, setEntries] = useState<JournalEntry[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [showUpload, setShowUpload] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false); // Start closed on mobile
  const [zoom, setZoom] = useState(100);
  const [isMobile, setIsMobile] = useState(false);

  const storage = useMemo(() => getLocalStorage(), []);

  // Update available tags from all entries
  const updateAvailableTags = useCallback((entriesList: JournalEntry[]) => {
    const allTags = new Set<string>();
    entriesList.forEach(entry => {
      entry.tags.forEach(tag => allTags.add(tag));
    });
    storage.cacheTags([...allTags]);
  }, [storage]);

  // Detect mobile and load cached entries on mount
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      setSidebarOpen(window.innerWidth >= 768); // Open sidebar on desktop by default
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    const cached = storage.getCachedEntries();
    if (cached) {
      setEntries(cached.entries);
      updateAvailableTags(cached.entries);
    }

    return () => window.removeEventListener('resize', checkMobile);
  }, [storage, updateAvailableTags]);

  // Filter entries based on search and tags
  const filteredEntries = entries.filter(entry => {
    const matchesSearch = !searchQuery ||
      entry.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      entry.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      entry.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesTags = selectedTags.length === 0 ||
      selectedTags.every(tag => entry.tags.includes(tag));

    return matchesSearch && matchesTags;
  });

  const createNewEntry = () => {
    const newEntry: JournalEntry = {
      id: `entry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      title: '',
      content: '',
      tags: [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      word_count: 0,
    };

    setCurrentEntry(newEntry);
    setEntries(prev => [newEntry, ...prev]);
    storage.cacheEntry(newEntry);

    // Close sidebar on mobile when creating new entry
    if (isMobile) {
      setSidebarOpen(false);
    }
  };

  const saveEntry = (data: { title: string; content: string; tags: string[] }) => {
    if (!currentEntry) return;

    const updatedEntry: JournalEntry = {
      ...currentEntry,
      title: data.title || 'Untitled Entry',
      content: data.content,
      tags: data.tags,
      updated_at: new Date().toISOString(),
      word_count: data.content.trim().split(/\s+/).filter(word => word.length > 0).length,
    };

    setCurrentEntry(updatedEntry);
    const newEntries = entries.map(entry =>
      entry.id === updatedEntry.id ? updatedEntry : entry
    );
    setEntries(newEntries);
    storage.cacheEntry(updatedEntry);

    // Update available tags from all entries
    updateAvailableTags(newEntries);
  };

  const selectEntry = (entry: JournalEntry) => {
    setCurrentEntry(entry);

    // Close sidebar on mobile when selecting entry
    if (isMobile) {
      setSidebarOpen(false);
    }
  };

  const deleteEntry = (entryId: string) => {
    setEntries(prev => prev.filter(entry => entry.id !== entryId));
    storage.removeCachedEntry(entryId);
    
    if (currentEntry?.id === entryId) {
      setCurrentEntry(null);
    }
  };

  const handleSearch = (query: string, tags: string[]) => {
    setSearchQuery(query);
    setSelectedTags(tags);
  };

  const handleFileImport = (file: any) => {
    const newEntry: JournalEntry = {
      id: crypto.randomUUID(),
      title: file.title,
      content: file.content,
      tags: [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      word_count: file.content.split(/\s+/).filter((word: string) => word.length > 0).length,
    };

    const newEntries = [newEntry, ...entries];
    setEntries(newEntries);
    storage.cacheEntry(newEntry);
    setCurrentEntry(newEntry);
    setShowUpload(false);
    updateAvailableTags(newEntries);
  };

  const adjustZoom = (delta: number) => {
    const newZoom = Math.max(50, Math.min(200, zoom + delta));
    setZoom(newZoom);
  };

  const loadSampleEntries = () => {
    const sampleEntries = loadSampleData();
    if (sampleEntries.length > 0) {
      setEntries(sampleEntries);
      updateAvailableTags(sampleEntries);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <div className={`flex h-screen bg-gray-50 dark:bg-gray-900 ${className}`}>
      {/* Mobile Overlay */}
      {isMobile && sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`
        ${sidebarOpen ? (isMobile ? 'w-80' : 'w-80') : 'w-0'}
        ${isMobile ? 'fixed left-0 top-0 h-full z-50' : 'relative'}
        transition-all duration-300 overflow-hidden
        bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700
        flex flex-col
      `}>
        {/* Sidebar Header */}
        <div className="p-3 md:p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-3 md:mb-4">
            <h1 className="text-lg md:text-xl font-bold text-gray-900 dark:text-gray-100">
              AllJournal
            </h1>
            {!isMobile && (
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => adjustZoom(-10)}
                  className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                  title="Zoom Out"
                >
                  <ZoomOut className="w-4 h-4" />
                </button>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {zoom}%
                </span>
                <button
                  onClick={() => adjustZoom(10)}
                  className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                  title="Zoom In"
                >
                  <ZoomIn className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>

          <div className="flex space-x-2">
            <button
              onClick={createNewEntry}
              className="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm md:text-base"
            >
              <PlusCircle className="w-4 h-4" />
              <span>{isMobile ? 'New' : 'New Entry'}</span>
            </button>

            <button
              onClick={() => setShowUpload(true)}
              className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              title="Upload Files"
            >
              <Upload className="w-4 h-4" />
            </button>

            <button
              onClick={() => setShowSettings(true)}
              className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              title="Settings"
            >
              <SettingsIcon className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Tag Manager */}
        <div className="p-3 md:p-4 border-b border-gray-200 dark:border-gray-700">
          <TagManager
            selectedTags={selectedTags}
            onTagsChange={setSelectedTags}
            onSearch={handleSearch}
          />
        </div>

        {/* Entries List */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-3 md:p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Entries ({filteredEntries.length})
              </h3>
            </div>

            <div className="space-y-2">
              {filteredEntries.map((entry) => (
                <div
                  key={entry.id}
                  onClick={() => selectEntry(entry)}
                  className={`
                    p-3 rounded-lg cursor-pointer transition-colors
                    ${currentEntry?.id === entry.id
                      ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
                      : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                    }
                  `}
                >
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 truncate text-sm md:text-base">
                    {entry.title || 'Untitled Entry'}
                  </h4>
                  <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                    {entry.content || 'No content'}
                  </p>
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {formatDate(entry.updated_at)}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {entry.word_count} words
                    </span>
                  </div>
                  {entry.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {entry.tags.slice(0, isMobile ? 2 : 3).map((tag) => (
                        <span
                          key={tag}
                          className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded"
                        >
                          #{tag}
                        </span>
                      ))}
                      {entry.tags.length > (isMobile ? 2 : 3) && (
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          +{entry.tags.length - (isMobile ? 2 : 3)}
                        </span>
                      )}
                    </div>
                  )}
                </div>
              ))}
              
              {filteredEntries.length === 0 && (
                <div className="text-center py-8">
                  <BookOpen className="w-12 h-12 mx-auto text-gray-400 mb-3" />
                  <p className="text-gray-500 dark:text-gray-400">
                    {entries.length === 0 ? 'No entries yet' : 'No entries match your search'}
                  </p>
                  {entries.length === 0 && (
                    <button
                      onClick={createNewEntry}
                      className="mt-2 text-blue-600 dark:text-blue-400 hover:underline"
                    >
                      Create your first entry
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className={`flex-1 flex flex-col ${isMobile && sidebarOpen ? 'pointer-events-none' : ''}`}>
        {/* Top Bar */}
        <div className="flex items-center justify-between p-3 md:p-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <Menu className="w-5 h-5" />
            </button>

            {/* Mobile: Show current entry title */}
            {isMobile && currentEntry && (
              <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100 truncate">
                {currentEntry.title || 'Untitled Entry'}
              </h2>
            )}
          </div>

          {currentEntry && (
            <div className="flex items-center space-x-2 md:space-x-4">
              {!isMobile && (
                <span className="text-sm text-gray-500 dark:text-gray-400 hidden sm:block">
                  Last saved: {formatDate(currentEntry.updated_at)}
                </span>
              )}
              <button
                onClick={() => deleteEntry(currentEntry.id)}
                className="text-sm text-red-600 dark:text-red-400 hover:underline"
              >
                {isMobile ? 'Delete' : 'Delete Entry'}
              </button>
            </div>
          )}
        </div>

        {/* Editor */}
        <div className="flex-1 p-2 md:p-0" style={{ zoom: `${zoom}%` }}>
          {currentEntry ? (
            <TextEditor
              key={currentEntry.id} // Force re-render when switching entries
              entryId={currentEntry.id}
              initialTitle={currentEntry.title}
              initialContent={currentEntry.content}
              initialTags={currentEntry.tags}
              onSave={saveEntry}
              onAutoSave={saveEntry}
              className="h-full"
            />
          ) : (
            <div className="flex items-center justify-center h-full p-4">
              <div className="text-center max-w-md">
                <BookOpen className="w-12 h-12 md:w-16 md:h-16 mx-auto text-gray-400 mb-4" />
                <h2 className="text-lg md:text-xl font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Welcome to AllJournal
                </h2>
                <p className="text-sm md:text-base text-gray-600 dark:text-gray-400 mb-6">
                  {isMobile
                    ? "Tap the menu to browse entries or create a new one to start writing."
                    : "Select an entry from the sidebar or create a new one to start writing."
                  }
                </p>
                <div className="space-y-3">
                  <button
                    onClick={createNewEntry}
                    className="flex items-center justify-center space-x-2 w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <PlusCircle className="w-4 h-4" />
                    <span>Create New Entry</span>
                  </button>

                  {isMobile && (
                    <button
                      onClick={() => setSidebarOpen(true)}
                      className="flex items-center justify-center space-x-2 w-full px-4 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                    >
                      <Menu className="w-4 h-4" />
                      <span>Browse Entries</span>
                    </button>
                  )}

                  {entries.length === 0 && (
                    <button
                      onClick={loadSampleEntries}
                      className="flex items-center justify-center space-x-2 w-full px-4 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                    >
                      <BookOpen className="w-4 h-4" />
                      <span>Load Sample Entries</span>
                    </button>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Modals */}
      {showSettings && (
        <Settings
          isOpen={showSettings}
          onClose={() => setShowSettings(false)}
        />
      )}

      {showUpload && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Import Files
              </h2>
              <button
                onClick={() => setShowUpload(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <div className="p-6">
              <FileUpload
                onFilesProcessed={() => {}}
                onImport={handleFileImport}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
