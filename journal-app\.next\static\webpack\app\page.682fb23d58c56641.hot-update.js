"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/JournalApp.tsx":
/*!***************************************!*\
  !*** ./src/components/JournalApp.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JournalApp: () => (/* binding */ JournalApp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _lib_localStorage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/localStorage */ \"(app-pages-browser)/./src/lib/localStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ JournalApp auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nfunction JournalApp(param) {\n    let { className = '' } = param;\n    _s();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentEntry, setCurrentEntry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [entries, setEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUpload, setShowUpload] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Memoize storage to prevent recreation on every render\n    const storage = (0,_lib_localStorage__WEBPACK_IMPORTED_MODULE_2__.getLocalStorage)();\n    // Load cached entries on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"JournalApp.useEffect\": ()=>{\n            const cached = storage.getCachedEntries();\n            if (cached) {\n                setEntries(cached.entries);\n            }\n        }\n    }[\"JournalApp.useEffect\"], [\n        storage\n    ]);\n    const createNewEntry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"JournalApp.useCallback[createNewEntry]\": ()=>{\n            console.log('createNewEntry called!');\n            const newEntry = {\n                id: \"entry_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n                title: '',\n                content: '',\n                tags: [],\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString(),\n                word_count: 0\n            };\n            setCurrentEntry(newEntry);\n            setEntries({\n                \"JournalApp.useCallback[createNewEntry]\": (prev)=>[\n                        newEntry,\n                        ...prev\n                    ]\n            }[\"JournalApp.useCallback[createNewEntry]\"]);\n            storage.cacheEntry(newEntry);\n        }\n    }[\"JournalApp.useCallback[createNewEntry]\"], [\n        storage\n    ]);\n    const selectEntry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"JournalApp.useCallback[selectEntry]\": (entry)=>{\n            setCurrentEntry(entry);\n        }\n    }[\"JournalApp.useCallback[selectEntry]\"], []);\n    const saveEntry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"JournalApp.useCallback[saveEntry]\": (data)=>{\n            if (!currentEntry) return;\n            const updatedEntry = {\n                ...currentEntry,\n                title: data.title || 'Untitled Entry',\n                content: data.content,\n                tags: data.tags,\n                updated_at: new Date().toISOString(),\n                word_count: data.content.trim().split(/\\s+/).filter({\n                    \"JournalApp.useCallback[saveEntry]\": (word)=>word.length > 0\n                }[\"JournalApp.useCallback[saveEntry]\"]).length\n            };\n            setCurrentEntry(updatedEntry);\n            const newEntries = entries.map({\n                \"JournalApp.useCallback[saveEntry].newEntries\": (entry)=>entry.id === updatedEntry.id ? updatedEntry : entry\n            }[\"JournalApp.useCallback[saveEntry].newEntries\"]);\n            setEntries(newEntries);\n            storage.cacheEntry(updatedEntry);\n        }\n    }[\"JournalApp.useCallback[saveEntry]\"], [\n        currentEntry,\n        entries,\n        storage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(sidebarOpen ? 'w-80' : 'w-0', \" transition-all duration-300 overflow-hidden bg-white border-r border-gray-200 flex flex-col\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold text-gray-900\",\n                                children: \"AllJournal\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2 mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            console.log('Create New Entry button clicked!');\n                                            createNewEntry();\n                                        },\n                                        className: \"flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"New Entry\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowUpload(true),\n                                        className: \"p-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                        title: \"Upload Files\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowSettings(true),\n                                        className: \"p-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                        title: \"Settings\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-gray-700 mb-3\",\n                                    children: [\n                                        \"Entries (\",\n                                        entries.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        entries.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                onClick: ()=>selectEntry(entry),\n                                                className: \"\\n                    p-3 rounded-lg cursor-pointer transition-colors\\n                    \".concat((currentEntry === null || currentEntry === void 0 ? void 0 : currentEntry.id) === entry.id ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50', \"\\n                  \"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 truncate\",\n                                                        children: entry.title || 'Untitled Entry'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mt-1 line-clamp-2\",\n                                                        children: entry.content || 'No content'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mt-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: new Date(entry.updated_at).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    entry.word_count,\n                                                                    \" words\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, entry.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this)),\n                                        entries.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-12 h-12 mx-auto text-gray-400 mb-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500\",\n                                                    children: \"No entries yet\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: createNewEntry,\n                                                    className: \"mt-2 text-blue-600 hover:underline\",\n                                                    children: \"Create your first entry\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 bg-white border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                console.log('Menu button clicked!', sidebarOpen);\n                                setSidebarOpen(!sidebarOpen);\n                            },\n                            className: \"p-2 text-gray-600 hover:text-gray-800 rounded-lg hover:bg-gray-100 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-4\",\n                        children: currentEntry ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleEditor, {\n                            entry: currentEntry,\n                            onSave: saveEntry\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-16 h-16 mx-auto text-gray-400 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-medium text-gray-900 mb-2\",\n                                        children: \"Welcome to AllJournal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-6\",\n                                        children: \"Create a new entry to start writing.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: createNewEntry,\n                                        className: \"flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Create New Entry\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n_s(JournalApp, \"Ir+bZCsd4FiKU3zXRr+br2YpLps=\");\n_c = JournalApp;\nfunction SimpleEditor(param) {\n    let { entry, onSave } = param;\n    _s1();\n    const [title, setTitle] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(entry.title);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(entry.content);\n    // Auto-save with debounce\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"SimpleEditor.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"SimpleEditor.useEffect.timer\": ()=>{\n                    if (title !== entry.title || content !== entry.content) {\n                        onSave({\n                            title,\n                            content,\n                            tags: entry.tags\n                        });\n                    }\n                }\n            }[\"SimpleEditor.useEffect.timer\"], 1000);\n            return ({\n                \"SimpleEditor.useEffect\": ()=>clearTimeout(timer)\n            })[\"SimpleEditor.useEffect\"];\n        }\n    }[\"SimpleEditor.useEffect\"], [\n        title,\n        content,\n        entry.title,\n        entry.content,\n        entry.tags,\n        onSave\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    value: title,\n                    onChange: (e)=>setTitle(e.target.value),\n                    placeholder: \"Enter title...\",\n                    className: \"w-full text-2xl font-bold bg-transparent border-none outline-none text-gray-900 placeholder-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    value: content,\n                    onChange: (e)=>setContent(e.target.value),\n                    placeholder: \"Start writing your journal entry...\",\n                    className: \"w-full h-full bg-transparent border-none outline-none resize-none text-gray-900 placeholder-gray-500 leading-relaxed\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200 text-sm text-gray-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"Words: \",\n                                content.trim().split(/\\s+/).filter((word)=>word.length > 0).length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"Characters: \",\n                                content.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n        lineNumber: 230,\n        columnNumber: 5\n    }, this);\n}\n_s1(SimpleEditor, \"1URyN+jVAv4ErmCp+sIa5PA1j7M=\");\n_c1 = SimpleEditor;\nvar _c, _c1;\n$RefreshReg$(_c, \"JournalApp\");\n$RefreshReg$(_c1, \"SimpleEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0pvdXJuYWxBcHAudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUV5RTtBQU9uRDtBQUMrQjtBQU85QyxTQUFTVyxXQUFXLEtBQW1DO1FBQW5DLEVBQUVDLFlBQVksRUFBRSxFQUFtQixHQUFuQzs7SUFDekIsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUdiLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ2MsY0FBY0MsZ0JBQWdCLEdBQUdmLCtDQUFRQSxDQUFzQjtJQUN0RSxNQUFNLENBQUNnQixTQUFTQyxXQUFXLEdBQUdqQiwrQ0FBUUEsQ0FBaUIsRUFBRTtJQUN6RCxNQUFNLENBQUNrQixjQUFjQyxnQkFBZ0IsR0FBR25CLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ29CLFlBQVlDLGNBQWMsR0FBR3JCLCtDQUFRQSxDQUFDO0lBRTdDLHdEQUF3RDtJQUN4RCxNQUFNc0IsVUFBVWIsa0VBQWVBO0lBRS9CLCtCQUErQjtJQUMvQlIsZ0RBQVNBO2dDQUFDO1lBQ1IsTUFBTXNCLFNBQVNELFFBQVFFLGdCQUFnQjtZQUN2QyxJQUFJRCxRQUFRO2dCQUNWTixXQUFXTSxPQUFPUCxPQUFPO1lBQzNCO1FBQ0Y7K0JBQUc7UUFBQ007S0FBUTtJQUVaLE1BQU1HLGlCQUFpQnZCLGtEQUFXQTtrREFBQztZQUNqQ3dCLFFBQVFDLEdBQUcsQ0FBQztZQUNaLE1BQU1DLFdBQXlCO2dCQUM3QkMsSUFBSSxTQUF1QkMsT0FBZEMsS0FBS0MsR0FBRyxJQUFHLEtBQTJDLE9BQXhDRixLQUFLRyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxNQUFNLENBQUMsR0FBRztnQkFDaEVDLE9BQU87Z0JBQ1BDLFNBQVM7Z0JBQ1RDLE1BQU0sRUFBRTtnQkFDUkMsWUFBWSxJQUFJUixPQUFPUyxXQUFXO2dCQUNsQ0MsWUFBWSxJQUFJVixPQUFPUyxXQUFXO2dCQUNsQ0UsWUFBWTtZQUNkO1lBRUEzQixnQkFBZ0JhO1lBQ2hCWDswREFBVzBCLENBQUFBLE9BQVE7d0JBQUNmOzJCQUFhZTtxQkFBSzs7WUFDdENyQixRQUFRc0IsVUFBVSxDQUFDaEI7UUFDckI7aURBQUc7UUFBQ047S0FBUTtJQUVaLE1BQU11QixjQUFjM0Msa0RBQVdBOytDQUFDLENBQUM0QztZQUMvQi9CLGdCQUFnQitCO1FBQ2xCOzhDQUFHLEVBQUU7SUFFTCxNQUFNQyxZQUFZN0Msa0RBQVdBOzZDQUFDLENBQUM4QztZQUM3QixJQUFJLENBQUNsQyxjQUFjO1lBRW5CLE1BQU1tQyxlQUE2QjtnQkFDakMsR0FBR25DLFlBQVk7Z0JBQ2ZzQixPQUFPWSxLQUFLWixLQUFLLElBQUk7Z0JBQ3JCQyxTQUFTVyxLQUFLWCxPQUFPO2dCQUNyQkMsTUFBTVUsS0FBS1YsSUFBSTtnQkFDZkcsWUFBWSxJQUFJVixPQUFPUyxXQUFXO2dCQUNsQ0UsWUFBWU0sS0FBS1gsT0FBTyxDQUFDYSxJQUFJLEdBQUdDLEtBQUssQ0FBQyxPQUFPQyxNQUFNO3lEQUFDQyxDQUFBQSxPQUFRQSxLQUFLQyxNQUFNLEdBQUc7d0RBQUdBLE1BQU07WUFDckY7WUFFQXZDLGdCQUFnQmtDO1lBQ2hCLE1BQU1NLGFBQWF2QyxRQUFRd0MsR0FBRztnRUFBQ1YsQ0FBQUEsUUFDN0JBLE1BQU1qQixFQUFFLEtBQUtvQixhQUFhcEIsRUFBRSxHQUFHb0IsZUFBZUg7O1lBRWhEN0IsV0FBV3NDO1lBQ1hqQyxRQUFRc0IsVUFBVSxDQUFDSztRQUNyQjs0Q0FBRztRQUFDbkM7UUFBY0U7UUFBU007S0FBUTtJQUVuQyxxQkFDRSw4REFBQ21DO1FBQUk5QyxXQUFVOzswQkFFYiw4REFBQzhDO2dCQUFJOUMsV0FBVyxHQUFnQyxPQUE3QkMsY0FBYyxTQUFTLE9BQU07O2tDQUM5Qyw4REFBQzZDO3dCQUFJOUMsV0FBVTs7MENBQ2IsOERBQUMrQztnQ0FBRy9DLFdBQVU7MENBQWtDOzs7Ozs7MENBQ2hELDhEQUFDOEM7Z0NBQUk5QyxXQUFVOztrREFDYiw4REFBQ2dEO3dDQUNDQyxTQUFTOzRDQUNQbEMsUUFBUUMsR0FBRyxDQUFDOzRDQUNaRjt3Q0FDRjt3Q0FDQWQsV0FBVTs7MERBRVYsOERBQUNSLG9IQUFVQTtnREFBQ1EsV0FBVTs7Ozs7OzBEQUN0Qiw4REFBQ2tEOzBEQUFLOzs7Ozs7Ozs7Ozs7a0RBRVIsOERBQUNGO3dDQUNDQyxTQUFTLElBQU12QyxjQUFjO3dDQUM3QlYsV0FBVTt3Q0FDVnlCLE9BQU07a0RBRU4sNEVBQUM1QixvSEFBTUE7NENBQUNHLFdBQVU7Ozs7Ozs7Ozs7O2tEQUVwQiw4REFBQ2dEO3dDQUNDQyxTQUFTLElBQU16QyxnQkFBZ0I7d0NBQy9CUixXQUFVO3dDQUNWeUIsT0FBTTtrREFFTiw0RUFBQzdCLG9IQUFZQTs0Q0FBQ0ksV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTTlCLDhEQUFDOEM7d0JBQUk5QyxXQUFVO2tDQUNiLDRFQUFDOEM7NEJBQUk5QyxXQUFVOzs4Q0FDYiw4REFBQ21EO29DQUFHbkQsV0FBVTs7d0NBQXlDO3dDQUMzQ0ssUUFBUXNDLE1BQU07d0NBQUM7Ozs7Ozs7OENBRTNCLDhEQUFDRztvQ0FBSTlDLFdBQVU7O3dDQUNaSyxRQUFRd0MsR0FBRyxDQUFDLENBQUNWLHNCQUNaLDhEQUFDVztnREFFQ0csU0FBUyxJQUFNZixZQUFZQztnREFDM0JuQyxXQUFXLDhGQUtSLE9BSENHLENBQUFBLHlCQUFBQSxtQ0FBQUEsYUFBY2UsRUFBRSxNQUFLaUIsTUFBTWpCLEVBQUUsR0FDM0Isc0NBQ0Esb0JBQ0g7O2tFQUdILDhEQUFDa0M7d0RBQUdwRCxXQUFVO2tFQUNYbUMsTUFBTVYsS0FBSyxJQUFJOzs7Ozs7a0VBRWxCLDhEQUFDNEI7d0RBQUVyRCxXQUFVO2tFQUNWbUMsTUFBTVQsT0FBTyxJQUFJOzs7Ozs7a0VBRXBCLDhEQUFDb0I7d0RBQUk5QyxXQUFVOzswRUFDYiw4REFBQ2tEO2dFQUFLbEQsV0FBVTswRUFDYixJQUFJb0IsS0FBS2UsTUFBTUwsVUFBVSxFQUFFd0Isa0JBQWtCOzs7Ozs7MEVBRWhELDhEQUFDSjtnRUFBS2xELFdBQVU7O29FQUNibUMsTUFBTUosVUFBVTtvRUFBQzs7Ozs7Ozs7Ozs7Ozs7K0NBckJqQkksTUFBTWpCLEVBQUU7Ozs7O3dDQTJCaEJiLFFBQVFzQyxNQUFNLEtBQUssbUJBQ2xCLDhEQUFDRzs0Q0FBSTlDLFdBQVU7OzhEQUNiLDhEQUFDTixvSEFBUUE7b0RBQUNNLFdBQVU7Ozs7Ozs4REFDcEIsOERBQUNxRDtvREFBRXJELFdBQVU7OERBQWdCOzs7Ozs7OERBQzdCLDhEQUFDZ0Q7b0RBQ0NDLFNBQVNuQztvREFDVGQsV0FBVTs4REFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBV2IsOERBQUM4QztnQkFBSTlDLFdBQVU7O2tDQUViLDhEQUFDOEM7d0JBQUk5QyxXQUFVO2tDQUNiLDRFQUFDZ0Q7NEJBQ0NDLFNBQVM7Z0NBQ1BsQyxRQUFRQyxHQUFHLENBQUMsd0JBQXdCZjtnQ0FDcENDLGVBQWUsQ0FBQ0Q7NEJBQ2xCOzRCQUNBRCxXQUFVO3NDQUVWLDRFQUFDUCxvSEFBSUE7Z0NBQUNPLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBS3BCLDhEQUFDOEM7d0JBQUk5QyxXQUFVO2tDQUNaRyw2QkFDQyw4REFBQ29EOzRCQUNDcEIsT0FBT2hDOzRCQUNQcUQsUUFBUXBCOzs7OztpREFHViw4REFBQ1U7NEJBQUk5QyxXQUFVO3NDQUNiLDRFQUFDOEM7Z0NBQUk5QyxXQUFVOztrREFDYiw4REFBQ04sb0hBQVFBO3dDQUFDTSxXQUFVOzs7Ozs7a0RBQ3BCLDhEQUFDeUQ7d0NBQUd6RCxXQUFVO2tEQUF5Qzs7Ozs7O2tEQUN2RCw4REFBQ3FEO3dDQUFFckQsV0FBVTtrREFBcUI7Ozs7OztrREFDbEMsOERBQUNnRDt3Q0FDQ0MsU0FBU25DO3dDQUNUZCxXQUFVOzswREFFViw4REFBQ1Isb0hBQVVBO2dEQUFDUSxXQUFVOzs7Ozs7MERBQ3RCLDhEQUFDa0Q7MERBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFTeEI7R0E1TGdCbkQ7S0FBQUE7QUFvTWhCLFNBQVN3RCxhQUFhLEtBQW9DO1FBQXBDLEVBQUVwQixLQUFLLEVBQUVxQixNQUFNLEVBQXFCLEdBQXBDOztJQUNwQixNQUFNLENBQUMvQixPQUFPaUMsU0FBUyxHQUFHdEUscURBQWMsQ0FBQytDLE1BQU1WLEtBQUs7SUFDcEQsTUFBTSxDQUFDQyxTQUFTaUMsV0FBVyxHQUFHdkUscURBQWMsQ0FBQytDLE1BQU1ULE9BQU87SUFFMUQsMEJBQTBCO0lBQzFCdEMsc0RBQWU7a0NBQUM7WUFDZCxNQUFNd0UsUUFBUUM7Z0RBQVc7b0JBQ3ZCLElBQUlwQyxVQUFVVSxNQUFNVixLQUFLLElBQUlDLFlBQVlTLE1BQU1ULE9BQU8sRUFBRTt3QkFDdEQ4QixPQUFPOzRCQUFFL0I7NEJBQU9DOzRCQUFTQyxNQUFNUSxNQUFNUixJQUFJO3dCQUFDO29CQUM1QztnQkFDRjsrQ0FBRztZQUVIOzBDQUFPLElBQU1tQyxhQUFhRjs7UUFDNUI7aUNBQUc7UUFBQ25DO1FBQU9DO1FBQVNTLE1BQU1WLEtBQUs7UUFBRVUsTUFBTVQsT0FBTztRQUFFUyxNQUFNUixJQUFJO1FBQUU2QjtLQUFPO0lBRW5FLHFCQUNFLDhEQUFDVjtRQUFJOUMsV0FBVTs7MEJBQ2IsOERBQUM4QztnQkFBSTlDLFdBQVU7MEJBQ2IsNEVBQUMrRDtvQkFDQ0MsTUFBSztvQkFDTEMsT0FBT3hDO29CQUNQeUMsVUFBVSxDQUFDQyxJQUFNVCxTQUFTUyxFQUFFQyxNQUFNLENBQUNILEtBQUs7b0JBQ3hDSSxhQUFZO29CQUNackUsV0FBVTs7Ozs7Ozs7Ozs7MEJBR2QsOERBQUM4QztnQkFBSTlDLFdBQVU7MEJBQ2IsNEVBQUNzRTtvQkFDQ0wsT0FBT3ZDO29CQUNQd0MsVUFBVSxDQUFDQyxJQUFNUixXQUFXUSxFQUFFQyxNQUFNLENBQUNILEtBQUs7b0JBQzFDSSxhQUFZO29CQUNackUsV0FBVTs7Ozs7Ozs7Ozs7MEJBR2QsOERBQUM4QztnQkFBSTlDLFdBQVU7MEJBQ2IsNEVBQUM4QztvQkFBSTlDLFdBQVU7O3NDQUNiLDhEQUFDa0Q7O2dDQUFLO2dDQUFReEIsUUFBUWEsSUFBSSxHQUFHQyxLQUFLLENBQUMsT0FBT0MsTUFBTSxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLQyxNQUFNLEdBQUcsR0FBR0EsTUFBTTs7Ozs7OztzQ0FDaEYsOERBQUNPOztnQ0FBSztnQ0FBYXhCLFFBQVFpQixNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLM0M7SUExQ1NZO01BQUFBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHh5aWVuXFxPbmVEcml2ZVxcRGVza3RvcFxcQWxsSm91cm5hbFxcam91cm5hbC1hcHBcXHNyY1xcY29tcG9uZW50c1xcSm91cm5hbEFwcC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2ssIHVzZU1lbW8gfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQge1xuICBQbHVzQ2lyY2xlLFxuICBNZW51LFxuICBCb29rT3BlbixcbiAgU2V0dGluZ3MgYXMgU2V0dGluZ3NJY29uLFxuICBVcGxvYWRcbn0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IGdldExvY2FsU3RvcmFnZSB9IGZyb20gJ0AvbGliL2xvY2FsU3RvcmFnZSc7XG5pbXBvcnQgeyBKb3VybmFsRW50cnkgfSBmcm9tICdAL2xpYi9kYXRhYmFzZSc7XG5cbmludGVyZmFjZSBKb3VybmFsQXBwUHJvcHMge1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBKb3VybmFsQXBwKHsgY2xhc3NOYW1lID0gJycgfTogSm91cm5hbEFwcFByb3BzKSB7XG4gIGNvbnN0IFtzaWRlYmFyT3Blbiwgc2V0U2lkZWJhck9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbY3VycmVudEVudHJ5LCBzZXRDdXJyZW50RW50cnldID0gdXNlU3RhdGU8Sm91cm5hbEVudHJ5IHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtlbnRyaWVzLCBzZXRFbnRyaWVzXSA9IHVzZVN0YXRlPEpvdXJuYWxFbnRyeVtdPihbXSk7XG4gIGNvbnN0IFtzaG93U2V0dGluZ3MsIHNldFNob3dTZXR0aW5nc10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93VXBsb2FkLCBzZXRTaG93VXBsb2FkXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICAvLyBNZW1vaXplIHN0b3JhZ2UgdG8gcHJldmVudCByZWNyZWF0aW9uIG9uIGV2ZXJ5IHJlbmRlclxuICBjb25zdCBzdG9yYWdlID0gZ2V0TG9jYWxTdG9yYWdlKCk7XG5cbiAgLy8gTG9hZCBjYWNoZWQgZW50cmllcyBvbiBtb3VudFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGNhY2hlZCA9IHN0b3JhZ2UuZ2V0Q2FjaGVkRW50cmllcygpO1xuICAgIGlmIChjYWNoZWQpIHtcbiAgICAgIHNldEVudHJpZXMoY2FjaGVkLmVudHJpZXMpO1xuICAgIH1cbiAgfSwgW3N0b3JhZ2VdKTtcblxuICBjb25zdCBjcmVhdGVOZXdFbnRyeSA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBjb25zb2xlLmxvZygnY3JlYXRlTmV3RW50cnkgY2FsbGVkIScpO1xuICAgIGNvbnN0IG5ld0VudHJ5OiBKb3VybmFsRW50cnkgPSB7XG4gICAgICBpZDogYGVudHJ5XyR7RGF0ZS5ub3coKX1fJHtNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgOSl9YCxcbiAgICAgIHRpdGxlOiAnJyxcbiAgICAgIGNvbnRlbnQ6ICcnLFxuICAgICAgdGFnczogW10sXG4gICAgICBjcmVhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICB3b3JkX2NvdW50OiAwLFxuICAgIH07XG5cbiAgICBzZXRDdXJyZW50RW50cnkobmV3RW50cnkpO1xuICAgIHNldEVudHJpZXMocHJldiA9PiBbbmV3RW50cnksIC4uLnByZXZdKTtcbiAgICBzdG9yYWdlLmNhY2hlRW50cnkobmV3RW50cnkpO1xuICB9LCBbc3RvcmFnZV0pO1xuXG4gIGNvbnN0IHNlbGVjdEVudHJ5ID0gdXNlQ2FsbGJhY2soKGVudHJ5OiBKb3VybmFsRW50cnkpID0+IHtcbiAgICBzZXRDdXJyZW50RW50cnkoZW50cnkpO1xuICB9LCBbXSk7XG5cbiAgY29uc3Qgc2F2ZUVudHJ5ID0gdXNlQ2FsbGJhY2soKGRhdGE6IHsgdGl0bGU6IHN0cmluZzsgY29udGVudDogc3RyaW5nOyB0YWdzOiBzdHJpbmdbXSB9KSA9PiB7XG4gICAgaWYgKCFjdXJyZW50RW50cnkpIHJldHVybjtcblxuICAgIGNvbnN0IHVwZGF0ZWRFbnRyeTogSm91cm5hbEVudHJ5ID0ge1xuICAgICAgLi4uY3VycmVudEVudHJ5LFxuICAgICAgdGl0bGU6IGRhdGEudGl0bGUgfHwgJ1VudGl0bGVkIEVudHJ5JyxcbiAgICAgIGNvbnRlbnQ6IGRhdGEuY29udGVudCxcbiAgICAgIHRhZ3M6IGRhdGEudGFncyxcbiAgICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgIHdvcmRfY291bnQ6IGRhdGEuY29udGVudC50cmltKCkuc3BsaXQoL1xccysvKS5maWx0ZXIod29yZCA9PiB3b3JkLmxlbmd0aCA+IDApLmxlbmd0aCxcbiAgICB9O1xuXG4gICAgc2V0Q3VycmVudEVudHJ5KHVwZGF0ZWRFbnRyeSk7XG4gICAgY29uc3QgbmV3RW50cmllcyA9IGVudHJpZXMubWFwKGVudHJ5ID0+XG4gICAgICBlbnRyeS5pZCA9PT0gdXBkYXRlZEVudHJ5LmlkID8gdXBkYXRlZEVudHJ5IDogZW50cnlcbiAgICApO1xuICAgIHNldEVudHJpZXMobmV3RW50cmllcyk7XG4gICAgc3RvcmFnZS5jYWNoZUVudHJ5KHVwZGF0ZWRFbnRyeSk7XG4gIH0sIFtjdXJyZW50RW50cnksIGVudHJpZXMsIHN0b3JhZ2VdKTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLXNjcmVlbiBiZy1ncmF5LTUwXCI+XG4gICAgICB7LyogU2lkZWJhciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtgJHtzaWRlYmFyT3BlbiA/ICd3LTgwJyA6ICd3LTAnfSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgb3ZlcmZsb3ctaGlkZGVuIGJnLXdoaXRlIGJvcmRlci1yIGJvcmRlci1ncmF5LTIwMCBmbGV4IGZsZXgtY29sYH0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+QWxsSm91cm5hbDwvaDE+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMiBtdC00XCI+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnQ3JlYXRlIE5ldyBFbnRyeSBidXR0b24gY2xpY2tlZCEnKTtcbiAgICAgICAgICAgICAgICBjcmVhdGVOZXdFbnRyeSgpO1xuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc3BhY2UteC0yIHB4LTMgcHktMiBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctYmx1ZS03MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8UGx1c0NpcmNsZSBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4+TmV3IEVudHJ5PC9zcGFuPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dVcGxvYWQodHJ1ZSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiB0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtZ3JheS04MDAgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGhvdmVyOmJnLWdyYXktNTAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICB0aXRsZT1cIlVwbG9hZCBGaWxlc1wiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxVcGxvYWQgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd1NldHRpbmdzKHRydWUpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgdGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktODAwIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmF5LTUwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgdGl0bGU9XCJTZXR0aW5nc1wiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxTZXR0aW5nc0ljb24gY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEVudHJpZXMgTGlzdCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItM1wiPlxuICAgICAgICAgICAgICBFbnRyaWVzICh7ZW50cmllcy5sZW5ndGh9KVxuICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgIHtlbnRyaWVzLm1hcCgoZW50cnkpID0+IChcbiAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICBrZXk9e2VudHJ5LmlkfVxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2VsZWN0RW50cnkoZW50cnkpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgXG4gICAgICAgICAgICAgICAgICAgIHAtMyByb3VuZGVkLWxnIGN1cnNvci1wb2ludGVyIHRyYW5zaXRpb24tY29sb3JzXG4gICAgICAgICAgICAgICAgICAgICR7Y3VycmVudEVudHJ5Py5pZCA9PT0gZW50cnkuaWRcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTUwIGJvcmRlciBib3JkZXItYmx1ZS0yMDAnXG4gICAgICAgICAgICAgICAgICAgICAgOiAnaG92ZXI6YmctZ3JheS01MCdcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgYH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCB0cnVuY2F0ZVwiPlxuICAgICAgICAgICAgICAgICAgICB7ZW50cnkudGl0bGUgfHwgJ1VudGl0bGVkIEVudHJ5J31cbiAgICAgICAgICAgICAgICAgIDwvaDQ+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgbXQtMSBsaW5lLWNsYW1wLTJcIj5cbiAgICAgICAgICAgICAgICAgICAge2VudHJ5LmNvbnRlbnQgfHwgJ05vIGNvbnRlbnQnfVxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbXQtMlwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7bmV3IERhdGUoZW50cnkudXBkYXRlZF9hdCkudG9Mb2NhbGVEYXRlU3RyaW5nKCl9XG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2VudHJ5LndvcmRfY291bnR9IHdvcmRzXG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApKX1cblxuICAgICAgICAgICAgICB7ZW50cmllcy5sZW5ndGggPT09IDAgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOFwiPlxuICAgICAgICAgICAgICAgICAgPEJvb2tPcGVuIGNsYXNzTmFtZT1cInctMTIgaC0xMiBteC1hdXRvIHRleHQtZ3JheS00MDAgbWItM1wiIC8+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+Tm8gZW50cmllcyB5ZXQ8L3A+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2NyZWF0ZU5ld0VudHJ5fVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0yIHRleHQtYmx1ZS02MDAgaG92ZXI6dW5kZXJsaW5lXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgQ3JlYXRlIHlvdXIgZmlyc3QgZW50cnlcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBNYWluIENvbnRlbnQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgIHsvKiBUb3AgQmFyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTQgYmctd2hpdGUgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICBjb25zb2xlLmxvZygnTWVudSBidXR0b24gY2xpY2tlZCEnLCBzaWRlYmFyT3Blbik7XG4gICAgICAgICAgICAgIHNldFNpZGViYXJPcGVuKCFzaWRlYmFyT3Blbik7XG4gICAgICAgICAgICB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIHRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1ncmF5LTgwMCByb3VuZGVkLWxnIGhvdmVyOmJnLWdyYXktMTAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8TWVudSBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIENvbnRlbnQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHAtNFwiPlxuICAgICAgICAgIHtjdXJyZW50RW50cnkgPyAoXG4gICAgICAgICAgICA8U2ltcGxlRWRpdG9yXG4gICAgICAgICAgICAgIGVudHJ5PXtjdXJyZW50RW50cnl9XG4gICAgICAgICAgICAgIG9uU2F2ZT17c2F2ZUVudHJ5fVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICApIDogKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLWZ1bGxcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxCb29rT3BlbiBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgbXgtYXV0byB0ZXh0LWdyYXktNDAwIG1iLTRcIiAvPlxuICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMlwiPldlbGNvbWUgdG8gQWxsSm91cm5hbDwvaDI+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi02XCI+Q3JlYXRlIGEgbmV3IGVudHJ5IHRvIHN0YXJ0IHdyaXRpbmcuPC9wPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2NyZWF0ZU5ld0VudHJ5fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc3BhY2UteC0yIHB4LTQgcHktMyBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctYmx1ZS03MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxQbHVzQ2lyY2xlIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4+Q3JlYXRlIE5ldyBFbnRyeTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuXG4vLyBTaW1wbGUgZWRpdG9yIGNvbXBvbmVudCB0byBhdm9pZCBjb21wbGV4aXR5XG5pbnRlcmZhY2UgU2ltcGxlRWRpdG9yUHJvcHMge1xuICBlbnRyeTogSm91cm5hbEVudHJ5O1xuICBvblNhdmU6IChkYXRhOiB7IHRpdGxlOiBzdHJpbmc7IGNvbnRlbnQ6IHN0cmluZzsgdGFnczogc3RyaW5nW10gfSkgPT4gdm9pZDtcbn1cblxuZnVuY3Rpb24gU2ltcGxlRWRpdG9yKHsgZW50cnksIG9uU2F2ZSB9OiBTaW1wbGVFZGl0b3JQcm9wcykge1xuICBjb25zdCBbdGl0bGUsIHNldFRpdGxlXSA9IFJlYWN0LnVzZVN0YXRlKGVudHJ5LnRpdGxlKTtcbiAgY29uc3QgW2NvbnRlbnQsIHNldENvbnRlbnRdID0gUmVhY3QudXNlU3RhdGUoZW50cnkuY29udGVudCk7XG5cbiAgLy8gQXV0by1zYXZlIHdpdGggZGVib3VuY2VcbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCB0aW1lciA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgaWYgKHRpdGxlICE9PSBlbnRyeS50aXRsZSB8fCBjb250ZW50ICE9PSBlbnRyeS5jb250ZW50KSB7XG4gICAgICAgIG9uU2F2ZSh7IHRpdGxlLCBjb250ZW50LCB0YWdzOiBlbnRyeS50YWdzIH0pO1xuICAgICAgfVxuICAgIH0sIDEwMDApO1xuXG4gICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0aW1lcik7XG4gIH0sIFt0aXRsZSwgY29udGVudCwgZW50cnkudGl0bGUsIGVudHJ5LmNvbnRlbnQsIGVudHJ5LnRhZ3MsIG9uU2F2ZV0pO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdyBoLWZ1bGwgZmxleCBmbGV4LWNvbFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgIDxpbnB1dFxuICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICB2YWx1ZT17dGl0bGV9XG4gICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRUaXRsZShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB0aXRsZS4uLlwiXG4gICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHRleHQtMnhsIGZvbnQtYm9sZCBiZy10cmFuc3BhcmVudCBib3JkZXItbm9uZSBvdXRsaW5lLW5vbmUgdGV4dC1ncmF5LTkwMCBwbGFjZWhvbGRlci1ncmF5LTUwMFwiXG4gICAgICAgIC8+XG4gICAgICA8L2Rpdj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHAtNFwiPlxuICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICB2YWx1ZT17Y29udGVudH1cbiAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvbnRlbnQoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU3RhcnQgd3JpdGluZyB5b3VyIGpvdXJuYWwgZW50cnkuLi5cIlxuICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgYmctdHJhbnNwYXJlbnQgYm9yZGVyLW5vbmUgb3V0bGluZS1ub25lIHJlc2l6ZS1ub25lIHRleHQtZ3JheS05MDAgcGxhY2Vob2xkZXItZ3JheS01MDAgbGVhZGluZy1yZWxheGVkXCJcbiAgICAgICAgLz5cbiAgICAgIDwvZGl2PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwIHRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgPHNwYW4+V29yZHM6IHtjb250ZW50LnRyaW0oKS5zcGxpdCgvXFxzKy8pLmZpbHRlcih3b3JkID0+IHdvcmQubGVuZ3RoID4gMCkubGVuZ3RofTwvc3Bhbj5cbiAgICAgICAgICA8c3Bhbj5DaGFyYWN0ZXJzOiB7Y29udGVudC5sZW5ndGh9PC9zcGFuPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsIlBsdXNDaXJjbGUiLCJNZW51IiwiQm9va09wZW4iLCJTZXR0aW5ncyIsIlNldHRpbmdzSWNvbiIsIlVwbG9hZCIsImdldExvY2FsU3RvcmFnZSIsIkpvdXJuYWxBcHAiLCJjbGFzc05hbWUiLCJzaWRlYmFyT3BlbiIsInNldFNpZGViYXJPcGVuIiwiY3VycmVudEVudHJ5Iiwic2V0Q3VycmVudEVudHJ5IiwiZW50cmllcyIsInNldEVudHJpZXMiLCJzaG93U2V0dGluZ3MiLCJzZXRTaG93U2V0dGluZ3MiLCJzaG93VXBsb2FkIiwic2V0U2hvd1VwbG9hZCIsInN0b3JhZ2UiLCJjYWNoZWQiLCJnZXRDYWNoZWRFbnRyaWVzIiwiY3JlYXRlTmV3RW50cnkiLCJjb25zb2xlIiwibG9nIiwibmV3RW50cnkiLCJpZCIsIk1hdGgiLCJEYXRlIiwibm93IiwicmFuZG9tIiwidG9TdHJpbmciLCJzdWJzdHIiLCJ0aXRsZSIsImNvbnRlbnQiLCJ0YWdzIiwiY3JlYXRlZF9hdCIsInRvSVNPU3RyaW5nIiwidXBkYXRlZF9hdCIsIndvcmRfY291bnQiLCJwcmV2IiwiY2FjaGVFbnRyeSIsInNlbGVjdEVudHJ5IiwiZW50cnkiLCJzYXZlRW50cnkiLCJkYXRhIiwidXBkYXRlZEVudHJ5IiwidHJpbSIsInNwbGl0IiwiZmlsdGVyIiwid29yZCIsImxlbmd0aCIsIm5ld0VudHJpZXMiLCJtYXAiLCJkaXYiLCJoMSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJzcGFuIiwiaDMiLCJoNCIsInAiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJTaW1wbGVFZGl0b3IiLCJvblNhdmUiLCJoMiIsInNldFRpdGxlIiwic2V0Q29udGVudCIsInRpbWVyIiwic2V0VGltZW91dCIsImNsZWFyVGltZW91dCIsImlucHV0IiwidHlwZSIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0IiwicGxhY2Vob2xkZXIiLCJ0ZXh0YXJlYSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/JournalApp.tsx\n"));

/***/ })

});