/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cxyien%5COneDrive%5CDesktop%5CAllJournal%5Cjournal-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cxyien%5COneDrive%5CDesktop%5CAllJournal%5Cjournal-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cxyien%5COneDrive%5CDesktop%5CAllJournal%5Cjournal-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cxyien%5COneDrive%5CDesktop%5CAllJournal%5Cjournal-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cxyien%5COneDrive%5CDesktop%5CAllJournal%5Cjournal-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cxyien%5COneDrive%5CDesktop%5CAllJournal%5Cjournal-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ThemeProvider.tsx */ \"(rsc)/./src/components/ThemeProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3h5aWVuJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDQWxsSm91cm5hbCU1QyU1Q2pvdXJuYWwtYXBwJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyU1QyU1QyU1QyU1Q2FwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3h5aWVuJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDQWxsSm91cm5hbCU1QyU1Q2pvdXJuYWwtYXBwJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDeHlpZW4lNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNBbGxKb3VybmFsJTVDJTVDam91cm5hbC1hcHAlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDVGhlbWVQcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUaGVtZVByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTEFBd0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRoZW1lUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFx4eWllblxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXEFsbEpvdXJuYWxcXFxcam91cm5hbC1hcHBcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcVGhlbWVQcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3h5aWVuJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDQWxsSm91cm5hbCU1QyU1Q2pvdXJuYWwtYXBwJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFxSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxceHlpZW5cXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxBbGxKb3VybmFsXFxcXGpvdXJuYWwtYXBwXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xceHlpZW5cXE9uZURyaXZlXFxEZXNrdG9wXFxBbGxKb3VybmFsXFxqb3VybmFsLWFwcFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"43e467f2a710\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHh5aWVuXFxPbmVEcml2ZVxcRGVza3RvcFxcQWxsSm91cm5hbFxcam91cm5hbC1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjQzZTQ2N2YyYTcxMFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_ThemeProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ThemeProvider */ \"(rsc)/./src/components/ThemeProvider.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"AllJournal - Your Ultimate Journal App\",\n    description: \"A powerful journal app with auto-save, tagging, file import, and customizable themes\",\n    keywords: \"journal, diary, writing, notes, auto-save, tags\",\n    authors: [\n        {\n            name: \"AllJournal\"\n        }\n    ]\n};\nconst viewport = {\n    width: 'device-width',\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                    name: \"theme-color\",\n                    content: \"#ffffff\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} antialiased`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeProvider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\AllJournal\\journal-app\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),
/* harmony export */   useTheme: () => (/* binding */ useTheme)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useTheme = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\AllJournal\\journal-app\\src\\components\\ThemeProvider.tsx",
"useTheme",
);const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\AllJournal\\journal-app\\src\\components\\ThemeProvider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ThemeProvider.tsx */ \"(ssr)/./src/components/ThemeProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3h5aWVuJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDQWxsSm91cm5hbCU1QyU1Q2pvdXJuYWwtYXBwJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyU1QyU1QyU1QyU1Q2FwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3h5aWVuJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDQWxsSm91cm5hbCU1QyU1Q2pvdXJuYWwtYXBwJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDeHlpZW4lNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNBbGxKb3VybmFsJTVDJTVDam91cm5hbC1hcHAlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDVGhlbWVQcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUaGVtZVByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTEFBd0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRoZW1lUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFx4eWllblxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXEFsbEpvdXJuYWxcXFxcam91cm5hbC1hcHBcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcVGhlbWVQcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3h5aWVuJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDQWxsSm91cm5hbCU1QyU1Q2pvdXJuYWwtYXBwJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFxSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxceHlpZW5cXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxBbGxKb3VybmFsXFxcXGpvdXJuYWwtYXBwXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cxyien%5C%5COneDrive%5C%5CDesktop%5C%5CAllJournal%5C%5Cjournal-app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_JournalApp__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/JournalApp */ \"(ssr)/./src/components/JournalApp.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_JournalApp__WEBPACK_IMPORTED_MODULE_1__.JournalApp, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRXFEO0FBRXRDLFNBQVNDO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFLQyxXQUFVO2tCQUNkLDRFQUFDSCw4REFBVUE7Ozs7Ozs7Ozs7QUFHakIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xceHlpZW5cXE9uZURyaXZlXFxEZXNrdG9wXFxBbGxKb3VybmFsXFxqb3VybmFsLWFwcFxcc3JjXFxhcHBcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgSm91cm5hbEFwcCB9IGZyb20gXCJAL2NvbXBvbmVudHMvSm91cm5hbEFwcFwiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICByZXR1cm4gKFxuICAgIDxtYWluIGNsYXNzTmFtZT1cImgtc2NyZWVuXCI+XG4gICAgICA8Sm91cm5hbEFwcCAvPlxuICAgIDwvbWFpbj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJKb3VybmFsQXBwIiwiSG9tZSIsIm1haW4iLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/FileUpload.tsx":
/*!***************************************!*\
  !*** ./src/components/FileUpload.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FileUpload: () => (/* binding */ FileUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"(ssr)/./node_modules/react-dropzone/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var mammoth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mammoth */ \"(ssr)/./node_modules/mammoth/lib/index.js\");\n/* __next_internal_client_entry_do_not_use__ FileUpload auto */ \n\n\n\n\nfunction FileUpload({ onFilesProcessed, onImport, className = '' }) {\n    const [uploadedFiles, setUploadedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const processTextFile = async (file)=>{\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                const content = e.target?.result;\n                resolve(content);\n            };\n            reader.onerror = ()=>reject(new Error('Failed to read file'));\n            reader.readAsText(file);\n        });\n    };\n    const processDocxFile = async (file)=>{\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.onload = async (e)=>{\n                try {\n                    const arrayBuffer = e.target?.result;\n                    const result = await mammoth__WEBPACK_IMPORTED_MODULE_3__.extractRawText({\n                        arrayBuffer\n                    });\n                    resolve(result.value);\n                } catch (error) {\n                    reject(new Error('Failed to process DOCX file'));\n                }\n            };\n            reader.onerror = ()=>reject(new Error('Failed to read file'));\n            reader.readAsArrayBuffer(file);\n        });\n    };\n    const extractTitle = (content, filename)=>{\n        // Try to extract title from first line\n        const lines = content.trim().split('\\n');\n        const firstLine = lines[0]?.trim();\n        if (firstLine && firstLine.length > 0 && firstLine.length < 100) {\n            // Remove common title markers\n            const cleanTitle = firstLine.replace(/^#+\\s*/, '') // Remove markdown headers\n            .replace(/^Title:\\s*/i, '') // Remove \"Title:\" prefix\n            .replace(/^Subject:\\s*/i, '') // Remove \"Subject:\" prefix\n            .trim();\n            if (cleanTitle.length > 0) {\n                return cleanTitle;\n            }\n        }\n        // Fallback to filename without extension\n        return filename.replace(/\\.[^/.]+$/, '');\n    };\n    const processFile = async (file)=>{\n        const id = crypto.randomUUID();\n        const uploadedFile = {\n            id,\n            name: file.name,\n            size: file.size,\n            type: file.type,\n            content: '',\n            title: '',\n            status: 'processing'\n        };\n        try {\n            let content;\n            if (file.type === 'text/plain' || file.name.endsWith('.txt')) {\n                content = await processTextFile(file);\n            } else if (file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' || file.name.endsWith('.docx')) {\n                content = await processDocxFile(file);\n            } else {\n                throw new Error('Unsupported file type. Please upload .txt or .docx files.');\n            }\n            const title = extractTitle(content, file.name);\n            return {\n                ...uploadedFile,\n                content: content.trim(),\n                title,\n                status: 'success'\n            };\n        } catch (error) {\n            return {\n                ...uploadedFile,\n                status: 'error',\n                error: error instanceof Error ? error.message : 'Unknown error occurred'\n            };\n        }\n    };\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"FileUpload.useCallback[onDrop]\": async (acceptedFiles)=>{\n            setIsProcessing(true);\n            const processedFiles = [];\n            for (const file of acceptedFiles){\n                const processed = await processFile(file);\n                processedFiles.push(processed);\n                // Update state incrementally\n                setUploadedFiles({\n                    \"FileUpload.useCallback[onDrop]\": (prev)=>[\n                            ...prev,\n                            processed\n                        ]\n                }[\"FileUpload.useCallback[onDrop]\"]);\n            }\n            setIsProcessing(false);\n            onFilesProcessed(processedFiles);\n        }\n    }[\"FileUpload.useCallback[onDrop]\"], [\n        onFilesProcessed\n    ]);\n    const { getRootProps, getInputProps, isDragActive } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: {\n            'text/plain': [\n                '.txt'\n            ],\n            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [\n                '.docx'\n            ]\n        },\n        multiple: true\n    });\n    const removeFile = (id)=>{\n        setUploadedFiles((prev)=>prev.filter((file)=>file.id !== id));\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    const getFileIcon = (type, name)=>{\n        if (type === 'text/plain' || name.endsWith('.txt')) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                lineNumber: 163,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"w-5 h-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n            lineNumber: 165,\n            columnNumber: 12\n        }, this);\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'success':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `space-y-6 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ...getRootProps(),\n                className: `\n          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors\n          ${isDragActive ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'}\n        `,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ...getInputProps()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-12 h-12 mx-auto mb-4 text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\",\n                        children: isDragActive ? 'Drop files here' : 'Upload your writings'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 dark:text-gray-400 mb-4\",\n                        children: \"Drag and drop files here, or click to select files\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-400 dark:text-gray-500\",\n                        children: \"Supports: .txt, .docx files\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this),\n            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-blue-700 dark:text-blue-300\",\n                        children: \"Processing files...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, this),\n            uploadedFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                        children: [\n                            \"Uploaded Files (\",\n                            uploadedFiles.length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: uploadedFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-500 dark:text-gray-400\",\n                                                children: getFileIcon(file.type, file.name)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\",\n                                                                children: file.title || file.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            getStatusIcon(file.status)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: file.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: formatFileSize(file.size)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            file.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"•\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                                                                        lineNumber: 245,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            file.content.split(/\\s+/).length,\n                                                                            \" words\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                                                                        lineNumber: 246,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    file.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-red-600 dark:text-red-400 mt-1\",\n                                                        children: file.error\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            file.status === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>onImport(file),\n                                                className: \"px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\",\n                                                children: \"Import\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>removeFile(file.id),\n                                                className: \"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, file.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n                lineNumber: 215,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\FileUpload.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FileUpload.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/JournalApp.tsx":
/*!***************************************!*\
  !*** ./src/components/JournalApp.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JournalApp: () => (/* binding */ JournalApp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Menu,PlusCircle,Settings,Upload,X,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _TextEditor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TextEditor */ \"(ssr)/./src/components/TextEditor.tsx\");\n/* harmony import */ var _TagManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TagManager */ \"(ssr)/./src/components/TagManager.tsx\");\n/* harmony import */ var _FileUpload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FileUpload */ \"(ssr)/./src/components/FileUpload.tsx\");\n/* harmony import */ var _Settings__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Settings */ \"(ssr)/./src/components/Settings.tsx\");\n/* harmony import */ var _lib_localStorage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/localStorage */ \"(ssr)/./src/lib/localStorage.ts\");\n/* harmony import */ var _lib_testData__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/testData */ \"(ssr)/./src/lib/testData.ts\");\n/* __next_internal_client_entry_do_not_use__ JournalApp auto */ \n\n\n\n\n\n\n\n\nfunction JournalApp({ className = '' }) {\n    const [currentEntry, setCurrentEntry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [entries, setEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUpload, setShowUpload] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Start closed on mobile\n    const [zoom, setZoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const storage = (0,_lib_localStorage__WEBPACK_IMPORTED_MODULE_6__.getLocalStorage)();\n    // Update available tags from all entries\n    const updateAvailableTags = (entriesList)=>{\n        const allTags = new Set();\n        entriesList.forEach((entry)=>{\n            entry.tags.forEach((tag)=>allTags.add(tag));\n        });\n        storage.cacheTags([\n            ...allTags\n        ]);\n    };\n    // Detect mobile and load cached entries on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"JournalApp.useEffect\": ()=>{\n            const checkMobile = {\n                \"JournalApp.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                    setSidebarOpen(window.innerWidth >= 768); // Open sidebar on desktop by default\n                }\n            }[\"JournalApp.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            const cached = storage.getCachedEntries();\n            if (cached) {\n                setEntries(cached.entries);\n                updateAvailableTags(cached.entries);\n            }\n            return ({\n                \"JournalApp.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"JournalApp.useEffect\"];\n        }\n    }[\"JournalApp.useEffect\"], []);\n    // Filter entries based on search and tags\n    const filteredEntries = entries.filter((entry)=>{\n        const matchesSearch = !searchQuery || entry.title.toLowerCase().includes(searchQuery.toLowerCase()) || entry.content.toLowerCase().includes(searchQuery.toLowerCase()) || entry.tags.some((tag)=>tag.toLowerCase().includes(searchQuery.toLowerCase()));\n        const matchesTags = selectedTags.length === 0 || selectedTags.every((tag)=>entry.tags.includes(tag));\n        return matchesSearch && matchesTags;\n    });\n    const createNewEntry = ()=>{\n        console.log('createNewEntry clicked!');\n        const newEntry = {\n            id: `entry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            title: '',\n            content: '',\n            tags: [],\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n            word_count: 0\n        };\n        setCurrentEntry(newEntry);\n        setEntries((prev)=>[\n                newEntry,\n                ...prev\n            ]);\n        storage.cacheEntry(newEntry);\n        // Close sidebar on mobile when creating new entry\n        if (isMobile) {\n            setSidebarOpen(false);\n        }\n    };\n    const saveEntry = (data)=>{\n        if (!currentEntry) return;\n        const updatedEntry = {\n            ...currentEntry,\n            title: data.title || 'Untitled Entry',\n            content: data.content,\n            tags: data.tags,\n            updated_at: new Date().toISOString(),\n            word_count: data.content.trim().split(/\\s+/).filter((word)=>word.length > 0).length\n        };\n        setCurrentEntry(updatedEntry);\n        const newEntries = entries.map((entry)=>entry.id === updatedEntry.id ? updatedEntry : entry);\n        setEntries(newEntries);\n        storage.cacheEntry(updatedEntry);\n        // Update available tags from all entries\n        updateAvailableTags(newEntries);\n    };\n    const selectEntry = (entry)=>{\n        setCurrentEntry(entry);\n        // Close sidebar on mobile when selecting entry\n        if (isMobile) {\n            setSidebarOpen(false);\n        }\n    };\n    const deleteEntry = (entryId)=>{\n        setEntries((prev)=>prev.filter((entry)=>entry.id !== entryId));\n        storage.removeCachedEntry(entryId);\n        if (currentEntry?.id === entryId) {\n            setCurrentEntry(null);\n        }\n    };\n    const handleSearch = (query, tags)=>{\n        setSearchQuery(query);\n        setSelectedTags(tags);\n    };\n    const handleFileImport = (file)=>{\n        const newEntry = {\n            id: crypto.randomUUID(),\n            title: file.title,\n            content: file.content,\n            tags: [],\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n            word_count: file.content.split(/\\s+/).filter((word)=>word.length > 0).length\n        };\n        const newEntries = [\n            newEntry,\n            ...entries\n        ];\n        setEntries(newEntries);\n        storage.cacheEntry(newEntry);\n        setCurrentEntry(newEntry);\n        setShowUpload(false);\n        updateAvailableTags(newEntries);\n    };\n    const adjustZoom = (delta)=>{\n        const newZoom = Math.max(50, Math.min(200, zoom + delta));\n        setZoom(newZoom);\n    };\n    const loadSampleEntries = ()=>{\n        const sampleEntries = (0,_lib_testData__WEBPACK_IMPORTED_MODULE_7__.loadSampleData)();\n        if (sampleEntries.length > 0) {\n            setEntries(sampleEntries);\n            updateAvailableTags(sampleEntries);\n        }\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', {\n            month: 'short',\n            day: 'numeric',\n            year: 'numeric'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex h-screen bg-gray-50 dark:bg-gray-900 ${className}`,\n        children: [\n            isMobile && sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-40\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 194,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `\n        ${sidebarOpen ? isMobile ? 'w-80' : 'w-80' : 'w-0'}\n        ${isMobile ? 'fixed left-0 top-0 h-full z-50' : 'relative'}\n        transition-all duration-300 overflow-hidden\n        bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700\n        flex flex-col\n      `,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 md:p-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3 md:mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-lg md:text-xl font-bold text-gray-900 dark:text-gray-100\",\n                                        children: \"AllJournal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 13\n                                    }, this),\n                                    !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>adjustZoom(-10),\n                                                className: \"p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300\",\n                                                title: \"Zoom Out\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                children: [\n                                                    zoom,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>adjustZoom(10),\n                                                className: \"p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300\",\n                                                title: \"Zoom In\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: createNewEntry,\n                                        className: \"flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm md:text-base\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: isMobile ? 'New' : 'New Entry'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowUpload(true),\n                                        className: \"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                        title: \"Upload Files\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowSettings(true),\n                                        className: \"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                        title: \"Settings\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 md:p-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TagManager__WEBPACK_IMPORTED_MODULE_3__.TagManager, {\n                            selectedTags: selectedTags,\n                            onTagsChange: setSelectedTags,\n                            onSearch: handleSearch\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 md:p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                        children: [\n                                            \"Entries (\",\n                                            filteredEntries.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        filteredEntries.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                onClick: ()=>selectEntry(entry),\n                                                className: `\n                    p-3 rounded-lg cursor-pointer transition-colors\n                    ${currentEntry?.id === entry.id ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' : 'hover:bg-gray-50 dark:hover:bg-gray-700'}\n                  `,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 dark:text-gray-100 truncate text-sm md:text-base\",\n                                                        children: entry.title || 'Untitled Entry'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs md:text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2\",\n                                                        children: entry.content || 'No content'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mt-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: formatDate(entry.updated_at)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: [\n                                                                    entry.word_count,\n                                                                    \" words\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    entry.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mt-2\",\n                                                        children: [\n                                                            entry.tags.slice(0, isMobile ? 2 : 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs px-2 py-1 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded\",\n                                                                    children: [\n                                                                        \"#\",\n                                                                        tag\n                                                                    ]\n                                                                }, tag, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 25\n                                                                }, this)),\n                                                            entry.tags.length > (isMobile ? 2 : 3) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: [\n                                                                    \"+\",\n                                                                    entry.tags.length - (isMobile ? 2 : 3)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, entry.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this)),\n                                        filteredEntries.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-12 h-12 mx-auto text-gray-400 mb-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 dark:text-gray-400\",\n                                                    children: entries.length === 0 ? 'No entries yet' : 'No entries match your search'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 19\n                                                }, this),\n                                                entries.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: createNewEntry,\n                                                    className: \"mt-2 text-blue-600 dark:text-blue-400 hover:underline\",\n                                                    children: \"Create your first entry\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `flex-1 flex flex-col ${isMobile && sidebarOpen ? 'pointer-events-none' : ''}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-3 md:p-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            console.log('Menu button clicked!', sidebarOpen);\n                                            setSidebarOpen(!sidebarOpen);\n                                        },\n                                        className: \"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 13\n                                    }, this),\n                                    isMobile && currentEntry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-medium text-gray-900 dark:text-gray-100 truncate\",\n                                        children: currentEntry.title || 'Untitled Entry'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, this),\n                            currentEntry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 md:space-x-4\",\n                                children: [\n                                    !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400 hidden sm:block\",\n                                        children: [\n                                            \"Last saved: \",\n                                            formatDate(currentEntry.updated_at)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>deleteEntry(currentEntry.id),\n                                        className: \"text-sm text-red-600 dark:text-red-400 hover:underline\",\n                                        children: isMobile ? 'Delete' : 'Delete Entry'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-2 md:p-0\",\n                        style: {\n                            zoom: `${zoom}%`\n                        },\n                        children: currentEntry ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TextEditor__WEBPACK_IMPORTED_MODULE_2__.TextEditor, {\n                            entryId: currentEntry.id,\n                            initialTitle: currentEntry.title,\n                            initialContent: currentEntry.content,\n                            initialTags: currentEntry.tags,\n                            onSave: saveEntry,\n                            onAutoSave: saveEntry,\n                            className: \"h-full\"\n                        }, currentEntry.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-full p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center max-w-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-12 h-12 md:w-16 md:h-16 mx-auto text-gray-400 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg md:text-xl font-medium text-gray-900 dark:text-gray-100 mb-2\",\n                                        children: \"Welcome to AllJournal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm md:text-base text-gray-600 dark:text-gray-400 mb-6\",\n                                        children: isMobile ? \"Tap the menu to browse entries or create a new one to start writing.\" : \"Select an entry from the sidebar or create a new one to start writing.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: createNewEntry,\n                                                className: \"flex items-center justify-center space-x-2 w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Create New Entry\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 19\n                                            }, this),\n                                            isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSidebarOpen(true),\n                                                className: \"flex items-center justify-center space-x-2 w-full px-4 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Browse Entries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 21\n                                            }, this),\n                                            entries.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: loadSampleEntries,\n                                                className: \"flex items-center justify-center space-x-2 w-full px-4 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Load Sample Entries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 351,\n                columnNumber: 7\n            }, this),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Settings__WEBPACK_IMPORTED_MODULE_5__.Settings, {\n                isOpen: showSettings,\n                onClose: ()=>setShowSettings(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 453,\n                columnNumber: 9\n            }, this),\n            showUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-gray-100\",\n                                    children: \"Import Files\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowUpload(false),\n                                    className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Menu_PlusCircle_Settings_Upload_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FileUpload__WEBPACK_IMPORTED_MODULE_4__.FileUpload, {\n                                onFilesProcessed: ()=>{},\n                                onImport: handleFileImport\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                    lineNumber: 461,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n                lineNumber: 460,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\JournalApp.tsx\",\n        lineNumber: 191,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/JournalApp.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Settings.tsx":
/*!*************************************!*\
  !*** ./src/components/Settings.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Settings: () => (/* binding */ Settings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Monitor_Moon_Palette_RotateCcw_Save_Settings_Sun_Type_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Monitor,Moon,Palette,RotateCcw,Save,Settings,Sun,Type,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Monitor_Moon_Palette_RotateCcw_Save_Settings_Sun_Type_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Monitor,Moon,Palette,RotateCcw,Save,Settings,Sun,Type,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Monitor_Moon_Palette_RotateCcw_Save_Settings_Sun_Type_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Monitor,Moon,Palette,RotateCcw,Save,Settings,Sun,Type,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Monitor_Moon_Palette_RotateCcw_Save_Settings_Sun_Type_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Monitor,Moon,Palette,RotateCcw,Save,Settings,Sun,Type,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Monitor_Moon_Palette_RotateCcw_Save_Settings_Sun_Type_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Monitor,Moon,Palette,RotateCcw,Save,Settings,Sun,Type,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Monitor_Moon_Palette_RotateCcw_Save_Settings_Sun_Type_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Monitor,Moon,Palette,RotateCcw,Save,Settings,Sun,Type,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Monitor_Moon_Palette_RotateCcw_Save_Settings_Sun_Type_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Monitor,Moon,Palette,RotateCcw,Save,Settings,Sun,Type,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Monitor_Moon_Palette_RotateCcw_Save_Settings_Sun_Type_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Monitor,Moon,Palette,RotateCcw,Save,Settings,Sun,Type,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Monitor_Moon_Palette_RotateCcw_Save_Settings_Sun_Type_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Monitor,Moon,Palette,RotateCcw,Save,Settings,Sun,Type,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Monitor_Moon_Palette_RotateCcw_Save_Settings_Sun_Type_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Monitor,Moon,Palette,RotateCcw,Save,Settings,Sun,Type,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _ThemeProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ThemeProvider */ \"(ssr)/./src/components/ThemeProvider.tsx\");\n/* harmony import */ var _lib_localStorage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/localStorage */ \"(ssr)/./src/lib/localStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ Settings auto */ \n\n\n\n\nfunction Settings({ isOpen, onClose }) {\n    const { theme, setTheme } = (0,_ThemeProvider__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasChanges, setHasChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const storage = (0,_lib_localStorage__WEBPACK_IMPORTED_MODULE_3__.getLocalStorage)();\n    // Load settings on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Settings.useEffect\": ()=>{\n            const currentSettings = storage.getSettings();\n            setSettings(currentSettings);\n        }\n    }[\"Settings.useEffect\"], [\n        storage\n    ]);\n    // Track changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Settings.useEffect\": ()=>{\n            if (settings) {\n                const currentSettings = storage.getSettings();\n                const changed = JSON.stringify(settings) !== JSON.stringify(currentSettings);\n                setHasChanges(changed);\n            }\n        }\n    }[\"Settings.useEffect\"], [\n        settings,\n        storage\n    ]);\n    const updateSetting = (key, value)=>{\n        if (settings) {\n            setSettings((prev)=>prev ? {\n                    ...prev,\n                    [key]: value\n                } : null);\n        }\n    };\n    const saveSettings = ()=>{\n        if (settings) {\n            storage.saveSettings(settings);\n            setHasChanges(false);\n        }\n    };\n    const resetSettings = ()=>{\n        const defaultSettings = storage.getSettings();\n        // Clear localStorage settings to get defaults\n        localStorage.removeItem('journal_settings');\n        const freshDefaults = storage.getSettings();\n        setSettings(freshDefaults);\n        setTheme(freshDefaults.theme);\n    };\n    const fontOptions = [\n        {\n            value: 'Inter, system-ui, sans-serif',\n            label: 'Inter (Default)'\n        },\n        {\n            value: 'Georgia, serif',\n            label: 'Georgia'\n        },\n        {\n            value: 'Times New Roman, serif',\n            label: 'Times New Roman'\n        },\n        {\n            value: 'Arial, sans-serif',\n            label: 'Arial'\n        },\n        {\n            value: 'Helvetica, sans-serif',\n            label: 'Helvetica'\n        },\n        {\n            value: 'Courier New, monospace',\n            label: 'Courier New'\n        },\n        {\n            value: 'Roboto, sans-serif',\n            label: 'Roboto'\n        },\n        {\n            value: 'Open Sans, sans-serif',\n            label: 'Open Sans'\n        }\n    ];\n    if (!isOpen || !settings) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Monitor_Moon_Palette_RotateCcw_Save_Settings_Sun_Type_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5 text-gray-600 dark:text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-gray-100\",\n                                    children: \"Settings\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                            children: \"\\xd7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Monitor_Moon_Palette_RotateCcw_Save_Settings_Sun_Type_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-5 h-5 text-gray-600 dark:text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                                            children: \"Appearance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                            children: \"Theme\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-3\",\n                                            children: [\n                                                {\n                                                    value: 'light',\n                                                    label: 'Light',\n                                                    icon: _barrel_optimize_names_CheckSquare_Monitor_Moon_Palette_RotateCcw_Save_Settings_Sun_Type_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                                                },\n                                                {\n                                                    value: 'dark',\n                                                    label: 'Dark',\n                                                    icon: _barrel_optimize_names_CheckSquare_Monitor_Moon_Palette_RotateCcw_Save_Settings_Sun_Type_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                                                },\n                                                {\n                                                    value: 'system',\n                                                    label: 'System',\n                                                    icon: _barrel_optimize_names_CheckSquare_Monitor_Moon_Palette_RotateCcw_Save_Settings_Sun_Type_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                                                }\n                                            ].map(({ value, label, icon: Icon })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setTheme(value);\n                                                        updateSetting('theme', value);\n                                                    },\n                                                    className: `\n                      flex flex-col items-center p-3 rounded-lg border-2 transition-colors\n                      ${theme === value ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'}\n                    `,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"w-5 h-5 mb-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: label\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, value, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Monitor_Moon_Palette_RotateCcw_Save_Settings_Sun_Type_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5 text-gray-600 dark:text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                                            children: \"Typography\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                    children: [\n                                                        \"Font Size: \",\n                                                        settings.fontSize,\n                                                        \"px\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    min: \"12\",\n                                                    max: \"24\",\n                                                    value: settings.fontSize,\n                                                    onChange: (e)=>updateSetting('fontSize', parseInt(e.target.value)),\n                                                    className: \"w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"12px\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"24px\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                    children: \"Font Family\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: settings.fontFamily,\n                                                    onChange: (e)=>updateSetting('fontFamily', e.target.value),\n                                                    className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\",\n                                                    children: fontOptions.map((font)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: font.value,\n                                                            children: font.label\n                                                        }, font.value, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border border-gray-200 dark:border-gray-600 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                            children: \"Preview:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                fontSize: `${settings.fontSize}px`,\n                                                fontFamily: settings.fontFamily\n                                            },\n                                            className: \"text-gray-900 dark:text-gray-100\",\n                                            children: \"The quick brown fox jumps over the lazy dog. This is how your journal text will appear.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Monitor_Moon_Palette_RotateCcw_Save_Settings_Sun_Type_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-5 h-5 text-gray-600 dark:text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                                            children: \"Auto-save\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                            children: \"Enable Auto-save\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                            children: \"Automatically save your work as you type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>updateSetting('autoSave', !settings.autoSave),\n                                                    className: `\n                    relative inline-flex h-6 w-11 items-center rounded-full transition-colors\n                    ${settings.autoSave ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'}\n                  `,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `\n                      inline-block h-4 w-4 transform rounded-full bg-white transition-transform\n                      ${settings.autoSave ? 'translate-x-6' : 'translate-x-1'}\n                    `\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, this),\n                                        settings.autoSave && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                    children: [\n                                                        \"Auto-save Interval: \",\n                                                        settings.autoSaveInterval / 1000,\n                                                        \"s\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    min: \"1000\",\n                                                    max: \"10000\",\n                                                    step: \"1000\",\n                                                    value: settings.autoSaveInterval,\n                                                    onChange: (e)=>updateSetting('autoSaveInterval', parseInt(e.target.value)),\n                                                    className: \"w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"1s\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"10s\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Monitor_Moon_Palette_RotateCcw_Save_Settings_Sun_Type_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-5 h-5 text-gray-600 dark:text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                                            children: \"Writing Assistance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                    children: \"Spell Check\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: \"Enable browser spell checking\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>updateSetting('spellCheck', !settings.spellCheck),\n                                            className: `\n                  relative inline-flex h-6 w-11 items-center rounded-full transition-colors\n                  ${settings.spellCheck ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'}\n                `,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `\n                    inline-block h-4 w-4 transform rounded-full bg-white transition-transform\n                    ${settings.spellCheck ? 'translate-x-6' : 'translate-x-1'}\n                  `\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: resetSettings,\n                            className: \"flex items-center space-x-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Monitor_Moon_Palette_RotateCcw_Save_Settings_Sun_Type_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Reset to Defaults\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        saveSettings();\n                                        onClose();\n                                    },\n                                    disabled: !hasChanges,\n                                    className: `\n                flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors\n                ${hasChanges ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'}\n              `,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Monitor_Moon_Palette_RotateCcw_Save_Settings_Sun_Type_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Save Changes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n            lineNumber: 85,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\Settings.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Settings.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TagManager.tsx":
/*!***************************************!*\
  !*** ./src/components/TagManager.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TagManager: () => (/* binding */ TagManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Plus_Search_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Search,Tag,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_Search_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Search,Tag,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_Search_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Search,Tag,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_Search_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Search,Tag,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _lib_localStorage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/localStorage */ \"(ssr)/./src/lib/localStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ TagManager auto */ \n\n\n\nfunction TagManager({ selectedTags, onTagsChange, onSearch, className = '' }) {\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tagInput, setTagInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [availableTags, setAvailableTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredTags, setFilteredTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreatingTag, setIsCreatingTag] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const suggestionsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const storage = (0,_lib_localStorage__WEBPACK_IMPORTED_MODULE_2__.getLocalStorage)();\n    // Load available tags from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TagManager.useEffect\": ()=>{\n            const cachedTags = storage.getCachedTags();\n            setAvailableTags(cachedTags);\n        }\n    }[\"TagManager.useEffect\"], [\n        storage\n    ]);\n    // Filter tags based on input\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TagManager.useEffect\": ()=>{\n            if (tagInput.trim()) {\n                const filtered = availableTags.filter({\n                    \"TagManager.useEffect.filtered\": (tag)=>tag.toLowerCase().includes(tagInput.toLowerCase()) && !selectedTags.includes(tag)\n                }[\"TagManager.useEffect.filtered\"]);\n                setFilteredTags(filtered);\n                setShowSuggestions(true);\n            } else {\n                setFilteredTags([]);\n                setShowSuggestions(false);\n            }\n        }\n    }[\"TagManager.useEffect\"], [\n        tagInput,\n        availableTags,\n        selectedTags\n    ]);\n    // Handle search\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TagManager.useEffect\": ()=>{\n            if (onSearch) {\n                const timeoutId = setTimeout({\n                    \"TagManager.useEffect.timeoutId\": ()=>{\n                        onSearch(searchQuery, selectedTags);\n                    }\n                }[\"TagManager.useEffect.timeoutId\"], 300); // Debounce search\n                return ({\n                    \"TagManager.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"TagManager.useEffect\"];\n            }\n        }\n    }[\"TagManager.useEffect\"], [\n        searchQuery,\n        selectedTags,\n        onSearch\n    ]);\n    // Close suggestions when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TagManager.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"TagManager.useEffect.handleClickOutside\": (event)=>{\n                    if (suggestionsRef.current && !suggestionsRef.current.contains(event.target) && inputRef.current && !inputRef.current.contains(event.target)) {\n                        setShowSuggestions(false);\n                    }\n                }\n            }[\"TagManager.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"TagManager.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"TagManager.useEffect\"];\n        }\n    }[\"TagManager.useEffect\"], []);\n    const addTag = (tagName)=>{\n        const trimmedTag = tagName.trim().toLowerCase();\n        if (trimmedTag && !selectedTags.includes(trimmedTag)) {\n            const newTags = [\n                ...selectedTags,\n                trimmedTag\n            ];\n            onTagsChange(newTags);\n            // Add to available tags if it's new\n            if (!availableTags.includes(trimmedTag)) {\n                const updatedTags = [\n                    ...availableTags,\n                    trimmedTag\n                ];\n                setAvailableTags(updatedTags);\n                storage.cacheTags(updatedTags);\n            }\n        }\n        setTagInput('');\n        setShowSuggestions(false);\n        setIsCreatingTag(false);\n    };\n    const removeTag = (tagToRemove)=>{\n        const newTags = selectedTags.filter((tag)=>tag !== tagToRemove);\n        onTagsChange(newTags);\n    };\n    const handleTagInputKeyDown = (e)=>{\n        if (e.key === 'Enter') {\n            e.preventDefault();\n            if (tagInput.trim()) {\n                if (filteredTags.length > 0) {\n                    addTag(filteredTags[0]);\n                } else {\n                    addTag(tagInput);\n                }\n            }\n        } else if (e.key === 'Escape') {\n            setShowSuggestions(false);\n            setIsCreatingTag(false);\n        } else if (e.key === 'Backspace' && !tagInput && selectedTags.length > 0) {\n            removeTag(selectedTags[selectedTags.length - 1]);\n        } else if (e.key === 'ArrowDown' && filteredTags.length > 0) {\n            e.preventDefault();\n        // Focus first suggestion (could be enhanced with keyboard navigation)\n        }\n    };\n    const handleSearchKeyDown = (e)=>{\n        if (e.key === 'Enter') {\n            e.preventDefault();\n            if (onSearch) {\n                onSearch(searchQuery, selectedTags);\n            }\n        }\n    };\n    const clearAllTags = ()=>{\n        onTagsChange([]);\n    };\n    const clearSearch = ()=>{\n        setSearchQuery('');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `space-y-4 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Search_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: searchQuery,\n                            onChange: (e)=>setSearchQuery(e.target.value),\n                            onKeyDown: handleSearchKeyDown,\n                            placeholder: \"Search entries...\",\n                            className: \"w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: clearSearch,\n                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Search_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            selectedTags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                children: \"Active Filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: clearAllTags,\n                                className: \"text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300\",\n                                children: \"Clear all\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2\",\n                        children: selectedTags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex items-center px-3 py-1 text-sm bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Search_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-3 h-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 17\n                                    }, this),\n                                    tag,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>removeTag(tag),\n                                        className: \"ml-2 text-blue-600 dark:text-blue-300 hover:text-blue-800 dark:hover:text-blue-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Search_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, tag, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                        children: \"Add Tags\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Search_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        ref: inputRef,\n                                        type: \"text\",\n                                        value: tagInput,\n                                        onChange: (e)=>setTagInput(e.target.value),\n                                        onKeyDown: handleTagInputKeyDown,\n                                        onFocus: ()=>setShowSuggestions(true),\n                                        placeholder: \"Type to add or search tags...\",\n                                        className: \"w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this),\n                                    tagInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>addTag(tagInput),\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-500 hover:text-blue-700 dark:hover:text-blue-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Search_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this),\n                            showSuggestions && (filteredTags.length > 0 || tagInput.trim()) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: suggestionsRef,\n                                className: \"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-48 overflow-y-auto\",\n                                children: [\n                                    filteredTags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>addTag(tag),\n                                            className: \"w-full px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Search_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-3 h-3 mr-2 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this),\n                                                tag\n                                            ]\n                                        }, tag, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this)),\n                                    tagInput.trim() && !availableTags.includes(tagInput.toLowerCase()) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>addTag(tagInput),\n                                        className: \"w-full px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100 flex items-center border-t border-gray-200 dark:border-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Search_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-3 h-3 mr-2 text-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 19\n                                            }, this),\n                                            'Create \"',\n                                            tagInput.toLowerCase(),\n                                            '\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            availableTags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                        children: \"Available Tags\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2 max-h-32 overflow-y-auto\",\n                        children: availableTags.filter((tag)=>!selectedTags.includes(tag)).slice(0, 20) // Limit display for performance\n                        .map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>addTag(tag),\n                                className: \"inline-flex items-center px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Search_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-3 h-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 19\n                                    }, this),\n                                    tag\n                                ]\n                            }, tag, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n                lineNumber: 261,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TagManager.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TagManager.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TextEditor.tsx":
/*!***************************************!*\
  !*** ./src/components/TextEditor.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TextEditor: () => (/* binding */ TextEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_localStorage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/localStorage */ \"(ssr)/./src/lib/localStorage.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clock_Save_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clock,Save!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clock_Save_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clock,Save!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clock_Save_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clock,Save!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ TextEditor auto */ \n\n\n\nfunction TextEditor({ entryId, initialTitle = '', initialContent = '', initialTags = [], onSave, onAutoSave, className = '' }) {\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTitle);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialContent);\n    const [tags, setTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTags);\n    const [tagInput, setTagInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [saveStatus, setSaveStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('saved');\n    const [wordCount, setWordCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [charCount, setCharCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"TextEditor.useState\": ()=>{\n            const storage = (0,_lib_localStorage__WEBPACK_IMPORTED_MODULE_2__.getLocalStorage)();\n            return storage.getSettings();\n        }\n    }[\"TextEditor.useState\"]);\n    const autoSaveTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const contentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const storage = (0,_lib_localStorage__WEBPACK_IMPORTED_MODULE_2__.getLocalStorage)();\n    // Reset state when initial values change (new entry selected)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TextEditor.useEffect\": ()=>{\n            setTitle(initialTitle);\n            setContent(initialContent);\n            setTags(initialTags);\n            setTagInput('');\n            setSaveStatus('saved');\n        }\n    }[\"TextEditor.useEffect\"], [\n        initialTitle,\n        initialContent,\n        initialTags\n    ]);\n    // Calculate word and character counts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TextEditor.useEffect\": ()=>{\n            const words = content.trim().split(/\\s+/).filter({\n                \"TextEditor.useEffect\": (word)=>word.length > 0\n            }[\"TextEditor.useEffect\"]).length;\n            setWordCount(content.trim() === '' ? 0 : words);\n            setCharCount(content.length);\n        }\n    }[\"TextEditor.useEffect\"], [\n        content\n    ]);\n    // Auto-save functionality\n    const performAutoSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TextEditor.useCallback[performAutoSave]\": ()=>{\n            if (!settings.autoSave) return;\n            const draftId = entryId || `draft_${Date.now()}`;\n            const draft = {\n                id: draftId,\n                title,\n                content,\n                tags,\n                lastModified: new Date().toISOString()\n            };\n            try {\n                storage.saveDraft(draft);\n                setSaveStatus('saved');\n                if (onAutoSave) {\n                    onAutoSave({\n                        title,\n                        content,\n                        tags\n                    });\n                }\n            } catch (error) {\n                console.error('Auto-save failed:', error);\n                setSaveStatus('error');\n            }\n        }\n    }[\"TextEditor.useCallback[performAutoSave]\"], [\n        title,\n        content,\n        tags,\n        entryId,\n        settings.autoSave,\n        onAutoSave\n    ]); // Remove storage from dependencies\n    // Debounced auto-save\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TextEditor.useEffect\": ()=>{\n            if (title || content) {\n                setSaveStatus('unsaved');\n                if (autoSaveTimeoutRef.current) {\n                    clearTimeout(autoSaveTimeoutRef.current);\n                }\n                autoSaveTimeoutRef.current = setTimeout({\n                    \"TextEditor.useEffect\": ()=>{\n                        setSaveStatus('saving');\n                        performAutoSave();\n                    }\n                }[\"TextEditor.useEffect\"], settings.autoSaveInterval);\n            }\n            return ({\n                \"TextEditor.useEffect\": ()=>{\n                    if (autoSaveTimeoutRef.current) {\n                        clearTimeout(autoSaveTimeoutRef.current);\n                    }\n                }\n            })[\"TextEditor.useEffect\"];\n        }\n    }[\"TextEditor.useEffect\"], [\n        title,\n        content,\n        tags,\n        settings.autoSaveInterval\n    ]);\n    // Manual save\n    const handleManualSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TextEditor.useCallback[handleManualSave]\": ()=>{\n            if (onSave) {\n                onSave({\n                    title,\n                    content,\n                    tags\n                });\n                setSaveStatus('saved');\n            } else {\n                performAutoSave();\n            }\n        }\n    }[\"TextEditor.useCallback[handleManualSave]\"], [\n        title,\n        content,\n        tags,\n        onSave,\n        performAutoSave\n    ]);\n    // Tag management\n    const addTag = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TextEditor.useCallback[addTag]\": (tagName)=>{\n            const trimmedTag = tagName.trim().toLowerCase();\n            if (trimmedTag && !tags.includes(trimmedTag)) {\n                setTags({\n                    \"TextEditor.useCallback[addTag]\": (prev)=>[\n                            ...prev,\n                            trimmedTag\n                        ]\n                }[\"TextEditor.useCallback[addTag]\"]);\n            }\n        }\n    }[\"TextEditor.useCallback[addTag]\"], [\n        tags\n    ]);\n    const removeTag = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TextEditor.useCallback[removeTag]\": (tagToRemove)=>{\n            setTags({\n                \"TextEditor.useCallback[removeTag]\": (prev)=>prev.filter({\n                        \"TextEditor.useCallback[removeTag]\": (tag)=>tag !== tagToRemove\n                    }[\"TextEditor.useCallback[removeTag]\"])\n            }[\"TextEditor.useCallback[removeTag]\"]);\n        }\n    }[\"TextEditor.useCallback[removeTag]\"], []);\n    const handleTagInputKeyDown = (e)=>{\n        if (e.key === 'Enter' || e.key === ',') {\n            e.preventDefault();\n            if (tagInput.trim()) {\n                addTag(tagInput);\n                setTagInput('');\n            }\n        } else if (e.key === 'Backspace' && !tagInput && tags.length > 0) {\n            removeTag(tags[tags.length - 1]);\n        }\n    };\n    // Keyboard shortcuts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TextEditor.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"TextEditor.useEffect.handleKeyDown\": (e)=>{\n                    if ((e.ctrlKey || e.metaKey) && e.key === 's') {\n                        e.preventDefault();\n                        handleManualSave();\n                    }\n                }\n            }[\"TextEditor.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"TextEditor.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"TextEditor.useEffect\"];\n        }\n    }[\"TextEditor.useEffect\"], [\n        handleManualSave\n    ]);\n    // Auto-resize textarea\n    const adjustTextareaHeight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TextEditor.useCallback[adjustTextareaHeight]\": ()=>{\n            const textarea = contentRef.current;\n            if (textarea) {\n                textarea.style.height = 'auto';\n                textarea.style.height = `${textarea.scrollHeight}px`;\n            }\n        }\n    }[\"TextEditor.useCallback[adjustTextareaHeight]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TextEditor.useEffect\": ()=>{\n            adjustTextareaHeight();\n        }\n    }[\"TextEditor.useEffect\"], [\n        content,\n        adjustTextareaHeight\n    ]);\n    // Save status indicator\n    const SaveStatusIndicator = ()=>{\n        switch(saveStatus){\n            case 'saving':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center text-blue-600 dark:text-blue-400\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Save_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-4 h-4 mr-1 animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm\",\n                            children: \"Saving...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 11\n                }, this);\n            case 'saved':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center text-green-600 dark:text-green-400\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Save_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-4 h-4 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm\",\n                            children: \"Saved\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 11\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center text-red-600 dark:text-red-400\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Save_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-4 h-4 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm\",\n                            children: \"Save failed\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 11\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center text-gray-500 dark:text-gray-400\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm\",\n                        children: \"Unsaved changes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex flex-col h-full ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row sm:items-center justify-between p-3 md:p-4 border-b border-gray-200 dark:border-gray-700 space-y-2 sm:space-y-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        value: title,\n                        onChange: (e)=>setTitle(e.target.value),\n                        placeholder: \"Enter title...\",\n                        className: \"flex-1 text-lg sm:text-2xl font-bold bg-transparent border-none outline-none text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400\",\n                        style: {\n                            fontSize: `${Math.max(16, settings.fontSize + 2)}px`\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between sm:justify-end space-x-2 sm:space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SaveStatusIndicator, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleManualSave,\n                                className: \"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\",\n                                children: \"Save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3 md:p-4 border-b border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap items-center gap-2\",\n                    children: [\n                        tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex items-center px-2 py-1 text-xs sm:text-sm bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full\",\n                                children: [\n                                    \"#\",\n                                    tag,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>removeTag(tag),\n                                        className: \"ml-1 text-blue-600 dark:text-blue-300 hover:text-blue-800 dark:hover:text-blue-100\",\n                                        children: \"\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, tag, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: tagInput,\n                            onChange: (e)=>setTagInput(e.target.value),\n                            onKeyDown: handleTagInputKeyDown,\n                            placeholder: \"Add tags...\",\n                            className: \"flex-1 min-w-[120px] px-2 py-1 text-sm bg-transparent border border-gray-300 dark:border-gray-600 rounded outline-none focus:border-blue-500 dark:focus:border-blue-400 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 p-3 md:p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    ref: contentRef,\n                    value: content,\n                    onChange: (e)=>setContent(e.target.value),\n                    placeholder: \"Start writing your journal entry...\",\n                    className: \"w-full h-full min-h-[400px] bg-transparent border-none outline-none resize-none text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 leading-relaxed\",\n                    style: {\n                        fontSize: `${settings.fontSize}px`,\n                        fontFamily: settings.fontFamily\n                    },\n                    spellCheck: settings.spellCheck\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row sm:items-center justify-between p-3 md:p-4 border-t border-gray-200 dark:border-gray-700 text-sm text-gray-500 dark:text-gray-400 space-y-1 sm:space-y-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3 sm:space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs sm:text-sm\",\n                                children: [\n                                    wordCount,\n                                    \" words\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs sm:text-sm\",\n                                children: [\n                                    charCount,\n                                    \" characters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs hidden sm:block\",\n                        children: \"Press Ctrl+S (Cmd+S) to save manually\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\TextEditor.tsx\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TextEditor.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_localStorage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/localStorage */ \"(ssr)/./src/lib/localStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ useTheme,ThemeProvider auto */ \n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (!context) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n}\nfunction ThemeProvider({ children }) {\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('system');\n    const [resolvedTheme, setResolvedTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load theme from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const storage = (0,_lib_localStorage__WEBPACK_IMPORTED_MODULE_2__.getLocalStorage)();\n            const settings = storage.getSettings();\n            setThemeState(settings.theme);\n            setMounted(true);\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // Update resolved theme when theme changes or system preference changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const updateResolvedTheme = {\n                \"ThemeProvider.useEffect.updateResolvedTheme\": ()=>{\n                    if (theme === 'system') {\n                        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n                        setResolvedTheme(systemTheme);\n                    } else {\n                        setResolvedTheme(theme);\n                    }\n                }\n            }[\"ThemeProvider.useEffect.updateResolvedTheme\"];\n            updateResolvedTheme();\n            // Listen for system theme changes\n            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            const handleChange = {\n                \"ThemeProvider.useEffect.handleChange\": ()=>{\n                    if (theme === 'system') {\n                        updateResolvedTheme();\n                    }\n                }\n            }[\"ThemeProvider.useEffect.handleChange\"];\n            mediaQuery.addEventListener('change', handleChange);\n            return ({\n                \"ThemeProvider.useEffect\": ()=>mediaQuery.removeEventListener('change', handleChange)\n            })[\"ThemeProvider.useEffect\"];\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme\n    ]);\n    // Apply theme to document\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            const root = document.documentElement;\n            // Remove existing theme classes\n            root.classList.remove('light', 'dark');\n            // Add current theme class\n            root.classList.add(resolvedTheme);\n            // Update meta theme-color for mobile browsers\n            const metaThemeColor = document.querySelector('meta[name=\"theme-color\"]');\n            if (metaThemeColor) {\n                metaThemeColor.setAttribute('content', resolvedTheme === 'dark' ? '#1f2937' : '#ffffff');\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        resolvedTheme,\n        mounted\n    ]);\n    const setTheme = (newTheme)=>{\n        setThemeState(newTheme);\n        // Save to localStorage\n        const storage = (0,_lib_localStorage__WEBPACK_IMPORTED_MODULE_2__.getLocalStorage)();\n        storage.saveSettings({\n            theme: newTheme\n        });\n    };\n    // Prevent hydration mismatch by not rendering until mounted\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-white\",\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\ThemeProvider.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            setTheme,\n            resolvedTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AllJournal\\\\journal-app\\\\src\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/localStorage.ts":
/*!*********************************!*\
  !*** ./src/lib/localStorage.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getLocalStorage: () => (/* binding */ getLocalStorage)\n/* harmony export */ });\nconst STORAGE_KEYS = {\n    ENTRIES: 'journal_entries',\n    DRAFTS: 'journal_drafts',\n    SETTINGS: 'journal_settings',\n    TAGS: 'journal_tags'\n};\nclass LocalStorageManager {\n    // Settings management\n    getSettings() {\n        if (true) {\n            return this.getDefaultSettings();\n        }\n        try {\n            const stored = localStorage.getItem(STORAGE_KEYS.SETTINGS);\n            if (stored) {\n                return {\n                    ...this.getDefaultSettings(),\n                    ...JSON.parse(stored)\n                };\n            }\n        } catch (error) {\n            console.error('Error loading settings from localStorage:', error);\n        }\n        return this.getDefaultSettings();\n    }\n    saveSettings(settings) {\n        if (true) return;\n        try {\n            const current = this.getSettings();\n            const updated = {\n                ...current,\n                ...settings\n            };\n            localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(updated));\n        } catch (error) {\n            console.error('Error saving settings to localStorage:', error);\n        }\n    }\n    getDefaultSettings() {\n        return {\n            theme: 'system',\n            fontSize: 16,\n            fontFamily: 'Inter, system-ui, sans-serif',\n            autoSave: true,\n            autoSaveInterval: 2000,\n            spellCheck: true\n        };\n    }\n    // Draft management (for unsaved entries)\n    saveDraft(draft) {\n        if (true) return;\n        try {\n            const drafts = this.getDrafts();\n            const existingIndex = drafts.findIndex((d)=>d.id === draft.id);\n            if (existingIndex >= 0) {\n                drafts[existingIndex] = draft;\n            } else {\n                drafts.push(draft);\n            }\n            localStorage.setItem(STORAGE_KEYS.DRAFTS, JSON.stringify(drafts));\n        } catch (error) {\n            console.error('Error saving draft to localStorage:', error);\n        }\n    }\n    getDrafts() {\n        if (true) return [];\n        try {\n            const stored = localStorage.getItem(STORAGE_KEYS.DRAFTS);\n            return stored ? JSON.parse(stored) : [];\n        } catch (error) {\n            console.error('Error loading drafts from localStorage:', error);\n            return [];\n        }\n    }\n    getDraft(id) {\n        const drafts = this.getDrafts();\n        return drafts.find((d)=>d.id === id) || null;\n    }\n    deleteDraft(id) {\n        try {\n            const drafts = this.getDrafts().filter((d)=>d.id !== id);\n            localStorage.setItem(STORAGE_KEYS.DRAFTS, JSON.stringify(drafts));\n        } catch (error) {\n            console.error('Error deleting draft from localStorage:', error);\n        }\n    }\n    // Entry caching (for offline access)\n    cacheEntries(entries) {\n        try {\n            const cached = {\n                entries,\n                lastUpdated: new Date().toISOString()\n            };\n            localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(cached));\n        } catch (error) {\n            console.error('Error caching entries to localStorage:', error);\n        }\n    }\n    getCachedEntries() {\n        try {\n            const stored = localStorage.getItem(STORAGE_KEYS.ENTRIES);\n            return stored ? JSON.parse(stored) : null;\n        } catch (error) {\n            console.error('Error loading cached entries from localStorage:', error);\n            return null;\n        }\n    }\n    cacheEntry(entry) {\n        try {\n            const cached = this.getCachedEntries();\n            if (cached) {\n                const existingIndex = cached.entries.findIndex((e)=>e.id === entry.id);\n                if (existingIndex >= 0) {\n                    cached.entries[existingIndex] = entry;\n                } else {\n                    cached.entries.unshift(entry);\n                }\n                cached.lastUpdated = new Date().toISOString();\n                localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(cached));\n            } else {\n                this.cacheEntries([\n                    entry\n                ]);\n            }\n        } catch (error) {\n            console.error('Error caching entry to localStorage:', error);\n        }\n    }\n    removeCachedEntry(id) {\n        try {\n            const cached = this.getCachedEntries();\n            if (cached) {\n                cached.entries = cached.entries.filter((e)=>e.id !== id);\n                cached.lastUpdated = new Date().toISOString();\n                localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(cached));\n            }\n        } catch (error) {\n            console.error('Error removing cached entry from localStorage:', error);\n        }\n    }\n    // Tag caching\n    cacheTags(tags) {\n        try {\n            const cached = {\n                tags,\n                lastUpdated: new Date().toISOString()\n            };\n            localStorage.setItem(STORAGE_KEYS.TAGS, JSON.stringify(cached));\n        } catch (error) {\n            console.error('Error caching tags to localStorage:', error);\n        }\n    }\n    getCachedTags() {\n        try {\n            const stored = localStorage.getItem(STORAGE_KEYS.TAGS);\n            const cached = stored ? JSON.parse(stored) : null;\n            return cached ? cached.tags : [];\n        } catch (error) {\n            console.error('Error loading cached tags from localStorage:', error);\n            return [];\n        }\n    }\n    // Utility methods\n    clearAllData() {\n        try {\n            Object.values(STORAGE_KEYS).forEach((key)=>{\n                localStorage.removeItem(key);\n            });\n        } catch (error) {\n            console.error('Error clearing localStorage:', error);\n        }\n    }\n    getStorageUsage() {\n        try {\n            let used = 0;\n            for(let i = 0; i < localStorage.length; i++){\n                const key = localStorage.key(i);\n                if (key) {\n                    used += localStorage.getItem(key)?.length || 0;\n                }\n            }\n            // Estimate available space (most browsers have ~5-10MB limit)\n            const available = 5 * 1024 * 1024 - used; // Assume 5MB limit\n            return {\n                used,\n                available\n            };\n        } catch (error) {\n            console.error('Error calculating storage usage:', error);\n            return {\n                used: 0,\n                available: 0\n            };\n        }\n    }\n    // Auto-save functionality\n    createAutoSaveTimer(callback, interval) {\n        const settings = this.getSettings();\n        const saveInterval = interval || settings.autoSaveInterval;\n        return setInterval(callback, saveInterval);\n    }\n    // Export/Import functionality\n    exportData() {\n        try {\n            const data = {\n                entries: this.getCachedEntries(),\n                drafts: this.getDrafts(),\n                settings: this.getSettings(),\n                tags: this.getCachedTags(),\n                exportDate: new Date().toISOString()\n            };\n            return JSON.stringify(data, null, 2);\n        } catch (error) {\n            console.error('Error exporting data:', error);\n            throw new Error('Failed to export data');\n        }\n    }\n    importData(jsonData) {\n        try {\n            const data = JSON.parse(jsonData);\n            if (data.entries) {\n                localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(data.entries));\n            }\n            if (data.drafts) {\n                localStorage.setItem(STORAGE_KEYS.DRAFTS, JSON.stringify(data.drafts));\n            }\n            if (data.settings) {\n                localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(data.settings));\n            }\n            if (data.tags) {\n                localStorage.setItem(STORAGE_KEYS.TAGS, JSON.stringify(data.tags));\n            }\n        } catch (error) {\n            console.error('Error importing data:', error);\n            throw new Error('Failed to import data');\n        }\n    }\n}\n// Singleton instance\nlet storageInstance = null;\nfunction getLocalStorage() {\n    if (!storageInstance) {\n        storageInstance = new LocalStorageManager();\n    }\n    return storageInstance;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LocalStorageManager);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/localStorage.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/testData.ts":
/*!*****************************!*\
  !*** ./src/lib/testData.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadSampleData: () => (/* binding */ loadSampleData),\n/* harmony export */   sampleEntries: () => (/* binding */ sampleEntries)\n/* harmony export */ });\nconst sampleEntries = [\n    {\n        id: 'sample-1',\n        title: 'My First Journal Entry',\n        content: 'Today was a great day! I learned about React and TypeScript. The weather was beautiful and I went for a walk in the park.',\n        tags: [\n            'personal',\n            'learning',\n            'react',\n            'typescript'\n        ],\n        created_at: '2024-01-15T10:00:00.000Z',\n        updated_at: '2024-01-15T10:30:00.000Z',\n        word_count: 25\n    },\n    {\n        id: 'sample-2',\n        title: 'Work Progress Update',\n        content: 'Made significant progress on the journal app today. Implemented the tagging system and search functionality. Need to test the mobile responsiveness next.',\n        tags: [\n            'work',\n            'programming',\n            'journal-app',\n            'mobile'\n        ],\n        created_at: '2024-01-16T14:00:00.000Z',\n        updated_at: '2024-01-16T14:45:00.000Z',\n        word_count: 28\n    },\n    {\n        id: 'sample-3',\n        title: 'Weekend Plans',\n        content: 'Planning to visit the museum this weekend. Also want to try that new restaurant downtown. Should be a relaxing weekend after a busy week.',\n        tags: [\n            'weekend',\n            'plans',\n            'museum',\n            'restaurant'\n        ],\n        created_at: '2024-01-17T09:00:00.000Z',\n        updated_at: '2024-01-17T09:15:00.000Z',\n        word_count: 26\n    }\n];\nfunction loadSampleData() {\n    if (false) {}\n    return [];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/testData.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/underscore","vendor-chunks/mammoth","vendor-chunks/bluebird","vendor-chunks/jszip","vendor-chunks/lucide-react","vendor-chunks/xmlbuilder","vendor-chunks/pako","vendor-chunks/lop","vendor-chunks/readable-stream","vendor-chunks/@swc","vendor-chunks/@xmldom","vendor-chunks/prop-types","vendor-chunks/file-selector","vendor-chunks/react-dropzone","vendor-chunks/react-is","vendor-chunks/inherits","vendor-chunks/dingbat-to-unicode","vendor-chunks/tslib","vendor-chunks/util-deprecate","vendor-chunks/string_decoder","vendor-chunks/safe-buffer","vendor-chunks/process-nextick-args","vendor-chunks/path-is-absolute","vendor-chunks/option","vendor-chunks/object-assign","vendor-chunks/lie","vendor-chunks/immediate","vendor-chunks/core-util-is","vendor-chunks/base64-js","vendor-chunks/attr-accept"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cxyien%5COneDrive%5CDesktop%5CAllJournal%5Cjournal-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cxyien%5COneDrive%5CDesktop%5CAllJournal%5Cjournal-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();